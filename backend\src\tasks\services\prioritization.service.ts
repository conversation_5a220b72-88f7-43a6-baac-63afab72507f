import { Injectable } from '@nestjs/common';
import {
  PriorityWeights,
  TaskPriorityData,
  PrioritizationSettings,
  DEFAULT_PRIORITIZATION_SETTINGS,
} from '../interfaces/prioritization.interface';

@Injectable()
export class PrioritizationService {
  /**
   * Calculate priority score for a task based on configurable weights
   */
  calculatePriorityScore(
    task: TaskPriorityData,
    settings: PrioritizationSettings = DEFAULT_PRIORITIZATION_SETTINGS,
  ): number {
    const { weights } = settings;

    const dueDateScore = this.calculateDueDateScore(task.dueDate, settings);
    const effortScore = this.calculateEffortScore(task.estimatedMinutes, settings);
    const impactScore = this.calculateBusinessImpactScore(task.priority, task.tags, settings);
    const contextScore = this.calculateContextSwitchingScore(
      task.projectName,
      task.assigneeId,
      settings,
    );

    const totalScore =
      dueDateScore * weights.dueDateProximity +
      effortScore * weights.effortEstimate +
      impactScore * weights.businessImpact +
      contextScore * weights.contextSwitching;

    // Scale to max score and ensure it's within bounds
    return Math.min(Math.max(totalScore * settings.maxScore, 0), settings.maxScore);
  }

  /**
   * Calculate due date proximity score (higher score for closer due dates)
   */
  private calculateDueDateScore(
    dueDate: Date | undefined,
    settings: PrioritizationSettings,
  ): number {
    if (!dueDate) {
      return 0.1; // Low priority for tasks without due dates
    }

    const now = new Date();
    const timeDiff = dueDate.getTime() - now.getTime();
    const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

    // Overdue tasks get maximum urgency
    if (daysDiff < 0) {
      return 1.0;
    }

    // Tasks due within urgency window get scaled score
    if (daysDiff <= settings.dueDateUrgencyDays) {
      return 1.0 - daysDiff / settings.dueDateUrgencyDays;
    }

    // Tasks due far in the future get low score
    return Math.max(0.1, 1.0 - daysDiff / (settings.dueDateUrgencyDays * 4));
  }

  /**
   * Calculate effort estimate score (higher score for smaller tasks)
   */
  private calculateEffortScore(
    estimatedMinutes: number | undefined,
    settings: PrioritizationSettings,
  ): number {
    if (!estimatedMinutes) {
      return 0.5; // Medium score for unknown effort
    }

    const { effortThresholds } = settings;

    // Quick tasks get high score (easy wins)
    if (estimatedMinutes <= effortThresholds.quick) {
      return 1.0;
    }

    // Medium tasks get medium-high score
    if (estimatedMinutes <= effortThresholds.medium) {
      return 0.7;
    }

    // Large tasks get medium score
    if (estimatedMinutes <= effortThresholds.large) {
      return 0.4;
    }

    // Very large tasks get low score
    return 0.2;
  }

  /**
   * Calculate business impact score based on priority and tags
   */
  private calculateBusinessImpactScore(
    priority: string | undefined,
    tags: string[],
    settings: PrioritizationSettings,
  ): number {
    let score = 0.5; // Default medium impact

    // Score based on explicit priority
    switch (priority?.toLowerCase()) {
      case 'critical':
      case 'urgent':
        score = 1.0;
        break;
      case 'high':
        score = 0.8;
        break;
      case 'medium':
        score = 0.5;
        break;
      case 'low':
        score = 0.2;
        break;
    }

    // Boost score if task has high-impact tags
    const hasHighImpactTag = tags.some(tag =>
      settings.businessImpactTags.includes(tag.toLowerCase()),
    );

    if (hasHighImpactTag) {
      score = Math.min(1.0, score + 0.3);
    }

    return score;
  }

  /**
   * Calculate context switching score (higher score for tasks that reduce context switching)
   */
  private calculateContextSwitchingScore(
    projectName: string | undefined,
    assigneeId: string | undefined,
    settings: PrioritizationSettings,
  ): number {
    // This is a simplified implementation
    // In a real system, you might track the user's current context
    // and give higher scores to tasks in the same project/context
    
    // For now, we'll give a base score and apply penalties for context switching
    let score = 0.5;

    // Tasks without project context get penalty
    if (!projectName) {
      score -= settings.contextSwitchingPenalty;
    }

    // Tasks without assignee get slight penalty
    if (!assigneeId) {
      score -= settings.contextSwitchingPenalty * 0.5;
    }

    return Math.max(0, Math.min(1.0, score));
  }

  /**
   * Validate priority weights to ensure they sum to 1.0
   */
  validateWeights(weights: PriorityWeights): boolean {
    const sum = weights.dueDateProximity + weights.effortEstimate + 
                weights.businessImpact + weights.contextSwitching;
    
    // Allow small floating point errors
    return Math.abs(sum - 1.0) < 0.001;
  }

  /**
   * Normalize weights to sum to 1.0
   */
  normalizeWeights(weights: PriorityWeights): PriorityWeights {
    const sum = weights.dueDateProximity + weights.effortEstimate + 
                weights.businessImpact + weights.contextSwitching;
    
    if (sum === 0) {
      // Return equal weights if all are zero
      return {
        dueDateProximity: 0.25,
        effortEstimate: 0.25,
        businessImpact: 0.25,
        contextSwitching: 0.25,
      };
    }

    return {
      dueDateProximity: weights.dueDateProximity / sum,
      effortEstimate: weights.effortEstimate / sum,
      businessImpact: weights.businessImpact / sum,
      contextSwitching: weights.contextSwitching / sum,
    };
  }

  /**
   * Get default prioritization settings
   */
  getDefaultSettings(): PrioritizationSettings {
    return { ...DEFAULT_PRIORITIZATION_SETTINGS };
  }
}