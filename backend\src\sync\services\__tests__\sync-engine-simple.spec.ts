import { Test, TestingModule } from '@nestjs/testing';
import { getQueueToken } from '@nestjs/bull';
import { SYNC_QUEUE, SyncJobType, SyncJobPriority } from '../../constants/sync.constants';

// Simple test to verify the sync engine can be imported and instantiated
describe('SyncEngineService - Simple Test', () => {
  let mockQueue: any;
  let mockPrisma: any;
  let mockIntegrationsService: any;

  beforeEach(async () => {
    mockQueue = {
      add: jest.fn(),
      getJob: jest.fn(),
      getJobs: jest.fn(),
      clean: jest.fn(),
    };

    mockPrisma = {
      integration: {
        findUnique: jest.fn(),
        update: jest.fn(),
      },
      syncLog: {
        findMany: jest.fn(),
        deleteMany: jest.fn(),
      },
    };

    mockIntegrationsService = {
      getAdapter: jest.fn(),
    };
  });

  it('should be defined', () => {
    expect(SYNC_QUEUE).toBeDefined();
    expect(SyncJobType).toBeDefined();
    expect(SyncJobPriority).toBeDefined();
  });

  it('should have correct constants', () => {
    expect(SYNC_QUEUE).toBe('sync-queue');
    expect(SyncJobType.FULL_SYNC).toBe('full-sync');
    expect(SyncJobType.INCREMENTAL_SYNC).toBe('incremental-sync');
    expect(SyncJobPriority.NORMAL).toBe(5);
    expect(SyncJobPriority.HIGH).toBe(10);
  });
});