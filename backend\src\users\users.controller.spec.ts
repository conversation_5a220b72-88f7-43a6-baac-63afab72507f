import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';

describe('UsersController', () => {
  let controller: UsersController;
  let usersService: jest.Mocked<UsersService>;

  const mockUser: User = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    email: '<EMAIL>',
    name: 'Test User',
    avatarUrl: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockUsersService = {
      create: jest.fn(),
      findAll: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        { provide: UsersService, useValue: mockUsersService },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    usersService = module.get(UsersService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new user', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        name: 'Test User',
      };
      usersService.create.mockResolvedValue(mockUser);

      // Act
      const result = await controller.create(createUserDto);

      // Assert
      expect(usersService.create).toHaveBeenCalledWith(createUserDto);
      expect(result).toBe(mockUser);
    });
  });

  describe('findAll', () => {
    it('should return all users', async () => {
      // Arrange
      const users = [mockUser];
      usersService.findAll.mockResolvedValue(users);

      // Act
      const result = await controller.findAll();

      // Assert
      expect(usersService.findAll).toHaveBeenCalled();
      expect(result).toBe(users);
    });
  });

  describe('getProfile', () => {
    it('should return current user profile', async () => {
      // Act
      const result = await controller.getProfile(mockUser);

      // Assert
      expect(result).toBe(mockUser);
    });
  });

  describe('findOne', () => {
    it('should return user by ID', async () => {
      // Arrange
      usersService.findById.mockResolvedValue(mockUser);

      // Act
      const result = await controller.findOne(mockUser.id);

      // Assert
      expect(usersService.findById).toHaveBeenCalledWith(mockUser.id);
      expect(result).toBe(mockUser);
    });

    it('should throw error if user not found', async () => {
      // Arrange
      usersService.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(controller.findOne('non-existent-id')).rejects.toThrow('User not found');
    });
  });

  describe('updateProfile', () => {
    it('should update current user profile', async () => {
      // Arrange
      const updateUserDto: UpdateUserDto = {
        name: 'Updated Name',
      };
      const updatedUser = { ...mockUser, name: updateUserDto.name };
      usersService.update.mockResolvedValue(updatedUser);

      // Act
      const result = await controller.updateProfile(mockUser, updateUserDto);

      // Assert
      expect(usersService.update).toHaveBeenCalledWith(mockUser.id, updateUserDto);
      expect(result).toBe(updatedUser);
    });
  });

  describe('update', () => {
    it('should update user by ID', async () => {
      // Arrange
      const updateUserDto: UpdateUserDto = {
        name: 'Updated Name',
      };
      const updatedUser = { ...mockUser, name: updateUserDto.name };
      usersService.update.mockResolvedValue(updatedUser);

      // Act
      const result = await controller.update(mockUser.id, updateUserDto);

      // Assert
      expect(usersService.update).toHaveBeenCalledWith(mockUser.id, updateUserDto);
      expect(result).toBe(updatedUser);
    });
  });

  describe('remove', () => {
    it('should delete user by ID', async () => {
      // Arrange
      usersService.remove.mockResolvedValue(undefined);

      // Act
      const result = await controller.remove(mockUser.id);

      // Assert
      expect(usersService.remove).toHaveBeenCalledWith(mockUser.id);
      expect(result).toEqual({ message: 'User successfully deleted' });
    });
  });
});