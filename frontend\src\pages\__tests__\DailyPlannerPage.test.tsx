import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { format, addDays, subDays } from 'date-fns'

import { DailyPlannerPage } from '../DailyPlannerPage'
import { useWorkspaceStore } from '@/store/workspace'

// Mock the workspace store
vi.mock('@/store/workspace')

// Mock the DailyPlanner component
vi.mock('@/components/daily-planner', () => ({
  DailyPlanner: ({ selectedDate }: { selectedDate: Date }) => (
    <div data-testid="daily-planner">
      Daily Planner for {format(selectedDate, 'yyyy-MM-dd')}
    </div>
  )
}))

const mockWorkspace = {
  id: 'workspace-1',
  name: 'Test Workspace',
  slug: 'test-workspace',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

describe('DailyPlannerPage', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    // Mock workspace store
    vi.mocked(useWorkspaceStore).mockReturnValue({
      currentWorkspace: mockWorkspace,
      workspaces: [mockWorkspace],
      setCurrentWorkspace: vi.fn(),
      addWorkspace: vi.fn(),
      updateWorkspace: vi.fn(),
      removeWorkspace: vi.fn(),
    })

    // Mock Date.now() to return a consistent date for testing
    vi.useFakeTimers()
    vi.setSystemTime(new Date('2024-01-15T10:00:00Z'))
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <DailyPlannerPage />
      </QueryClientProvider>
    )
  }

  it('renders page title and description', () => {
    renderComponent()

    expect(screen.getByText('Daily Planner')).toBeInTheDocument()
    expect(screen.getByText('Plan and track your daily tasks')).toBeInTheDocument()
  })

  it('renders navigation buttons', () => {
    renderComponent()

    expect(screen.getByRole('button', { name: /today/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /previous day/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /next day/i })).toBeInTheDocument()
  })

  it('displays current week navigation', () => {
    renderComponent()

    // Should show week range
    expect(screen.getByText(/Week of/)).toBeInTheDocument()
    expect(screen.getByText(/January/)).toBeInTheDocument()
    expect(screen.getByText(/2024/)).toBeInTheDocument()
  })

  it('shows week days with correct formatting', () => {
    renderComponent()

    // Should show day abbreviations
    expect(screen.getByText('Mon')).toBeInTheDocument()
    expect(screen.getByText('Tue')).toBeInTheDocument()
    expect(screen.getByText('Wed')).toBeInTheDocument()
    expect(screen.getByText('Thu')).toBeInTheDocument()
    expect(screen.getByText('Fri')).toBeInTheDocument()
    expect(screen.getByText('Sat')).toBeInTheDocument()
    expect(screen.getByText('Sun')).toBeInTheDocument()
  })

  it('highlights today in week navigation', () => {
    renderComponent()

    const todayBadge = screen.getByText('Today')
    expect(todayBadge).toBeInTheDocument()
  })

  it('renders DailyPlanner component with current date', () => {
    renderComponent()

    expect(screen.getByTestId('daily-planner')).toBeInTheDocument()
    expect(screen.getByText('Daily Planner for 2024-01-15')).toBeInTheDocument()
  })

  it('navigates to previous day when previous button is clicked', () => {
    renderComponent()

    const previousButton = screen.getByRole('button', { name: /previous day/i })
    fireEvent.click(previousButton)

    // Should show previous day
    expect(screen.getByText('Daily Planner for 2024-01-14')).toBeInTheDocument()
  })

  it('navigates to next day when next button is clicked', () => {
    renderComponent()

    const nextButton = screen.getByRole('button', { name: /next day/i })
    fireEvent.click(nextButton)

    // Should show next day
    expect(screen.getByText('Daily Planner for 2024-01-16')).toBeInTheDocument()
  })

  it('returns to today when Today button is clicked', () => {
    renderComponent()

    // Navigate away from today
    const nextButton = screen.getByRole('button', { name: /next day/i })
    fireEvent.click(nextButton)
    expect(screen.getByText('Daily Planner for 2024-01-16')).toBeInTheDocument()

    // Click Today button
    const todayButton = screen.getByRole('button', { name: /today/i })
    fireEvent.click(todayButton)

    // Should return to today
    expect(screen.getByText('Daily Planner for 2024-01-15')).toBeInTheDocument()
  })

  it('allows selecting different days from week navigation', () => {
    renderComponent()

    // Find a day button (not today) and click it
    const dayButtons = screen.getAllByRole('button').filter(button => 
      /^\d+$/.test(button.textContent || '')
    )
    
    // Click on a different day (assuming it's not the 15th)
    const differentDay = dayButtons.find(button => button.textContent !== '15')
    if (differentDay) {
      fireEvent.click(differentDay)
      
      // Should update the selected date
      expect(screen.getByTestId('daily-planner')).toBeInTheDocument()
    }
  })

  it('highlights selected day in week navigation', () => {
    renderComponent()

    // Today (15th) should be highlighted by default
    const todayButton = screen.getByText('15').closest('button')
    expect(todayButton).toHaveClass('bg-primary') // or whatever the selected class is
  })

  it('shows correct week range in header', () => {
    renderComponent()

    // Should show the week containing January 15, 2024
    expect(screen.getByText(/Week of January 15 - January 21, 2024/)).toBeInTheDocument()
  })

  it('updates week navigation when navigating to different weeks', () => {
    renderComponent()

    // Navigate to previous day multiple times to get to previous week
    const previousButton = screen.getByRole('button', { name: /previous day/i })
    
    // Click previous 7 times to go to previous week
    for (let i = 0; i < 7; i++) {
      fireEvent.click(previousButton)
    }

    // Should show different week range
    expect(screen.getByText(/Week of January 8 - January 14, 2024/)).toBeInTheDocument()
  })

  it('maintains selected date when clicking on same day in week navigation', () => {
    renderComponent()

    const currentDayButton = screen.getByText('15').closest('button')
    fireEvent.click(currentDayButton!)

    // Should still show the same date
    expect(screen.getByText('Daily Planner for 2024-01-15')).toBeInTheDocument()
  })

  it('handles month transitions correctly', () => {
    renderComponent()

    // Navigate to end of January
    const nextButton = screen.getByRole('button', { name: /next day/i })
    
    // Click next many times to get to February
    for (let i = 0; i < 20; i++) {
      fireEvent.click(nextButton)
    }

    // Should show February date
    expect(screen.getByText('Daily Planner for 2024-02-04')).toBeInTheDocument()
  })

  it('handles year transitions correctly', () => {
    // Set date to end of December
    vi.setSystemTime(new Date('2024-12-30T10:00:00Z'))
    
    renderComponent()

    const nextButton = screen.getByRole('button', { name: /next day/i })
    
    // Navigate to next year
    fireEvent.click(nextButton)
    fireEvent.click(nextButton)

    expect(screen.getByText('Daily Planner for 2025-01-01')).toBeInTheDocument()
  })

  it('shows today badge only for current date', () => {
    renderComponent()

    // Should show Today badge initially
    expect(screen.getByText('Today')).toBeInTheDocument()

    // Navigate to different day
    const nextButton = screen.getByRole('button', { name: /next day/i })
    fireEvent.click(nextButton)

    // Today badge should still be visible but not on selected day
    expect(screen.getByText('Today')).toBeInTheDocument()
  })

  it('formats week days correctly for different locales', () => {
    renderComponent()

    // Should show abbreviated day names
    const dayAbbreviations = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    dayAbbreviations.forEach(day => {
      expect(screen.getByText(day)).toBeInTheDocument()
    })
  })

  it('handles edge case of week starting on different days', () => {
    // Test with a date where week starts on Monday
    vi.setSystemTime(new Date('2024-01-01T10:00:00Z')) // Monday
    
    renderComponent()

    // Should still show proper week navigation
    expect(screen.getByText('Mon')).toBeInTheDocument()
    expect(screen.getByText('1')).toBeInTheDocument() // January 1st
  })
})