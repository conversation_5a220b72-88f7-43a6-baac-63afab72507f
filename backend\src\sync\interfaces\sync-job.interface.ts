import { SyncJobType, ConflictResolutionStrategy } from '../constants/sync.constants';
import { SyncConflict, SyncError } from '../../integrations/types';

/**
 * Base sync job data interface
 */
export interface BaseSyncJobData {
  integrationId: string;
  workspaceId: string;
  jobType: SyncJobType;
  priority?: number;
  retryCount?: number;
  metadata?: Record<string, any>;
}

/**
 * Full sync job data
 */
export interface FullSyncJobData extends BaseSyncJobData {
  jobType: SyncJobType.FULL_SYNC;
  forceSync?: boolean;
  syncAllTasks?: boolean;
}

/**
 * Incremental sync job data
 */
export interface IncrementalSyncJobData extends BaseSyncJobData {
  jobType: SyncJobType.INCREMENTAL_SYNC;
  lastSyncAt?: Date;
  modifiedSince?: Date;
}

/**
 * Webhook sync job data
 */
export interface WebhookSyncJobData extends BaseSyncJobData {
  jobType: SyncJobType.WEBHOOK_SYNC;
  webhookPayload: any;
  eventType: string;
  externalTaskId?: string;
}

/**
 * Conflict resolution job data
 */
export interface ConflictResolutionJobData extends BaseSyncJobData {
  jobType: SyncJobType.CONFLICT_RESOLUTION;
  conflicts: SyncConflict[];
  strategy: ConflictResolutionStrategy;
  resolvedBy?: string;
}

/**
 * Retry failed job data
 */
export interface RetryFailedJobData extends BaseSyncJobData {
  jobType: SyncJobType.RETRY_FAILED;
  originalJobId: string;
  originalError: SyncError;
  maxRetries?: number;
}

/**
 * Union type for all sync job data types
 */
export type SyncJobData = 
  | FullSyncJobData 
  | IncrementalSyncJobData 
  | WebhookSyncJobData 
  | ConflictResolutionJobData 
  | RetryFailedJobData;

/**
 * Sync job result interface
 */
export interface SyncJobResult {
  success: boolean;
  integrationId: string;
  jobType: SyncJobType;
  tasksProcessed: number;
  tasksCreated: number;
  tasksUpdated: number;
  tasksDeleted: number;
  conflictsDetected: number;
  conflictsResolved: number;
  errors: SyncError[];
  conflicts: SyncConflict[];
  duration: number;
  completedAt: Date;
  metadata?: Record<string, any>;
}

/**
 * Sync job status interface
 */
export interface SyncJobStatus {
  id: string;
  integrationId: string;
  jobType: SyncJobType;
  status: 'waiting' | 'active' | 'completed' | 'failed' | 'delayed' | 'paused';
  progress: number;
  createdAt: Date;
  processedAt?: Date;
  completedAt?: Date;
  failedAt?: Date;
  error?: string;
  result?: SyncJobResult;
}

/**
 * Sync schedule configuration
 */
export interface SyncScheduleConfig {
  integrationId: string;
  enabled: boolean;
  fullSyncInterval: number; // minutes
  incrementalSyncInterval: number; // minutes
  timezone?: string;
  activeHours?: {
    start: string; // HH:mm format
    end: string; // HH:mm format
  };
  excludeDays?: number[]; // 0-6, Sunday = 0
}