import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'

import { TaskSelector } from '../TaskSelector'
import { Task } from '@/types/task'

const mockTasks: Task[] = [
  {
    id: 'task-1',
    title: 'First Task',
    description: 'First task description',
    status: 'todo',
    priority: 'high',
    priorityScore: 85,
    estimatedMinutes: 60,
    tags: ['urgent'],
    projectName: 'Project A',
    sourceUrl: 'https://example.com/task-1',
    workspaceId: 'workspace-1',
    integrationId: 'integration-1',
    externalId: 'ext-1',
    syncStatus: 'synced',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    lastSyncAt: '2024-01-01T00:00:00Z',
    metadata: {}
  },
  {
    id: 'task-2',
    title: 'Second Task',
    description: 'Second task description',
    status: 'todo',
    priority: 'medium',
    priorityScore: 65,
    estimatedMinutes: 30,
    tags: ['feature'],
    projectName: 'Project B',
    sourceUrl: 'https://example.com/task-2',
    workspaceId: 'workspace-1',
    integrationId: 'integration-1',
    externalId: 'ext-2',
    syncStatus: 'synced',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    lastSyncAt: '2024-01-01T00:00:00Z',
    metadata: {}
  },
  {
    id: 'task-3',
    title: 'Third Task',
    description: 'Third task with different project',
    status: 'todo',
    priority: 'low',
    priorityScore: 45,
    estimatedMinutes: 45,
    tags: ['bug'],
    projectName: 'Project C',
    sourceUrl: 'https://example.com/task-3',
    workspaceId: 'workspace-1',
    integrationId: 'integration-1',
    externalId: 'ext-3',
    syncStatus: 'synced',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    lastSyncAt: '2024-01-01T00:00:00Z',
    metadata: {}
  }
]

describe('TaskSelector', () => {
  const mockOnClose = vi.fn()
  const mockOnAddTask = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  const renderComponent = (props = {}) => {
    const defaultProps = {
      isOpen: true,
      onClose: mockOnClose,
      onAddTask: mockOnAddTask,
      availableTasks: mockTasks,
      selectedTask: null,
      ...props
    }

    return render(<TaskSelector {...defaultProps} />)
  }

  it('renders dialog when open', () => {
    renderComponent()

    expect(screen.getByText('Add Task to Daily Plan')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Search tasks...')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    renderComponent({ isOpen: false })

    expect(screen.queryByText('Add Task to Daily Plan')).not.toBeInTheDocument()
  })

  it('displays all available tasks', () => {
    renderComponent()

    expect(screen.getByText('First Task')).toBeInTheDocument()
    expect(screen.getByText('Second Task')).toBeInTheDocument()
    expect(screen.getByText('Third Task')).toBeInTheDocument()
  })

  it('shows task details correctly', () => {
    renderComponent()

    // Check first task details
    expect(screen.getByText('First Task')).toBeInTheDocument()
    expect(screen.getByText('First task description')).toBeInTheDocument()
    expect(screen.getByText('Project A')).toBeInTheDocument()
    expect(screen.getByText('60m')).toBeInTheDocument()
    expect(screen.getByText('85')).toBeInTheDocument() // Priority score
  })

  it('filters tasks based on search query', async () => {
    renderComponent()

    const searchInput = screen.getByPlaceholderText('Search tasks...')
    fireEvent.change(searchInput, { target: { value: 'First' } })

    await waitFor(() => {
      expect(screen.getByText('First Task')).toBeInTheDocument()
      expect(screen.queryByText('Second Task')).not.toBeInTheDocument()
      expect(screen.queryByText('Third Task')).not.toBeInTheDocument()
    })
  })

  it('filters tasks by description', async () => {
    renderComponent()

    const searchInput = screen.getByPlaceholderText('Search tasks...')
    fireEvent.change(searchInput, { target: { value: 'different project' } })

    await waitFor(() => {
      expect(screen.getByText('Third Task')).toBeInTheDocument()
      expect(screen.queryByText('First Task')).not.toBeInTheDocument()
      expect(screen.queryByText('Second Task')).not.toBeInTheDocument()
    })
  })

  it('filters tasks by project name', async () => {
    renderComponent()

    const searchInput = screen.getByPlaceholderText('Search tasks...')
    fireEvent.change(searchInput, { target: { value: 'Project B' } })

    await waitFor(() => {
      expect(screen.getByText('Second Task')).toBeInTheDocument()
      expect(screen.queryByText('First Task')).not.toBeInTheDocument()
      expect(screen.queryByText('Third Task')).not.toBeInTheDocument()
    })
  })

  it('filters tasks by tags', async () => {
    renderComponent()

    const searchInput = screen.getByPlaceholderText('Search tasks...')
    fireEvent.change(searchInput, { target: { value: 'urgent' } })

    await waitFor(() => {
      expect(screen.getByText('First Task')).toBeInTheDocument()
      expect(screen.queryByText('Second Task')).not.toBeInTheDocument()
      expect(screen.queryByText('Third Task')).not.toBeInTheDocument()
    })
  })

  it('shows no tasks found message when search has no results', async () => {
    renderComponent()

    const searchInput = screen.getByPlaceholderText('Search tasks...')
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } })

    await waitFor(() => {
      expect(screen.getByText('No tasks found')).toBeInTheDocument()
    })
  })

  it('selects task when clicked', () => {
    renderComponent()

    const firstTask = screen.getByText('First Task').closest('div')
    fireEvent.click(firstTask!)

    // Task should be highlighted
    expect(firstTask).toHaveClass('bg-blue-50', 'border-blue-200')
  })

  it('shows selected task details when task is selected', () => {
    renderComponent()

    const firstTask = screen.getByText('First Task').closest('div')
    fireEvent.click(firstTask!)

    expect(screen.getByText('Selected Task')).toBeInTheDocument()
    expect(screen.getByText('First Task')).toBeInTheDocument()
  })

  it('pre-selects task when selectedTask prop is provided', () => {
    renderComponent({ selectedTask: mockTasks[0] })

    expect(screen.getByText('Selected Task')).toBeInTheDocument()
    expect(screen.getByText('First Task')).toBeInTheDocument()
  })

  it('updates estimated time when task is selected', () => {
    renderComponent()

    const secondTask = screen.getByText('Second Task').closest('div')
    fireEvent.click(secondTask!)

    // Should show the task's estimated time in the time input
    expect(screen.getByDisplayValue('30')).toBeInTheDocument()
  })

  it('calls onAddTask when Add to Plan button is clicked', () => {
    renderComponent()

    // Select a task
    const firstTask = screen.getByText('First Task').closest('div')
    fireEvent.click(firstTask!)

    // Click Add to Plan
    const addButton = screen.getByRole('button', { name: /add to plan/i })
    fireEvent.click(addButton)

    expect(mockOnAddTask).toHaveBeenCalledWith('task-1', 60)
  })

  it('calls onClose when Cancel button is clicked', () => {
    renderComponent()

    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    fireEvent.click(cancelButton)

    expect(mockOnClose).toHaveBeenCalled()
  })

  it('disables Add to Plan button when no task is selected', () => {
    renderComponent()

    const addButton = screen.getByRole('button', { name: /add to plan/i })
    expect(addButton).toBeDisabled()
  })

  it('disables Add to Plan button when estimated time is 0', () => {
    renderComponent()

    // Select a task
    const firstTask = screen.getByText('First Task').closest('div')
    fireEvent.click(firstTask!)

    // Set estimated time to 0
    const timeInput = screen.getByDisplayValue('60')
    fireEvent.change(timeInput, { target: { value: '0' } })

    const addButton = screen.getByRole('button', { name: /add to plan/i })
    expect(addButton).toBeDisabled()
  })

  it('allows changing estimated time', () => {
    renderComponent()

    // Select a task
    const firstTask = screen.getByText('First Task').closest('div')
    fireEvent.click(firstTask!)

    // Change estimated time using the time estimation input
    const incrementButton = screen.getByLabelText('Increment time')
    fireEvent.click(incrementButton)

    expect(screen.getByDisplayValue('65')).toBeInTheDocument()
  })

  it('shows empty state when no tasks available', () => {
    renderComponent({ availableTasks: [] })

    expect(screen.getByText('No tasks found')).toBeInTheDocument()
  })

  it('handles tasks without descriptions', () => {
    const tasksWithoutDescription = [
      {
        ...mockTasks[0],
        description: undefined
      }
    ]

    renderComponent({ availableTasks: tasksWithoutDescription })

    expect(screen.getByText('First Task')).toBeInTheDocument()
    // Should not crash when description is undefined
  })

  it('handles tasks without project names', () => {
    const tasksWithoutProject = [
      {
        ...mockTasks[0],
        projectName: undefined
      }
    ]

    renderComponent({ availableTasks: tasksWithoutProject })

    expect(screen.getByText('First Task')).toBeInTheDocument()
    // Should not show project badge when project name is undefined
  })

  it('case-insensitive search', async () => {
    renderComponent()

    const searchInput = screen.getByPlaceholderText('Search tasks...')
    fireEvent.change(searchInput, { target: { value: 'FIRST' } })

    await waitFor(() => {
      expect(screen.getByText('First Task')).toBeInTheDocument()
    })
  })

  it('clears search when input is cleared', async () => {
    renderComponent()

    const searchInput = screen.getByPlaceholderText('Search tasks...')
    
    // Search for something
    fireEvent.change(searchInput, { target: { value: 'First' } })
    
    await waitFor(() => {
      expect(screen.queryByText('Second Task')).not.toBeInTheDocument()
    })

    // Clear search
    fireEvent.change(searchInput, { target: { value: '' } })

    await waitFor(() => {
      expect(screen.getByText('Second Task')).toBeInTheDocument()
    })
  })
})