import { Test, TestingModule } from '@nestjs/testing';
import { ProgressTrackingService } from '../services/progress-tracking.service';
import { ReportExportService } from '../services/report-export.service';
import { DatabaseService } from '../../database/database.service';

describe('Progress Tracking Integration - Simple', () => {
  let progressTrackingService: ProgressTrackingService;
  let reportExportService: ReportExportService;

  const mockDatabaseService = {
    task: {
      count: jest.fn(),
      groupBy: jest.fn(),
      findMany: jest.fn(),
    },
    integration: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProgressTrackingService,
        ReportExportService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    progressTrackingService = module.get<ProgressTrackingService>(ProgressTrackingService);
    reportExportService = module.get<ReportExportService>(ReportExportService);
  });

  describe('ProgressTrackingService', () => {
    it('should be defined', () => {
      expect(progressTrackingService).toBeDefined();
    });

    it('should have all required methods', () => {
      expect(progressTrackingService.getCompletionRateReport).toBeDefined();
      expect(progressTrackingService.getTaskAgingReport).toBeDefined();
      expect(progressTrackingService.getVelocityReport).toBeDefined();
      expect(progressTrackingService.getReportFilterOptions).toBeDefined();
    });

    it('should generate completion rate report with mocked data', async () => {
      // Mock the database calls
      mockDatabaseService.task.count
        .mockResolvedValueOnce(100) // total tasks
        .mockResolvedValueOnce(75); // completed tasks

      mockDatabaseService.task.groupBy
        .mockResolvedValueOnce([]) // by source
        .mockResolvedValueOnce([]); // by project

      const filters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
      };

      const result = await progressTrackingService.getCompletionRateReport('workspace-1', filters);

      expect(result).toBeDefined();
      expect(result.overall).toBeDefined();
      expect(result.overall.totalTasks).toBe(100);
      expect(result.overall.completedTasks).toBe(75);
      expect(result.overall.completionRate).toBe(75);
      expect(result.bySource).toEqual([]);
      expect(result.byProject).toEqual([]);
      expect(result.filters).toEqual(filters);
      expect(result.generatedAt).toBeInstanceOf(Date);
    });

    it('should generate task aging report with mocked data', async () => {
      const mockTasks = [
        {
          id: 'task-1',
          title: 'Test Task',
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
          dueDate: null,
          status: 'todo',
          projectName: 'Test Project',
          assigneeName: 'John Doe',
          integration: { provider: 'asana', name: 'Asana Integration' },
        },
      ];

      mockDatabaseService.task.findMany.mockResolvedValue(mockTasks);

      const filters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
      };

      const result = await progressTrackingService.getTaskAgingReport('workspace-1', filters);

      expect(result).toBeDefined();
      expect(result.totalActiveTasks).toBe(1);
      expect(result.tasksByAgeRange).toHaveLength(3); // Default age ranges
      expect(result.filters).toEqual(filters);
      expect(result.generatedAt).toBeInstanceOf(Date);
    });

    it('should generate velocity report with mocked data', async () => {
      const mockCompletedTasks = [
        {
          id: 'task-1',
          updatedAt: new Date('2024-01-15'),
          estimatedMinutes: 60,
          integration: { provider: 'asana', name: 'Asana Integration' },
        },
      ];

      mockDatabaseService.task.findMany.mockResolvedValue(mockCompletedTasks);
      mockDatabaseService.task.groupBy.mockResolvedValue([
        {
          integrationId: 'int-1',
          _count: { _all: 1 },
          _sum: { estimatedMinutes: 60 },
        },
      ]);
      mockDatabaseService.integration.findUnique.mockResolvedValue({
        provider: 'asana',
        name: 'Asana Integration',
      });

      const filters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
      };

      const result = await progressTrackingService.getVelocityReport('workspace-1', filters, 4);

      expect(result).toBeDefined();
      expect(result.weeklyData).toHaveLength(4);
      expect(result.averageTasksPerWeek).toBeGreaterThanOrEqual(0);
      expect(result.trendAnalysis).toBeDefined();
      expect(result.velocityBySource).toHaveLength(1);
      expect(result.filters).toEqual(filters);
      expect(result.generatedAt).toBeInstanceOf(Date);
    });

    it('should get report filter options', async () => {
      mockDatabaseService.integration.findMany.mockResolvedValue([
        { provider: 'asana', name: 'Asana Integration' },
      ]);
      mockDatabaseService.task.findMany
        .mockResolvedValueOnce([{ projectName: 'Test Project' }])
        .mockResolvedValueOnce([{ assigneeId: 'user-1', assigneeName: 'John Doe' }]);

      const result = await progressTrackingService.getReportFilterOptions('workspace-1');

      expect(result).toBeDefined();
      expect(result.sources).toEqual([{ value: 'asana', label: 'Asana Integration' }]);
      expect(result.projects).toEqual([{ value: 'Test Project', label: 'Test Project' }]);
      expect(result.assignees).toEqual([{ value: 'user-1', label: 'John Doe' }]);
    });
  });

  describe('ReportExportService', () => {
    it('should be defined', () => {
      expect(reportExportService).toBeDefined();
    });

    it('should export completion rate report as CSV', async () => {
      const mockReport = {
        overall: {
          totalTasks: 100,
          completedTasks: 75,
          completionRate: 75,
        },
        bySource: [
          {
            source: 'asana',
            sourceName: 'Asana Integration',
            totalTasks: 50,
            completedTasks: 40,
            completionRate: 80,
          },
        ],
        byProject: [
          {
            project: 'Test Project',
            totalTasks: 60,
            completedTasks: 45,
            completionRate: 75,
          },
        ],
        byTimePeriod: [],
        filters: {
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
        },
        generatedAt: new Date('2024-01-15T10:00:00Z'),
      };

      const result = await reportExportService.exportReport(
        mockReport,
        'completion' as any,
        'csv' as any
      );

      expect(result).toBeDefined();
      expect(result.filename).toMatch(/completion-report-\d{4}-\d{2}-\d{2}\.csv/);
      expect(result.mimeType).toBe('text/csv');
      expect(result.data).toBeInstanceOf(Buffer);

      const csvContent = result.data.toString('utf-8');
      expect(csvContent).toContain('Report Type,Completion Rate Report');
      expect(csvContent).toContain('100,75,75.00');
      expect(csvContent).toContain('asana,Asana Integration,50,40,80.00');
    });

    it('should export report as PDF placeholder', async () => {
      const mockReport = {
        overall: { totalTasks: 10, completedTasks: 5, completionRate: 50 },
        bySource: [],
        byProject: [],
        byTimePeriod: [],
        filters: { startDate: new Date('2024-01-01'), endDate: new Date('2024-12-31') },
        generatedAt: new Date('2024-01-15T10:00:00Z'),
      };

      const result = await reportExportService.exportReport(
        mockReport,
        'completion' as any,
        'pdf' as any
      );

      expect(result).toBeDefined();
      expect(result.filename).toMatch(/completion-report-\d{4}-\d{2}-\d{2}\.pdf/);
      expect(result.mimeType).toBe('application/pdf');
      expect(result.data).toBeInstanceOf(Buffer);

      const pdfContent = result.data.toString('utf-8');
      expect(pdfContent).toContain('PDF Report Placeholder');
      expect(pdfContent).toContain('Report Type: completion');
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});