import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { TaskListItem } from '../TaskListItem'
import { Task, TaskStatus, TaskPriority, SyncStatus } from '@/types/task'

// Mock date-fns
vi.mock('date-fns', () => ({
  formatDistanceToNow: vi.fn(() => '2 hours ago'),
  format: vi.fn(() => 'Dec 25, 2024 2:30 PM')
}))

const mockTask: Task = {
  id: 'task-1',
  workspaceId: 'workspace-1',
  integrationId: 'integration-1',
  externalId: 'ext-1',
  title: 'Test Task',
  description: 'This is a test task description',
  status: TaskStatus.TODO,
  priority: TaskPriority.HIGH,
  priorityScore: 85.5,
  assigneeId: 'user-1',
  assigneeName: '<PERSON>',
  dueDate: '2024-12-25T14:30:00Z',
  estimatedMinutes: 120,
  tags: ['urgent', 'bug', 'frontend'],
  projectName: 'Test Project',
  sourceUrl: 'https://example.com/task/1',
  metadata: {},
  syncStatus: SyncStatus.SYNCED,
  createdAt: '2024-12-20T10:00:00Z',
  updatedAt: '2024-12-23T15:30:00Z',
  lastSyncAt: '2024-12-23T15:30:00Z',
  integration: {
    id: 'integration-1',
    name: 'Asana',
    provider: 'asana'
  }
}

describe('TaskListItem', () => {
  const mockOnSelectionChange = vi.fn()
  const mockOnTaskClick = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render task information correctly', () => {
    render(
      <TaskListItem
        task={mockTask}
        isSelected={false}
        onSelectionChange={mockOnSelectionChange}
        onTaskClick={mockOnTaskClick}
      />
    )

    expect(screen.getByText('Test Task')).toBeInTheDocument()
    expect(screen.getByText('This is a test task description')).toBeInTheDocument()
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Test Project')).toBeInTheDocument()
    expect(screen.getByText('120m')).toBeInTheDocument()
    expect(screen.getByText('Asana')).toBeInTheDocument()
  })

  it('should show priority badge with score', () => {
    render(
      <TaskListItem
        task={mockTask}
        isSelected={false}
        onSelectionChange={mockOnSelectionChange}
        onTaskClick={mockOnTaskClick}
      />
    )

    expect(screen.getByText('High')).toBeInTheDocument()
    expect(screen.getByText('86')).toBeInTheDocument() // Rounded priority score
  })

  it('should show status badge', () => {
    render(
      <TaskListItem
        task={mockTask}
        isSelected={false}
        onSelectionChange={mockOnSelectionChange}
        onTaskClick={mockOnTaskClick}
      />
    )

    expect(screen.getByText('To Do')).toBeInTheDocument()
  })

  it('should show sync status badge', () => {
    render(
      <TaskListItem
        task={mockTask}
        isSelected={false}
        onSelectionChange={mockOnSelectionChange}
        onTaskClick={mockOnTaskClick}
      />
    )

    expect(screen.getByText('Synced')).toBeInTheDocument()
  })

  it('should show first 3 tags and count for more', () => {
    render(
      <TaskListItem
        task={mockTask}
        isSelected={false}
        onSelectionChange={mockOnSelectionChange}
        onTaskClick={mockOnTaskClick}
      />
    )

    expect(screen.getByText('urgent')).toBeInTheDocument()
    expect(screen.getByText('bug')).toBeInTheDocument()
    expect(screen.getByText('frontend')).toBeInTheDocument()
  })

  it('should handle task with many tags', () => {
    const taskWithManyTags = {
      ...mockTask,
      tags: ['tag1', 'tag2', 'tag3', 'tag4', 'tag5']
    }

    render(
      <TaskListItem
        task={taskWithManyTags}
        isSelected={false}
        onSelectionChange={mockOnSelectionChange}
        onTaskClick={mockOnTaskClick}
      />
    )

    expect(screen.getByText('tag1')).toBeInTheDocument()
    expect(screen.getByText('tag2')).toBeInTheDocument()
    expect(screen.getByText('tag3')).toBeInTheDocument()
    expect(screen.getByText('+2 more')).toBeInTheDocument()
  })

  it('should handle selection change', () => {
    render(
      <TaskListItem
        task={mockTask}
        isSelected={false}
        onSelectionChange={mockOnSelectionChange}
        onTaskClick={mockOnTaskClick}
      />
    )

    const checkbox = screen.getByRole('checkbox')
    fireEvent.click(checkbox)

    expect(mockOnSelectionChange).toHaveBeenCalledWith('task-1', true)
  })

  it('should handle task click', () => {
    render(
      <TaskListItem
        task={mockTask}
        isSelected={false}
        onSelectionChange={mockOnSelectionChange}
        onTaskClick={mockOnTaskClick}
      />
    )

    const taskContent = screen.getByText('Test Task').closest('div')
    fireEvent.click(taskContent!)

    expect(mockOnTaskClick).toHaveBeenCalledWith(mockTask)
  })

  it('should show selected state', () => {
    render(
      <TaskListItem
        task={mockTask}
        isSelected={true}
        onSelectionChange={mockOnSelectionChange}
        onTaskClick={mockOnTaskClick}
      />
    )

    const checkbox = screen.getByRole('checkbox')
    expect(checkbox).toBeChecked()
  })

  it('should handle task without optional fields', () => {
    const minimalTask: Task = {
      ...mockTask,
      description: undefined,
      assigneeName: undefined,
      dueDate: undefined,
      estimatedMinutes: undefined,
      projectName: undefined,
      tags: []
    }

    render(
      <TaskListItem
        task={minimalTask}
        isSelected={false}
        onSelectionChange={mockOnSelectionChange}
        onTaskClick={mockOnTaskClick}
      />
    )

    expect(screen.getByText('Test Task')).toBeInTheDocument()
    expect(screen.queryByText('This is a test task description')).not.toBeInTheDocument()
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
  })

  it('should open external link when external link button is clicked', () => {
    const mockOpen = vi.fn()
    Object.defineProperty(window, 'open', { value: mockOpen })

    render(
      <TaskListItem
        task={mockTask}
        isSelected={true} // Selected to show external link button
        onSelectionChange={mockOnSelectionChange}
        onTaskClick={mockOnTaskClick}
      />
    )

    const externalLinkButton = screen.getByTitle('Open in source tool')
    fireEvent.click(externalLinkButton)

    expect(mockOpen).toHaveBeenCalledWith('https://example.com/task/1', '_blank')
  })
})