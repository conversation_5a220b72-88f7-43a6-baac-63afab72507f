import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DatabaseService } from '../database/database.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(private readonly databaseService: DatabaseService) {}

  @Get()
  @ApiOperation({ summary: 'Health check endpoint' })
  @ApiResponse({ 
    status: 200, 
    description: 'Service is healthy',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', example: '2024-12-08T10:30:00.000Z' },
        uptime: { type: 'number', example: 123.456 },
        environment: { type: 'string', example: 'development' },
        database: { type: 'string', example: 'healthy' },
        version: { type: 'string', example: '1.0.0' },
      }
    }
  })
  async getHealth() {
    const dbHealth = await this.databaseService.healthCheck();
    
    return {
      status: dbHealth.status === 'healthy' ? 'ok' : 'degraded',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      database: dbHealth.status,
      version: process.env.npm_package_version || '1.0.0',
      ...(dbHealth.details && { databaseError: dbHealth.details }),
    };
  }

  @Get('database')
  @ApiOperation({ summary: 'Database health check' })
  @ApiResponse({ 
    status: 200, 
    description: 'Database health status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        stats: {
          type: 'object',
          properties: {
            users: { type: 'number', example: 3 },
            workspaces: { type: 'number', example: 2 },
            integrations: { type: 'number', example: 3 },
            tasks: { type: 'number', example: 5 },
            syncLogs: { type: 'number', example: 3 },
            timestamp: { type: 'string', example: '2024-12-08T10:30:00.000Z' },
          }
        }
      }
    }
  })
  async getDatabaseHealth() {
    const health = await this.databaseService.healthCheck();
    
    if (health.status === 'healthy') {
      const stats = await this.databaseService.getStats();
      return {
        status: health.status,
        stats,
      };
    }
    
    return {
      status: health.status,
      error: health.details,
    };
  }
}