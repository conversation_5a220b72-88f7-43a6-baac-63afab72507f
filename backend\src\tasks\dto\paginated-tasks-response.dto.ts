import { ApiProperty } from '@nestjs/swagger';
import { Task } from '@prisma/client';

export class PaginatedTasksResponseDto {
  @ApiProperty({ 
    description: 'Array of tasks',
    type: 'array',
    items: { type: 'object' }
  })
  tasks: Task[];

  @ApiProperty({ 
    description: 'Total number of tasks matching the query',
    example: 150
  })
  total: number;

  @ApiProperty({ 
    description: 'Number of tasks returned in this response',
    example: 20
  })
  count: number;

  @ApiProperty({ 
    description: 'Current offset',
    example: 0
  })
  offset: number;

  @ApiProperty({ 
    description: 'Limit used for this query',
    example: 20
  })
  limit: number;

  @ApiProperty({ 
    description: 'Whether there are more tasks available',
    example: true
  })
  hasMore: boolean;
}