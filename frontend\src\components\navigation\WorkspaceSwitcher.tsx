import { useState } from 'react'
import { ChevronDown, Plus, Check, Settings } from 'lucide-react'
import { useWorkspaceStore } from '@/store/workspace'
import { useNavigate } from 'react-router-dom'

export function WorkspaceSwitcher() {
  const [isOpen, setIsOpen] = useState(false)
  const { workspaces, currentWorkspace, switchWorkspace } = useWorkspaceStore()
  const navigate = useNavigate()

  const handleWorkspaceSwitch = (workspaceId: string) => {
    switchWorkspace(workspaceId)
    setIsOpen(false)
  }

  const handleCreateWorkspace = () => {
    setIsOpen(false)
    navigate('/workspaces?action=create')
  }

  const handleManageWorkspaces = () => {
    setIsOpen(false)
    navigate('/workspaces')
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-3 py-2 text-sm font-medium text-gray-700 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
        data-testid="workspace-switcher-button"
      >
        <span className="truncate">
          {currentWorkspace?.name || 'Select Workspace'}
        </span>
        <ChevronDown className="h-4 w-4 ml-2 flex-shrink-0" />
      </button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute z-20 w-full mt-1 bg-white rounded-md shadow-lg border border-gray-200">
            <div className="py-1">
              {workspaces.map((workspace) => (
                <button
                  key={workspace.id}
                  onClick={() => handleWorkspaceSwitch(workspace.id)}
                  className="w-full flex items-center justify-between px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  data-testid={`workspace-option-${workspace.id}`}
                >
                  <span className="truncate">{workspace.name}</span>
                  {currentWorkspace?.id === workspace.id && (
                    <Check className="h-4 w-4 text-blue-600" />
                  )}
                </button>
              ))}
              <div className="border-t border-gray-200 mt-1 pt-1">
                <button 
                  onClick={handleCreateWorkspace}
                  className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  data-testid="create-workspace-button"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Workspace
                </button>
                <button 
                  onClick={handleManageWorkspaces}
                  className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  data-testid="manage-workspaces-button"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Manage Workspaces
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}