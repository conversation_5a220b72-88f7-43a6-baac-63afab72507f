import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { vi, describe, it, expect, beforeEach } from 'vitest'

import { DailyPlanTask } from '../DailyPlanTask'
import { dailyPlanService } from '@/services/daily-plan'
import { DailyPlanTask as DailyPlanTaskType } from '@/types/task'

// Mock the service
vi.mock('@/services/daily-plan')

const mockPlanTask: DailyPlanTaskType = {
  id: 'plan-task-1',
  taskId: 'task-1',
  estimatedMinutes: 60,
  actualMinutes: null,
  orderIndex: 0,
  completedAt: null,
  task: {
    id: 'task-1',
    title: 'Test Task',
    description: 'Test description',
    status: 'todo',
    priority: 'high',
    priorityScore: 85,
    dueDate: '2024-01-20T00:00:00Z',
    tags: ['urgent', 'important'],
    projectName: 'Test Project',
    sourceUrl: 'https://example.com/task-1'
  }
}

const mockCompletedPlanTask: DailyPlanTaskType = {
  ...mockPlanTask,
  id: 'plan-task-2',
  actualMinutes: 45,
  completedAt: new Date('2024-01-15T14:30:00Z')
}

describe('DailyPlanTask', () => {
  let queryClient: QueryClient
  const mockOnRemove = vi.fn()
  const workspaceId = 'workspace-1'
  const planId = 'plan-1'

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    vi.clearAllMocks()
  })

  const renderComponent = (planTask = mockPlanTask) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <DailyPlanTask
          planTask={planTask}
          onRemove={mockOnRemove}
          workspaceId={workspaceId}
          planId={planId}
          dragHandleProps={{ 'data-testid': 'drag-handle' }}
        />
      </QueryClientProvider>
    )
  }

  it('renders task information correctly', () => {
    renderComponent()

    expect(screen.getByText('Test Task')).toBeInTheDocument()
    expect(screen.getByText('Test description')).toBeInTheDocument()
    expect(screen.getByText('Test Project')).toBeInTheDocument()
    expect(screen.getByText('Due Jan 20')).toBeInTheDocument()
    expect(screen.getByText('1h estimated')).toBeInTheDocument()
  })

  it('displays task badges correctly', () => {
    renderComponent()

    expect(screen.getByText('high')).toBeInTheDocument()
    expect(screen.getByText('todo')).toBeInTheDocument()
    expect(screen.getByText('Test Project')).toBeInTheDocument()
  })

  it('shows drag handle', () => {
    renderComponent()

    expect(screen.getByTestId('drag-handle')).toBeInTheDocument()
  })

  it('renders completion checkbox as unchecked for incomplete task', () => {
    renderComponent()

    const checkbox = screen.getByRole('button', { name: /complete task/i })
    expect(checkbox).toBeInTheDocument()
    
    // Should show empty circle icon for incomplete task
    const circleIcon = checkbox.querySelector('svg')
    expect(circleIcon).toBeInTheDocument()
  })

  it('renders completion checkbox as checked for completed task', () => {
    renderComponent(mockCompletedPlanTask)

    const checkbox = screen.getByRole('button', { name: /complete task/i })
    expect(checkbox).toBeInTheDocument()
    
    // Task should appear completed
    expect(screen.getByText('Test Task')).toHaveClass('line-through')
  })

  it('shows actual time when task is completed', () => {
    renderComponent(mockCompletedPlanTask)

    expect(screen.getByText('45m actual')).toBeInTheDocument()
  })

  it('calls complete mutation when checkbox is clicked', async () => {
    const completeMock = vi.mocked(dailyPlanService.completeDailyPlanTask).mockResolvedValue()

    renderComponent()

    const checkbox = screen.getByRole('button', { name: /complete task/i })
    fireEvent.click(checkbox)

    await waitFor(() => {
      expect(completeMock).toHaveBeenCalledWith(workspaceId, planId, 'plan-task-1')
    })
  })

  it('opens external link when external link button is clicked', () => {
    const mockOpen = vi.fn()
    vi.stubGlobal('open', mockOpen)

    renderComponent()

    const externalLinkButton = screen.getByRole('button', { name: /open external link/i })
    fireEvent.click(externalLinkButton)

    expect(mockOpen).toHaveBeenCalledWith('https://example.com/task-1', '_blank')
  })

  it('calls onRemove when remove button is clicked', () => {
    renderComponent()

    const removeButton = screen.getByRole('button', { name: /remove task/i })
    fireEvent.click(removeButton)

    expect(mockOnRemove).toHaveBeenCalled()
  })

  it('allows editing estimated time', async () => {
    const updateMock = vi.mocked(dailyPlanService.updateDailyPlanTask).mockResolvedValue()

    renderComponent()

    // Click edit button
    const editButton = screen.getByRole('button', { name: /edit time/i })
    fireEvent.click(editButton)

    // Should show input field
    const input = screen.getByDisplayValue('60')
    expect(input).toBeInTheDocument()

    // Change value
    fireEvent.change(input, { target: { value: '90' } })

    // Click save
    const saveButton = screen.getByRole('button', { name: /save/i })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(updateMock).toHaveBeenCalledWith(workspaceId, planId, 'plan-task-1', {
        estimatedMinutes: 90
      })
    })
  })

  it('cancels editing when cancel button is clicked', () => {
    renderComponent()

    // Click edit button
    const editButton = screen.getByRole('button', { name: /edit time/i })
    fireEvent.click(editButton)

    // Should show input field
    expect(screen.getByDisplayValue('60')).toBeInTheDocument()

    // Click cancel
    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    fireEvent.click(cancelButton)

    // Should hide input field
    expect(screen.queryByDisplayValue('60')).not.toBeInTheDocument()
    expect(screen.getByText('1h estimated')).toBeInTheDocument()
  })

  it('starts time tracking when start button is clicked', () => {
    renderComponent()

    const startButton = screen.getByRole('button', { name: /start/i })
    fireEvent.click(startButton)

    expect(screen.getByRole('button', { name: /stop/i })).toBeInTheDocument()
    expect(screen.getByText(/tracking/)).toBeInTheDocument()
  })

  it('stops time tracking and updates actual time', async () => {
    const updateMock = vi.mocked(dailyPlanService.updateDailyPlanTask).mockResolvedValue()

    renderComponent()

    // Start tracking
    const startButton = screen.getByRole('button', { name: /start/i })
    fireEvent.click(startButton)

    // Stop tracking
    const stopButton = screen.getByRole('button', { name: /stop/i })
    fireEvent.click(stopButton)

    await waitFor(() => {
      expect(updateMock).toHaveBeenCalledWith(
        workspaceId, 
        planId, 
        'plan-task-1', 
        expect.objectContaining({
          actualMinutes: expect.any(Number)
        })
      )
    })
  })

  it('completes task with tracked time when complete is clicked during tracking', async () => {
    const updateMock = vi.mocked(dailyPlanService.updateDailyPlanTask).mockResolvedValue()

    renderComponent()

    // Start tracking
    const startButton = screen.getByRole('button', { name: /start/i })
    fireEvent.click(startButton)

    // Complete task while tracking
    const checkbox = screen.getByRole('button', { name: /complete task/i })
    fireEvent.click(checkbox)

    await waitFor(() => {
      expect(updateMock).toHaveBeenCalledWith(
        workspaceId, 
        planId, 
        'plan-task-1', 
        expect.objectContaining({
          actualMinutes: expect.any(Number),
          completedAt: expect.any(Date)
        })
      )
    })
  })

  it('does not show time tracking controls for completed tasks', () => {
    renderComponent(mockCompletedPlanTask)

    expect(screen.queryByRole('button', { name: /start/i })).not.toBeInTheDocument()
    expect(screen.queryByRole('button', { name: /stop/i })).not.toBeInTheDocument()
  })

  it('formats duration correctly', () => {
    const longTask = {
      ...mockPlanTask,
      estimatedMinutes: 150 // 2h 30m
    }

    renderComponent(longTask)

    expect(screen.getByText('2h 30m estimated')).toBeInTheDocument()
  })

  it('handles tasks without due date', () => {
    const taskWithoutDueDate = {
      ...mockPlanTask,
      task: {
        ...mockPlanTask.task,
        dueDate: null
      }
    }

    renderComponent(taskWithoutDueDate)

    expect(screen.queryByText(/due/i)).not.toBeInTheDocument()
  })

  it('handles tasks without project name', () => {
    const taskWithoutProject = {
      ...mockPlanTask,
      task: {
        ...mockPlanTask.task,
        projectName: null
      }
    }

    renderComponent(taskWithoutProject)

    expect(screen.queryByText('Test Project')).not.toBeInTheDocument()
  })

  it('applies completed styling to completed tasks', () => {
    renderComponent(mockCompletedPlanTask)

    expect(screen.getByText('Test Task')).toHaveClass('line-through', 'text-gray-500')
  })

  it('shows loading state during mutations', async () => {
    vi.mocked(dailyPlanService.completeDailyPlanTask).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    )

    renderComponent()

    const checkbox = screen.getByRole('button', { name: /complete task/i })
    fireEvent.click(checkbox)

    expect(checkbox).toBeDisabled()
  })
})