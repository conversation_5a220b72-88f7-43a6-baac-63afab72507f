import React, { useState } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { format } from 'date-fns'
import { 
  Clock, 
  CheckCircle2, 
  Circle, 
  ExternalLink, 
  GripVertical, 
  X, 
  Edit3,
  Play,
  Pause
} from 'lucide-react'

import { DailyPlanTask as DailyPlanTaskType } from '@/types/task'
import { dailyPlanService } from '@/services/daily-plan'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { TaskStatusBadge } from '@/components/tasks/TaskStatusBadge'
import { TaskPriorityBadge } from '@/components/tasks/TaskPriorityBadge'

interface DailyPlanTaskProps {
  planTask: DailyPlanTaskType
  dragHandleProps?: any
  onRemove: () => void
  workspaceId: string
  planId: string
}

export const DailyPlanTask: React.FC<DailyPlanTaskProps> = ({
  planTask,
  dragHandleProps,
  onRemove,
  workspaceId,
  planId
}) => {
  const queryClient = useQueryClient()
  const [isEditing, setIsEditing] = useState(false)
  const [editedMinutes, setEditedMinutes] = useState(planTask.estimatedMinutes)
  const [isTracking, setIsTracking] = useState(false)
  const [startTime, setStartTime] = useState<Date | null>(null)

  const { task } = planTask
  const isCompleted = !!planTask.completedAt

  // Mutation for completing task
  const completeMutation = useMutation({
    mutationFn: () => dailyPlanService.completeDailyPlanTask(workspaceId, planId, planTask.id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['daily-plan', workspaceId] })
    }
  })

  // Mutation for updating task estimation
  const updateMutation = useMutation({
    mutationFn: (estimatedMinutes: number) => 
      dailyPlanService.updateDailyPlanTask(workspaceId, planId, planTask.id, {
        estimatedMinutes
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['daily-plan', workspaceId] })
      setIsEditing(false)
    }
  })

  const handleComplete = () => {
    if (isTracking) {
      // Stop tracking and calculate actual time
      const actualMinutes = startTime 
        ? Math.round((Date.now() - startTime.getTime()) / 60000)
        : planTask.estimatedMinutes

      dailyPlanService.updateDailyPlanTask(workspaceId, planId, planTask.id, {
        actualMinutes,
        completedAt: new Date()
      }).then(() => {
        queryClient.invalidateQueries({ queryKey: ['daily-plan', workspaceId] })
      })

      setIsTracking(false)
      setStartTime(null)
    } else {
      completeMutation.mutate()
    }
  }

  const handleStartTracking = () => {
    setIsTracking(true)
    setStartTime(new Date())
  }

  const handleStopTracking = () => {
    if (startTime) {
      const actualMinutes = Math.round((Date.now() - startTime.getTime()) / 60000)
      dailyPlanService.updateDailyPlanTask(workspaceId, planId, planTask.id, {
        actualMinutes
      }).then(() => {
        queryClient.invalidateQueries({ queryKey: ['daily-plan', workspaceId] })
      })
    }
    setIsTracking(false)
    setStartTime(null)
  }

  const handleSaveEdit = () => {
    if (editedMinutes !== planTask.estimatedMinutes) {
      updateMutation.mutate(editedMinutes)
    } else {
      setIsEditing(false)
    }
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  const getTrackingTime = () => {
    if (!startTime) return 0
    return Math.round((Date.now() - startTime.getTime()) / 60000)
  }

  return (
    <Card className={`transition-all ${isCompleted ? 'opacity-75 bg-green-50' : 'hover:shadow-md'}`}>
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          {/* Drag Handle */}
          <div {...dragHandleProps} className="cursor-move mt-1">
            <GripVertical className="h-4 w-4 text-gray-400" />
          </div>

          {/* Completion Checkbox */}
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-auto mt-1"
            onClick={handleComplete}
            disabled={completeMutation.isPending}
          >
            {isCompleted ? (
              <CheckCircle2 className="h-5 w-5 text-green-600" />
            ) : (
              <Circle className="h-5 w-5 text-gray-400 hover:text-green-600" />
            )}
          </Button>

          {/* Task Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className={`font-medium ${isCompleted ? 'line-through text-gray-500' : ''}`}>
                  {task.title}
                </h4>
                {task.description && (
                  <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                    {task.description}
                  </p>
                )}
                
                {/* Task Metadata */}
                <div className="flex items-center space-x-2 mt-2">
                  <TaskStatusBadge status={task.status} />
                  {task.priority && <TaskPriorityBadge priority={task.priority} />}
                  {task.projectName && (
                    <Badge variant="outline" className="text-xs">
                      {task.projectName}
                    </Badge>
                  )}
                  {task.dueDate && (
                    <Badge variant="outline" className="text-xs">
                      Due {format(new Date(task.dueDate), 'MMM d')}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-1 ml-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => window.open(task.sourceUrl, '_blank')}
                  className="p-1 h-auto"
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRemove}
                  className="p-1 h-auto text-red-600 hover:text-red-700"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Time Tracking */}
            <div className="flex items-center justify-between mt-3 pt-3 border-t">
              <div className="flex items-center space-x-4">
                {/* Estimated Time */}
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  {isEditing ? (
                    <div className="flex items-center space-x-2">
                      <Input
                        type="number"
                        value={editedMinutes}
                        onChange={(e) => setEditedMinutes(parseInt(e.target.value) || 0)}
                        className="w-16 h-6 text-xs"
                        min="1"
                      />
                      <span className="text-xs text-gray-500">min</span>
                      <Button
                        size="sm"
                        onClick={handleSaveEdit}
                        className="h-6 px-2 text-xs"
                      >
                        Save
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setIsEditing(false)
                          setEditedMinutes(planTask.estimatedMinutes)
                        }}
                        className="h-6 px-2 text-xs"
                      >
                        Cancel
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1">
                      <span className="text-sm text-gray-600">
                        {formatDuration(planTask.estimatedMinutes)} estimated
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsEditing(true)}
                        className="p-0 h-auto"
                      >
                        <Edit3 className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>

                {/* Actual Time */}
                {planTask.actualMinutes && (
                  <div className="text-sm text-gray-600">
                    {formatDuration(planTask.actualMinutes)} actual
                  </div>
                )}

                {/* Live Tracking */}
                {isTracking && (
                  <div className="text-sm text-blue-600 font-medium">
                    {formatDuration(getTrackingTime())} tracking
                  </div>
                )}
              </div>

              {/* Time Tracking Controls */}
              {!isCompleted && (
                <div className="flex items-center space-x-2">
                  {isTracking ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleStopTracking}
                      className="h-7 px-2 text-xs"
                    >
                      <Pause className="h-3 w-3 mr-1" />
                      Stop
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleStartTracking}
                      className="h-7 px-2 text-xs"
                    >
                      <Play className="h-3 w-3 mr-1" />
                      Start
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}