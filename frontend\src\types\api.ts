// Common API response types
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
}

export interface ApiError {
  error: {
    code: string
    message: string
    details?: Record<string, any>
    timestamp: string
    requestId: string
  }
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Auth types
export interface LoginRequest {
  email: string
  password: string
}

export interface SignupRequest {
  name: string
  email: string
  password: string
}

export interface AuthResponse {
  user: {
    id: string
    email: string
    name: string
    avatar?: string
    createdAt: string
    updatedAt: string
  }
  token: string
}

export interface OAuthCallbackRequest {
  code: string
  state: string
  provider: string
}

// Integration types
export enum IntegrationProvider {
  ASANA = 'asana',
  TRELLO = 'trello',
  JIRA = 'jira',
  CLICKUP = 'clickup',
  MONDAY = 'monday',
  TODOIST = 'todoist',
  NOTION = 'notion',
  GOOGLE_SHEETS = 'google_sheets'
}

export enum IntegrationStatus {
  ACTIVE = 'ACTIVE',
  ERROR = 'ERROR',
  DISABLED = 'DISABLED',
  EXPIRED = 'EXPIRED'
}

export enum SyncStatus {
  SYNCED = 'synced',
  PENDING = 'pending',
  ERROR = 'error',
  CONFLICT = 'conflict'
}

export interface FieldMapping {
  localField: string
  externalField: string
  transform?: 'uppercase' | 'lowercase' | 'date' | 'number' | 'boolean'
  defaultValue?: any
}

export interface SyncFilter {
  field: string
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'in' | 'notIn'
  value: any
}

export interface IntegrationConfig {
  syncInterval: number
  enableTwoWaySync: boolean
  fieldMappings: FieldMapping[]
  filters: SyncFilter[]
  webhookEnabled?: boolean
  customSettings?: Record<string, any>
}

export interface Integration {
  id: string
  workspaceId: string
  provider: IntegrationProvider
  name: string
  config: IntegrationConfig
  status: IntegrationStatus
  lastSyncAt?: string
  createdAt: string
  updatedAt: string
}

export interface CreateIntegrationRequest {
  provider: IntegrationProvider
  name: string
  config: IntegrationConfig
  oauthCode?: string
  oauthState?: string
}

export interface UpdateIntegrationRequest {
  name?: string
  config?: Partial<IntegrationConfig>
}

export interface SyncResult {
  success: boolean
  tasksProcessed: number
  tasksCreated: number
  tasksUpdated: number
  tasksDeleted: number
  errors: Array<{
    type: string
    message: string
    retryable: boolean
    timestamp: string
  }>
  conflicts: Array<{
    taskId: string
    field: string
    localValue: any
    remoteValue: any
    lastSyncAt: string
    conflictedAt: string
  }>
  duration: number
}

export interface IntegrationStatusInfo {
  id: string
  status: IntegrationStatus
  lastSyncAt?: string
  lastSyncResult?: SyncResult
  nextSyncAt?: string
  errorCount: number
  isHealthy: boolean
}