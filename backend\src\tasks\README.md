# Task Prioritization System

This module implements a configurable task prioritization system that calculates priority scores based on multiple factors.

## Features

- **Configurable Weights**: Adjust the importance of different factors in priority calculation
- **Multiple Scoring Factors**: Due date proximity, effort estimate, business impact, and context switching
- **Workspace-Level Settings**: Each workspace can have its own prioritization configuration
- **Real-time Recalculation**: Priority scores are updated when tasks or settings change

## Priority Factors

### 1. Due Date Proximity (Default Weight: 40%)
- Overdue tasks get maximum urgency (score = 1.0)
- Tasks due within the urgency window get scaled scores
- Tasks due far in the future get lower scores

### 2. Effort Estimate (Default Weight: 20%)
- Quick tasks (≤30 min) get high scores (easy wins)
- Medium tasks (≤120 min) get medium-high scores
- Large tasks (≤480 min) get medium scores
- Very large tasks get low scores

### 3. Business Impact (Default Weight: 30%)
- Based on explicit priority levels (critical, high, medium, low)
- Boosted by high-impact tags (urgent, critical, client-facing, etc.)
- Configurable business impact tags per workspace

### 4. Context Switching (Default Weight: 10%)
- Reduces penalty for tasks in the same project/context
- Considers assignee and project grouping
- Helps minimize context switching overhead

## API Endpoints

### Get Tasks by Priority
```
GET /workspaces/:workspaceId/tasks
```
Returns tasks ordered by priority score (highest first).

### Get Prioritization Settings
```
GET /workspaces/:workspaceId/tasks/prioritization/settings
```
Returns the current prioritization settings for the workspace.

### Update Prioritization Settings
```
PUT /workspaces/:workspaceId/tasks/prioritization/settings
```
Updates the prioritization settings and recalculates all task scores.

### Recalculate Priority Scores
```
POST /workspaces/:workspaceId/tasks/prioritization/recalculate
```
Manually triggers recalculation of all task priority scores.

### Update Single Task Priority
```
PUT /workspaces/:workspaceId/tasks/:taskId/priority
```
Updates the priority score for a specific task.

## Configuration Example

```json
{
  "weights": {
    "dueDateProximity": 0.4,
    "effortEstimate": 0.2,
    "businessImpact": 0.3,
    "contextSwitching": 0.1
  },
  "maxScore": 100,
  "dueDateUrgencyDays": 7,
  "effortThresholds": {
    "quick": 30,
    "medium": 120,
    "large": 480
  },
  "businessImpactTags": ["urgent", "critical", "high-priority", "client-facing"],
  "contextSwitchingPenalty": 0.1
}
```

## Usage

The prioritization system is automatically used when:
- Tasks are synced from external sources
- Task details are updated (due date, priority, etc.)
- Workspace prioritization settings are changed

Priority scores range from 0 to 100, with higher scores indicating higher priority tasks.

## Testing

The system includes comprehensive unit tests covering:
- Priority score calculations for various scenarios
- Weight validation and normalization
- Edge cases and error handling
- Integration with workspace settings

Run tests with:
```bash
npm test -- --testPathPattern="tasks"
```