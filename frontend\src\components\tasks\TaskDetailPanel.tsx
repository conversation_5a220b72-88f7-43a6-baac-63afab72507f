import { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { X, ExternalLink, Save, Edit, Calendar, Clock, User, Tag, Building2, Link } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { TaskStatusBadge } from './TaskStatusBadge'
import { TaskPriorityBadge } from './TaskPriorityBadge'
import { TaskSyncStatusBadge } from './TaskSyncStatusBadge'
import { Task, TaskStatus, TaskPriority, type UpdateTaskRequest } from '@/types/task'
import { cn } from '@/lib/utils'

interface TaskDetailPanelProps {
  task: Task | null
  isOpen: boolean
  onClose: () => void
  onUpdate: (taskId: string, updates: UpdateTaskRequest) => Promise<void>
  onDelete: (taskId: string) => Promise<void>
  isLoading?: boolean
  className?: string
}

export function TaskDetailPanel({
  task,
  isOpen,
  onClose,
  onUpdate,
  onDelete,
  isLoading = false,
  className
}: TaskDetailPanelProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editData, setEditData] = useState<UpdateTaskRequest>({})
  const [isSaving, setIsSaving] = useState(false)

  // Reset edit state when task changes
  useEffect(() => {
    if (task) {
      setEditData({
        title: task.title,
        description: task.description || '',
        status: task.status,
        priority: task.priority,
        assigneeName: task.assigneeName || '',
        dueDate: task.dueDate || '',
        estimatedMinutes: task.estimatedMinutes || undefined,
        tags: task.tags,
        projectName: task.projectName || ''
      })
    }
    setIsEditing(false)
  }, [task])

  if (!isOpen || !task) {
    return null
  }

  const handleSave = async () => {
    if (!task) return

    setIsSaving(true)
    try {
      // Filter out unchanged values
      const updates: UpdateTaskRequest = {}
      
      if (editData.title !== task.title) updates.title = editData.title
      if (editData.description !== (task.description || '')) updates.description = editData.description
      if (editData.status !== task.status) updates.status = editData.status
      if (editData.priority !== task.priority) updates.priority = editData.priority
      if (editData.assigneeName !== (task.assigneeName || '')) updates.assigneeName = editData.assigneeName
      if (editData.dueDate !== (task.dueDate || '')) updates.dueDate = editData.dueDate
      if (editData.estimatedMinutes !== task.estimatedMinutes) updates.estimatedMinutes = editData.estimatedMinutes
      if (JSON.stringify(editData.tags) !== JSON.stringify(task.tags)) updates.tags = editData.tags
      if (editData.projectName !== (task.projectName || '')) updates.projectName = editData.projectName

      if (Object.keys(updates).length > 0) {
        await onUpdate(task.id, updates)
      }
      
      setIsEditing(false)
    } catch (error) {
      console.error('Failed to update task:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    setEditData({
      title: task.title,
      description: task.description || '',
      status: task.status,
      priority: task.priority,
      assigneeName: task.assigneeName || '',
      dueDate: task.dueDate || '',
      estimatedMinutes: task.estimatedMinutes || undefined,
      tags: task.tags,
      projectName: task.projectName || ''
    })
    setIsEditing(false)
  }

  const canEdit = task.syncStatus !== 'pending' && !isLoading

  return (
    <div className={cn(
      'fixed inset-y-0 right-0 w-96 bg-white shadow-xl border-l z-50 transform transition-transform duration-300',
      isOpen ? 'translate-x-0' : 'translate-x-full',
      className
    )}>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">Task Details</h2>
          <div className="flex items-center gap-2">
            {canEdit && !isEditing && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
                disabled={isLoading}
              >
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          {/* Title */}
          <div>
            <Label htmlFor="task-title">Title</Label>
            {isEditing ? (
              <Input
                id="task-title"
                value={editData.title || ''}
                onChange={(e) => setEditData(prev => ({ ...prev, title: e.target.value }))}
                className="mt-1"
              />
            ) : (
              <h3 className="text-lg font-medium mt-1">{task.title}</h3>
            )}
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="task-description">Description</Label>
            {isEditing ? (
              <Textarea
                id="task-description"
                value={editData.description || ''}
                onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
                rows={4}
                className="mt-1"
              />
            ) : (
              <p className="text-sm text-gray-600 mt-1 whitespace-pre-wrap">
                {task.description || 'No description'}
              </p>
            )}
          </div>

          {/* Status and Priority */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="task-status">Status</Label>
              {isEditing ? (
                <Select
                  value={editData.status || ''}
                  onValueChange={(value) => setEditData(prev => ({ ...prev, status: value as TaskStatus }))}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={TaskStatus.TODO}>To Do</SelectItem>
                    <SelectItem value={TaskStatus.IN_PROGRESS}>In Progress</SelectItem>
                    <SelectItem value={TaskStatus.DONE}>Done</SelectItem>
                    <SelectItem value={TaskStatus.CANCELLED}>Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <div className="mt-1">
                  <TaskStatusBadge status={task.status} />
                </div>
              )}
            </div>

            <div>
              <Label htmlFor="task-priority">Priority</Label>
              {isEditing ? (
                <Select
                  value={editData.priority || ''}
                  onValueChange={(value) => setEditData(prev => ({ ...prev, priority: value as TaskPriority }))}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={TaskPriority.LOW}>Low</SelectItem>
                    <SelectItem value={TaskPriority.MEDIUM}>Medium</SelectItem>
                    <SelectItem value={TaskPriority.HIGH}>High</SelectItem>
                    <SelectItem value={TaskPriority.URGENT}>Urgent</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <div className="mt-1">
                  <TaskPriorityBadge 
                    priority={task.priority} 
                    priorityScore={task.priorityScore}
                    showIcon
                  />
                </div>
              )}
            </div>
          </div>

          {/* Assignee and Due Date */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="task-assignee">Assignee</Label>
              {isEditing ? (
                <Input
                  id="task-assignee"
                  value={editData.assigneeName || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, assigneeName: e.target.value }))}
                  className="mt-1"
                  placeholder="Unassigned"
                />
              ) : (
                <div className="mt-1 flex items-center gap-1 text-sm">
                  <User className="h-4 w-4 text-gray-400" />
                  {task.assigneeName || 'Unassigned'}
                </div>
              )}
            </div>

            <div>
              <Label htmlFor="task-due-date">Due Date</Label>
              {isEditing ? (
                <Input
                  id="task-due-date"
                  type="datetime-local"
                  value={editData.dueDate ? editData.dueDate.slice(0, 16) : ''}
                  onChange={(e) => setEditData(prev => ({ 
                    ...prev, 
                    dueDate: e.target.value ? `${e.target.value}:00Z` : '' 
                  }))}
                  className="mt-1"
                />
              ) : (
                <div className="mt-1 flex items-center gap-1 text-sm">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  {task.dueDate ? format(new Date(task.dueDate), 'MMM d, yyyy h:mm a') : 'No due date'}
                </div>
              )}
            </div>
          </div>

          {/* Estimated Time and Project */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="task-estimated-time">Estimated Time (minutes)</Label>
              {isEditing ? (
                <Input
                  id="task-estimated-time"
                  type="number"
                  min="0"
                  value={editData.estimatedMinutes || ''}
                  onChange={(e) => setEditData(prev => ({ 
                    ...prev, 
                    estimatedMinutes: e.target.value ? parseInt(e.target.value) : undefined 
                  }))}
                  className="mt-1"
                />
              ) : (
                <div className="mt-1 flex items-center gap-1 text-sm">
                  <Clock className="h-4 w-4 text-gray-400" />
                  {task.estimatedMinutes ? `${task.estimatedMinutes}m` : 'Not estimated'}
                </div>
              )}
            </div>

            <div>
              <Label htmlFor="task-project">Project</Label>
              {isEditing ? (
                <Input
                  id="task-project"
                  value={editData.projectName || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, projectName: e.target.value }))}
                  className="mt-1"
                  placeholder="No project"
                />
              ) : (
                <div className="mt-1 flex items-center gap-1 text-sm">
                  <Building2 className="h-4 w-4 text-gray-400" />
                  {task.projectName || 'No project'}
                </div>
              )}
            </div>
          </div>

          {/* Tags */}
          <div>
            <Label htmlFor="task-tags">Tags</Label>
            {isEditing ? (
              <Input
                id="task-tags"
                value={editData.tags?.join(', ') || ''}
                onChange={(e) => setEditData(prev => ({ 
                  ...prev, 
                  tags: e.target.value ? e.target.value.split(',').map(tag => tag.trim()) : [] 
                }))}
                className="mt-1"
                placeholder="Enter tags separated by commas"
              />
            ) : (
              <div className="mt-1">
                {task.tags.length > 0 ? (
                  <div className="flex gap-1 flex-wrap">
                    <Tag className="h-4 w-4 text-gray-400 mt-0.5" />
                    {task.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center gap-1 text-sm text-gray-500">
                    <Tag className="h-4 w-4 text-gray-400" />
                    No tags
                  </div>
                )}
              </div>
            )}
          </div>

          <Separator />

          {/* Source Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Source Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {/* Integration */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Source</span>
                <Badge variant="outline">
                  {task.integration?.name || 'Unknown'} ({task.integration?.provider || 'unknown'})
                </Badge>
              </div>

              {/* Sync Status */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Sync Status</span>
                <TaskSyncStatusBadge syncStatus={task.syncStatus} />
              </div>

              {/* External Link */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">External Link</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(task.sourceUrl, '_blank')}
                  className="h-6"
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Open
                </Button>
              </div>

              {/* Last Sync */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Last Sync</span>
                <span className="text-xs text-gray-500">
                  {format(new Date(task.lastSyncAt), 'MMM d, h:mm a')}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Metadata */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Timestamps</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-600">Created</span>
                <span>{format(new Date(task.createdAt), 'MMM d, yyyy h:mm a')}</span>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-600">Updated</span>
                <span>{format(new Date(task.updatedAt), 'MMM d, yyyy h:mm a')}</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer with action buttons */}
        {isEditing && (
          <div className="border-t p-4 flex gap-2">
            <Button
              onClick={handleSave}
              disabled={isSaving}
              className="flex-1"
            >
              <Save className="h-4 w-4 mr-1" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isSaving}
            >
              Cancel
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}