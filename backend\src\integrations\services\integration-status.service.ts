import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '../../database/database.service';
import { IntegrationStatus, SyncError, SyncResult } from '../types';

/**
 * Service for tracking integration status and sync operations
 */
@Injectable()
export class IntegrationStatusService {
  private readonly logger = new Logger(IntegrationStatusService.name);

  constructor(private readonly prisma: DatabaseService) {}

  /**
   * Update integration status
   * @param integrationId Integration ID
   * @param status New status
   * @param error Optional error information
   */
  async updateStatus(
    integrationId: string,
    status: IntegrationStatus,
    error?: SyncError,
  ): Promise<void> {
    try {
      await this.prisma.integration.update({
        where: { id: integrationId },
        data: {
          status,
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Integration ${integrationId} status updated to ${status}`);

      // Log error if provided
      if (error) {
        await this.logSyncError(integrationId, error);
      }
    } catch (err) {
      this.logger.error(`Failed to update integration status:`, err);
      throw new Error('Failed to update integration status');
    }
  }

  /**
   * Update last sync timestamp
   * @param integrationId Integration ID
   * @param timestamp Sync timestamp
   */
  async updateLastSync(integrationId: string, timestamp: Date = new Date()): Promise<void> {
    try {
      await this.prisma.integration.update({
        where: { id: integrationId },
        data: {
          lastSyncAt: timestamp,
          updatedAt: new Date(),
        },
      });

      this.logger.debug(`Integration ${integrationId} last sync updated to ${timestamp.toISOString()}`);
    } catch (error) {
      this.logger.error(`Failed to update last sync timestamp:`, error);
      throw new Error('Failed to update last sync timestamp');
    }
  }

  /**
   * Log sync operation result
   * @param integrationId Integration ID
   * @param operation Sync operation type
   * @param result Sync operation result
   */
  async logSyncOperation(
    integrationId: string,
    operation: string,
    result: SyncResult,
  ): Promise<void> {
    try {
      await this.prisma.syncLog.create({
        data: {
          integrationId,
          operation,
          status: result.success ? 'SUCCESS' : 'ERROR',
          tasksProcessed: result.tasksProcessed,
          errors: result.errors as any, // Cast to any for JSON storage
          startedAt: new Date(Date.now() - result.duration),
          completedAt: new Date(),
        },
      });

      this.logger.log(
        `Sync operation logged: ${operation} for integration ${integrationId} - ` +
        `${result.tasksProcessed} tasks processed, ${result.errors.length} errors`,
      );

      // Update integration status based on result
      if (!result.success && result.errors.length > 0) {
        const hasAuthError = result.errors.some(error => error.type === 'AUTH_ERROR');
        const newStatus = hasAuthError ? IntegrationStatus.EXPIRED : IntegrationStatus.ERROR;
        await this.updateStatus(integrationId, newStatus);
      } else if (result.success) {
        await this.updateStatus(integrationId, IntegrationStatus.ACTIVE);
        await this.updateLastSync(integrationId);
      }
    } catch (error) {
      this.logger.error(`Failed to log sync operation:`, error);
      // Don't throw here to avoid breaking the sync process
    }
  }

  /**
   * Log sync error
   * @param integrationId Integration ID
   * @param error Sync error
   */
  async logSyncError(integrationId: string, error: SyncError): Promise<void> {
    try {
      await this.prisma.syncLog.create({
        data: {
          integrationId,
          operation: 'ERROR',
          status: 'ERROR',
          tasksProcessed: 0,
          errors: [error] as any, // Cast to any for JSON storage
          startedAt: error.timestamp,
          completedAt: error.timestamp,
        },
      });

      this.logger.error(
        `Sync error logged for integration ${integrationId}: ${error.type} - ${error.message}`,
      );
    } catch (err) {
      this.logger.error(`Failed to log sync error:`, err);
    }
  }

  /**
   * Get integration status
   * @param integrationId Integration ID
   * @returns Integration status information
   */
  async getStatus(integrationId: string): Promise<{
    status: string;
    lastSyncAt: Date | null;
    recentErrors: SyncError[];
    syncHistory: any[];
  }> {
    try {
      const integration = await this.prisma.integration.findUnique({
        where: { id: integrationId },
        select: {
          status: true,
          lastSyncAt: true,
        },
      });

      if (!integration) {
        throw new Error('Integration not found');
      }

      // Get recent sync logs
      const recentLogs = await this.prisma.syncLog.findMany({
        where: { integrationId },
        orderBy: { createdAt: 'desc' },
        take: 10,
      });

      // Extract recent errors
      const recentErrors: SyncError[] = [];
      recentLogs.forEach((log: any) => {
        if (Array.isArray(log.errors)) {
          recentErrors.push(...log.errors);
        }
      });

      return {
        status: integration.status,
        lastSyncAt: integration.lastSyncAt,
        recentErrors: recentErrors.slice(0, 5), // Last 5 errors
        syncHistory: recentLogs.map((log: any) => ({
          operation: log.operation,
          status: log.status,
          tasksProcessed: log.tasksProcessed,
          startedAt: log.startedAt,
          completedAt: log.completedAt,
          errorCount: Array.isArray(log.errors) ? log.errors.length : 0,
        })),
      };
    } catch (error) {
      this.logger.error(`Failed to get integration status:`, error);
      throw new Error('Failed to get integration status');
    }
  }

  /**
   * Check if integration needs attention (has errors or is expired)
   * @param integrationId Integration ID
   * @returns True if integration needs attention
   */
  async needsAttention(integrationId: string): Promise<boolean> {
    try {
      const integration = await this.prisma.integration.findUnique({
        where: { id: integrationId },
        select: { status: true },
      });

      return (integration as any)?.status === IntegrationStatus.ERROR || 
             (integration as any)?.status === IntegrationStatus.EXPIRED;
    } catch (error) {
      this.logger.error(`Failed to check integration attention status:`, error);
      return true; // Assume needs attention if we can't check
    }
  }

  /**
   * Get integrations that need attention
   * @param workspaceId Workspace ID
   * @returns List of integration IDs that need attention
   */
  async getIntegrationsNeedingAttention(workspaceId: string): Promise<string[]> {
    try {
      const integrations = await this.prisma.integration.findMany({
        where: {
          workspaceId,
          status: {
            in: [IntegrationStatus.ERROR, IntegrationStatus.EXPIRED],
          },
        },
        select: { id: true },
      });

      return integrations.map(integration => integration.id);
    } catch (error) {
      this.logger.error(`Failed to get integrations needing attention:`, error);
      return [];
    }
  }

  /**
   * Clean up old sync logs
   * @param olderThanDays Remove logs older than this many days
   */
  async cleanupOldLogs(olderThanDays: number = 30): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const result = await this.prisma.syncLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
        },
      });

      this.logger.log(`Cleaned up ${result.count} old sync logs older than ${olderThanDays} days`);
    } catch (error) {
      this.logger.error(`Failed to cleanup old sync logs:`, error);
    }
  }
}