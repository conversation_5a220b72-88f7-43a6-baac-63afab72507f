import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { DatabaseService } from '../../database/database.service';
import { IntegrationAdapterRegistry } from './integration-adapter.registry';
import { CredentialEncryptionService } from './credential-encryption.service';
import { IntegrationStatusService } from './integration-status.service';
import {
  IntegrationProvider,
  IntegrationConfig,
  OAuthCredentials,
  IntegrationStatus,
  SyncResult,
  ExternalTask,
  TaskUpdate,
  CreateTaskRequest,
} from '../types';

/**
 * Main service for managing integrations
 * Orchestrates adapter operations, credential management, and sync operations
 */
@Injectable()
export class IntegrationService {
  private readonly logger = new Logger(IntegrationService.name);

  constructor(
    private readonly prisma: DatabaseService,
    private readonly adapterRegistry: IntegrationAdapterRegistry,
    private readonly credentialEncryption: CredentialEncryptionService,
    private readonly statusService: IntegrationStatusService,
  ) {}

  /**
   * Create a new integration
   * @param workspaceId Workspace ID
   * @param provider Integration provider
   * @param name Integration name
   * @param credentials OAuth credentials
   * @param config Integration configuration
   * @returns Created integration
   */
  async createIntegration(
    workspaceId: string,
    provider: IntegrationProvider,
    name: string,
    credentials: OAuthCredentials,
    config: IntegrationConfig,
  ): Promise<any> {
    try {
      // Validate that adapter exists
      const adapter = this.adapterRegistry.getAdapter(provider);

      // Validate credentials
      const isValid = await adapter.validateCredentials(credentials);
      if (!isValid) {
        throw new BadRequestException('Invalid credentials provided');
      }

      // Encrypt credentials
      const encryptedCredentials = this.credentialEncryption.encrypt(credentials);

      // Create integration record
      const integration = await this.prisma.integration.create({
        data: {
          workspaceId,
          provider,
          name,
          config: config as any, // Cast to any for JSON storage
          encryptedCredentials,
          status: IntegrationStatus.ACTIVE,
        },
      });

      this.logger.log(`Created integration ${integration.id} for provider ${provider}`);

      // Perform initial sync
      await this.syncIntegration(integration.id);

      return integration;
    } catch (error) {
      this.logger.error(`Failed to create integration:`, error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to create integration');
    }
  }

  /**
   * Get integration by ID
   * @param integrationId Integration ID
   * @returns Integration with decrypted credentials
   */
  async getIntegration(integrationId: string): Promise<any> {
    const integration = await this.prisma.integration.findUnique({
      where: { id: integrationId },
    });

    if (!integration) {
      throw new NotFoundException('Integration not found');
    }

    return {
      ...integration,
      credentials: this.credentialEncryption.decrypt(integration.encryptedCredentials),
    };
  }

  /**
   * Get integrations for a workspace
   * @param workspaceId Workspace ID
   * @returns List of integrations (without credentials)
   */
  async getWorkspaceIntegrations(workspaceId: string): Promise<any[]> {
    const integrations = await this.prisma.integration.findMany({
      where: { workspaceId },
      select: {
        id: true,
        provider: true,
        name: true,
        config: true,
        status: true,
        lastSyncAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return integrations;
  }

  /**
   * Update integration configuration
   * @param integrationId Integration ID
   * @param config New configuration
   * @returns Updated integration
   */
  async updateIntegrationConfig(
    integrationId: string,
    config: Partial<IntegrationConfig>,
  ): Promise<any> {
    try {
      const integration = await this.getIntegration(integrationId);

      const updatedConfig = {
        ...integration.config,
        ...config,
      };

      const updated = await this.prisma.integration.update({
        where: { id: integrationId },
        data: {
          config: updatedConfig as any, // Cast to any for JSON storage
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Updated configuration for integration ${integrationId}`);
      return updated;
    } catch (error) {
      this.logger.error(`Failed to update integration config:`, error);
      throw new BadRequestException('Failed to update integration configuration');
    }
  }

  /**
   * Update integration credentials
   * @param integrationId Integration ID
   * @param credentials New credentials
   * @returns Updated integration
   */
  async updateIntegrationCredentials(
    integrationId: string,
    credentials: OAuthCredentials,
  ): Promise<any> {
    try {
      const integration = await this.getIntegration(integrationId);
      const adapter = this.adapterRegistry.getAdapter(integration.provider);

      // Validate new credentials
      const isValid = await adapter.validateCredentials(credentials);
      if (!isValid) {
        throw new BadRequestException('Invalid credentials provided');
      }

      // Encrypt new credentials
      const encryptedCredentials = this.credentialEncryption.encrypt(credentials);

      const updated = await this.prisma.integration.update({
        where: { id: integrationId },
        data: {
          encryptedCredentials,
          status: IntegrationStatus.ACTIVE,
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Updated credentials for integration ${integrationId}`);

      // Perform sync with new credentials
      await this.syncIntegration(integrationId);

      return updated;
    } catch (error) {
      this.logger.error(`Failed to update integration credentials:`, error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to update integration credentials');
    }
  }

  /**
   * Delete integration
   * @param integrationId Integration ID
   */
  async deleteIntegration(integrationId: string): Promise<void> {
    try {
      await this.prisma.integration.delete({
        where: { id: integrationId },
      });

      this.logger.log(`Deleted integration ${integrationId}`);
    } catch (error) {
      this.logger.error(`Failed to delete integration:`, error);
      throw new BadRequestException('Failed to delete integration');
    }
  }

  /**
   * Sync integration tasks
   * @param integrationId Integration ID
   * @returns Sync result
   */
  async syncIntegration(integrationId: string): Promise<SyncResult> {
    const startTime = Date.now();
    let result: SyncResult = {
      success: false,
      tasksProcessed: 0,
      tasksCreated: 0,
      tasksUpdated: 0,
      tasksDeleted: 0,
      errors: [],
      conflicts: [],
      duration: 0,
    };

    try {
      const integration = await this.getIntegration(integrationId);
      const adapter = this.adapterRegistry.getAdapter(integration.provider);

      // Fetch tasks from external service
      const externalTasks = await adapter.fetchTasks(integration.config);
      result.tasksProcessed = externalTasks.length;

      // Process each task
      for (const externalTask of externalTasks) {
        try {
          await this.processExternalTask(integration, externalTask);
          result.tasksCreated++; // Simplified - would need to track actual creates vs updates
        } catch (error) {
          result.errors.push({
            type: 'API_ERROR',
            message: error.message || 'Failed to process task',
            retryable: true,
            timestamp: new Date(),
          });
        }
      }

      result.success = result.errors.length === 0;
      result.duration = Date.now() - startTime;

      // Log sync operation
      await this.statusService.logSyncOperation(integrationId, 'SYNC', result);

      this.logger.log(
        `Sync completed for integration ${integrationId}: ` +
        `${result.tasksProcessed} tasks processed, ${result.errors.length} errors`,
      );

      return result;
    } catch (error) {
      result.duration = Date.now() - startTime;
      result.errors.push({
        type: 'API_ERROR',
        message: error.message || 'Sync operation failed',
        retryable: true,
        timestamp: new Date(),
      });

      await this.statusService.logSyncOperation(integrationId, 'SYNC', result);
      
      this.logger.error(`Sync failed for integration ${integrationId}:`, error);
      return result;
    }
  }

  /**
   * Update task in external service
   * @param integrationId Integration ID
   * @param taskId Task ID
   * @param updates Task updates
   * @returns Updated external task
   */
  async updateExternalTask(
    integrationId: string,
    taskId: string,
    updates: TaskUpdate,
  ): Promise<ExternalTask> {
    try {
      const integration = await this.getIntegration(integrationId);
      const adapter = this.adapterRegistry.getAdapter(integration.provider);

      // Check if adapter supports two-way sync
      if (!adapter.supportsTwoWaySync()) {
        throw new BadRequestException('Integration does not support task updates');
      }

      const updatedTask = await adapter.updateTask(taskId, updates);

      this.logger.log(`Updated external task ${taskId} in integration ${integrationId}`);
      return updatedTask;
    } catch (error) {
      this.logger.error(`Failed to update external task:`, error);
      throw new BadRequestException('Failed to update external task');
    }
  }

  /**
   * Create task in external service
   * @param integrationId Integration ID
   * @param task Task creation request
   * @returns Created external task
   */
  async createExternalTask(
    integrationId: string,
    task: CreateTaskRequest,
  ): Promise<ExternalTask> {
    try {
      const integration = await this.getIntegration(integrationId);
      const adapter = this.adapterRegistry.getAdapter(integration.provider);

      const createdTask = await adapter.createTask(task);

      this.logger.log(`Created external task in integration ${integrationId}`);
      return createdTask;
    } catch (error) {
      this.logger.error(`Failed to create external task:`, error);
      throw new BadRequestException('Failed to create external task');
    }
  }

  /**
   * Get integration status
   * @param integrationId Integration ID
   * @returns Integration status information
   */
  async getIntegrationStatus(integrationId: string): Promise<any> {
    return this.statusService.getStatus(integrationId);
  }

  /**
   * Get adapter for a provider
   * @param provider Integration provider
   * @returns Integration adapter
   */
  getAdapter(provider: IntegrationProvider): any {
    return this.adapterRegistry.getAdapter(provider);
  }

  /**
   * Process external task and sync to local database
   * @param integration Integration record
   * @param externalTask External task data
   */
  private async processExternalTask(integration: any, externalTask: ExternalTask): Promise<void> {
    try {
      // Check if task already exists
      const existingTask = await this.prisma.task.findUnique({
        where: {
          integrationId_externalId: {
            integrationId: integration.id,
            externalId: externalTask.id,
          },
        },
      });

      if (existingTask) {
        // Update existing task
        await this.prisma.task.update({
          where: { id: existingTask.id },
          data: {
            title: externalTask.title,
            description: externalTask.description,
            status: externalTask.status,
            priority: externalTask.priority,
            assigneeId: externalTask.assigneeId,
            assigneeName: externalTask.assigneeName,
            dueDate: externalTask.dueDate,
            estimatedMinutes: externalTask.estimatedMinutes,
            tags: externalTask.tags || [],
            projectName: externalTask.projectName,
            sourceUrl: externalTask.sourceUrl,
            metadata: externalTask.metadata || {},
            syncStatus: 'SYNCED',
            lastSyncAt: new Date(),
            updatedAt: new Date(),
          },
        });
      } else {
        // Create new task
        await this.prisma.task.create({
          data: {
            workspaceId: integration.workspaceId,
            integrationId: integration.id,
            externalId: externalTask.id,
            title: externalTask.title,
            description: externalTask.description,
            status: externalTask.status,
            priority: externalTask.priority || 'medium',
            assigneeId: externalTask.assigneeId,
            assigneeName: externalTask.assigneeName,
            dueDate: externalTask.dueDate,
            estimatedMinutes: externalTask.estimatedMinutes,
            tags: externalTask.tags || [],
            projectName: externalTask.projectName,
            sourceUrl: externalTask.sourceUrl,
            metadata: externalTask.metadata || {},
            syncStatus: 'SYNCED',
            lastSyncAt: new Date(),
          },
        });
      }
    } catch (error) {
      this.logger.error(`Failed to process external task ${externalTask.id}:`, error);
      throw error;
    }
  }
}