import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { TasksModule } from '../tasks.module';
import { DatabaseModule } from '../../database/database.module';
import { DatabaseService } from '../../database/database.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { beforeEach } from 'node:test';
import { describe } from 'node:test';

describe('Progress Tracking API (e2e)', () => {
  let app: INestApplication;
  let prismaService: DatabaseService;

  const mockJwtAuthGuard = {
    canActivate: jest.fn(() => true),
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TasksModule, DatabaseModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    prismaService = moduleFixture.get<DatabaseService>(DatabaseService);
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Clean up database before each test
    await prismaService.task.deleteMany();
    await prismaService.integration.deleteMany();
    await prismaService.workspaceMember.deleteMany();
    await prismaService.workspace.deleteMany();
    await prismaService.user.deleteMany();
  });

  describe('GET /workspaces/:workspaceId/tasks/reports/completion-rate', () => {
    it('should return completion rate report', async () => {
      // Setup test data
      const user = await prismaService.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await prismaService.workspace.create({
        data: {
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const integration = await prismaService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'asana',
          name: 'Test Asana',
          config: {},
          encryptedCredentials: 'encrypted-creds',
          status: 'ACTIVE',
        },
      });

      // Create test tasks
      await prismaService.task.createMany({
        data: [
          {
            workspaceId: workspace.id,
            integrationId: integration.id,
            externalId: 'task-1',
            title: 'Completed Task',
            status: 'done',
            sourceUrl: 'https://example.com/task-1',
          },
          {
            workspaceId: workspace.id,
            integrationId: integration.id,
            externalId: 'task-2',
            title: 'Pending Task',
            status: 'todo',
            sourceUrl: 'https://example.com/task-2',
          },
        ],
      });

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspace.id}/tasks/reports/completion-rate`)
        .query({
          startDate: '2024-01-01',
          endDate: '2024-12-31',
        })
        .expect(200);

      expect(response.body).toHaveProperty('overall');
      expect(response.body.overall.totalTasks).toBe(2);
      expect(response.body.overall.completedTasks).toBe(1);
      expect(response.body.overall.completionRate).toBe(50);
      expect(response.body).toHaveProperty('bySource');
      expect(response.body).toHaveProperty('byProject');
      expect(response.body).toHaveProperty('byTimePeriod');
    });

    it('should apply filters correctly', async () => {
      // Setup test data with multiple sources
      const user = await prismaService.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await prismaService.workspace.create({
        data: {
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const asanaIntegration = await prismaService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'asana',
          name: 'Test Asana',
          config: {},
          encryptedCredentials: 'encrypted-creds',
          status: 'ACTIVE',
        },
      });

      const trelloIntegration = await prismaService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'trello',
          name: 'Test Trello',
          config: {},
          encryptedCredentials: 'encrypted-creds',
          status: 'ACTIVE',
        },
      });

      // Create tasks for different sources
      await prismaService.task.createMany({
        data: [
          {
            workspaceId: workspace.id,
            integrationId: asanaIntegration.id,
            externalId: 'asana-task-1',
            title: 'Asana Task',
            status: 'done',
            sourceUrl: 'https://example.com/asana-task-1',
          },
          {
            workspaceId: workspace.id,
            integrationId: trelloIntegration.id,
            externalId: 'trello-task-1',
            title: 'Trello Task',
            status: 'todo',
            sourceUrl: 'https://example.com/trello-task-1',
          },
        ],
      });

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspace.id}/tasks/reports/completion-rate`)
        .query({
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          source: 'asana',
        })
        .expect(200);

      expect(response.body.overall.totalTasks).toBe(1);
      expect(response.body.bySource).toHaveLength(1);
      expect(response.body.bySource[0].source).toBe('asana');
    });
  });

  describe('GET /workspaces/:workspaceId/tasks/reports/task-aging', () => {
    it('should return task aging report', async () => {
      // Setup test data
      const user = await prismaService.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await prismaService.workspace.create({
        data: {
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const integration = await prismaService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'asana',
          name: 'Test Asana',
          config: {},
          encryptedCredentials: 'encrypted-creds',
          status: 'ACTIVE',
        },
      });

      // Create tasks with different ages
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 45);

      const recentDate = new Date();
      recentDate.setDate(recentDate.getDate() - 3);

      await prismaService.task.createMany({
        data: [
          {
            workspaceId: workspace.id,
            integrationId: integration.id,
            externalId: 'old-task',
            title: 'Old Task',
            status: 'todo',
            sourceUrl: 'https://example.com/old-task',
            createdAt: oldDate,
          },
          {
            workspaceId: workspace.id,
            integrationId: integration.id,
            externalId: 'recent-task',
            title: 'Recent Task',
            status: 'in_progress',
            sourceUrl: 'https://example.com/recent-task',
            createdAt: recentDate,
          },
          {
            workspaceId: workspace.id,
            integrationId: integration.id,
            externalId: 'completed-task',
            title: 'Completed Task',
            status: 'done',
            sourceUrl: 'https://example.com/completed-task',
          },
        ],
      });

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspace.id}/tasks/reports/task-aging`)
        .query({
          startDate: '2024-01-01',
          endDate: '2024-12-31',
        })
        .expect(200);

      expect(response.body.totalActiveTasks).toBe(2); // Excludes completed task
      expect(response.body.tasksByAgeRange).toHaveLength(3);
      expect(response.body.tasksByAgeRange.some(range => range.taskCount > 0)).toBe(true);
    });

    it('should use custom age ranges when provided', async () => {
      // Setup minimal test data
      const user = await prismaService.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await prismaService.workspace.create({
        data: {
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const integration = await prismaService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'asana',
          name: 'Test Asana',
          config: {},
          encryptedCredentials: 'encrypted-creds',
          status: 'ACTIVE',
        },
      });

      await prismaService.task.create({
        data: {
          workspaceId: workspace.id,
          integrationId: integration.id,
          externalId: 'test-task',
          title: 'Test Task',
          status: 'todo',
          sourceUrl: 'https://example.com/test-task',
        },
      });

      const customAgeRanges = [
        { label: '0-5 days', minDays: 0, maxDays: 5 },
        { label: '6+ days', minDays: 6, maxDays: null },
      ];

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspace.id}/tasks/reports/task-aging`)
        .query({
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          ageRanges: JSON.stringify(customAgeRanges),
        })
        .expect(200);

      expect(response.body.tasksByAgeRange).toHaveLength(2);
      expect(response.body.tasksByAgeRange[0].ageRange.label).toBe('0-5 days');
      expect(response.body.tasksByAgeRange[1].ageRange.label).toBe('6+ days');
    });
  });

  describe('GET /workspaces/:workspaceId/tasks/reports/velocity', () => {
    it('should return velocity report', async () => {
      // Setup test data
      const user = await prismaService.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await prismaService.workspace.create({
        data: {
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const integration = await prismaService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'asana',
          name: 'Test Asana',
          config: {},
          encryptedCredentials: 'encrypted-creds',
          status: 'ACTIVE',
        },
      });

      // Create completed tasks
      const lastWeek = new Date();
      lastWeek.setDate(lastWeek.getDate() - 7);

      await prismaService.task.createMany({
        data: [
          {
            workspaceId: workspace.id,
            integrationId: integration.id,
            externalId: 'completed-task-1',
            title: 'Completed Task 1',
            status: 'done',
            sourceUrl: 'https://example.com/completed-task-1',
            estimatedMinutes: 60,
            updatedAt: lastWeek,
          },
          {
            workspaceId: workspace.id,
            integrationId: integration.id,
            externalId: 'completed-task-2',
            title: 'Completed Task 2',
            status: 'done',
            sourceUrl: 'https://example.com/completed-task-2',
            estimatedMinutes: 120,
            updatedAt: new Date(),
          },
        ],
      });

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspace.id}/tasks/reports/velocity`)
        .query({
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          weeksBack: 4,
        })
        .expect(200);

      expect(response.body).toHaveProperty('weeklyData');
      expect(response.body.weeklyData).toHaveLength(4);
      expect(response.body).toHaveProperty('averageTasksPerWeek');
      expect(response.body).toHaveProperty('trendAnalysis');
      expect(response.body.trendAnalysis).toHaveProperty('direction');
      expect(response.body).toHaveProperty('velocityBySource');
    });
  });

  describe('GET /workspaces/:workspaceId/tasks/reports/filter-options', () => {
    it('should return available filter options', async () => {
      // Setup test data
      const user = await prismaService.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await prismaService.workspace.create({
        data: {
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const integration = await prismaService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'asana',
          name: 'Test Asana',
          config: {},
          encryptedCredentials: 'encrypted-creds',
          status: 'ACTIVE',
        },
      });

      await prismaService.task.create({
        data: {
          workspaceId: workspace.id,
          integrationId: integration.id,
          externalId: 'test-task',
          title: 'Test Task',
          status: 'todo',
          sourceUrl: 'https://example.com/test-task',
          projectName: 'Test Project',
          assigneeId: 'user-123',
          assigneeName: 'John Doe',
        },
      });

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspace.id}/tasks/reports/filter-options`)
        .expect(200);

      expect(response.body).toHaveProperty('sources');
      expect(response.body).toHaveProperty('projects');
      expect(response.body).toHaveProperty('assignees');
      expect(response.body.sources).toHaveLength(1);
      expect(response.body.sources[0]).toEqual({
        value: 'asana',
        label: 'Test Asana',
      });
      expect(response.body.projects).toHaveLength(1);
      expect(response.body.projects[0]).toEqual({
        value: 'Test Project',
        label: 'Test Project',
      });
      expect(response.body.assignees).toHaveLength(1);
      expect(response.body.assignees[0]).toEqual({
        value: 'user-123',
        label: 'John Doe',
      });
    });
  });

  describe('POST /workspaces/:workspaceId/tasks/reports/export', () => {
    it('should export completion rate report as CSV', async () => {
      // Setup test data
      const user = await prismaService.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await prismaService.workspace.create({
        data: {
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const integration = await prismaService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'asana',
          name: 'Test Asana',
          config: {},
          encryptedCredentials: 'encrypted-creds',
          status: 'ACTIVE',
        },
      });

      await prismaService.task.create({
        data: {
          workspaceId: workspace.id,
          integrationId: integration.id,
          externalId: 'test-task',
          title: 'Test Task',
          status: 'done',
          sourceUrl: 'https://example.com/test-task',
        },
      });

      const response = await request(app.getHttpServer())
        .post(`/workspaces/${workspace.id}/tasks/reports/export`)
        .send({
          format: 'csv',
          reportType: 'completion',
          filters: JSON.stringify({
            startDate: '2024-01-01',
            endDate: '2024-12-31',
          }),
        })
        .expect(200);

      expect(response.headers['content-type']).toBe('text/csv; charset=utf-8');
      expect(response.headers['content-disposition']).toMatch(/attachment; filename="completion-report-\d{4}-\d{2}-\d{2}\.csv"/);
      expect(response.text).toContain('Report Type,Completion Rate Report');
    });

    it('should export task aging report as PDF placeholder', async () => {
      // Setup minimal test data
      const user = await prismaService.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await prismaService.workspace.create({
        data: {
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const integration = await prismaService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'asana',
          name: 'Test Asana',
          config: {},
          encryptedCredentials: 'encrypted-creds',
          status: 'ACTIVE',
        },
      });

      await prismaService.task.create({
        data: {
          workspaceId: workspace.id,
          integrationId: integration.id,
          externalId: 'test-task',
          title: 'Test Task',
          status: 'todo',
          sourceUrl: 'https://example.com/test-task',
        },
      });

      const response = await request(app.getHttpServer())
        .post(`/workspaces/${workspace.id}/tasks/reports/export`)
        .send({
          format: 'pdf',
          reportType: 'aging',
          filters: JSON.stringify({
            startDate: '2024-01-01',
            endDate: '2024-12-31',
          }),
        })
        .expect(200);

      expect(response.headers['content-type']).toBe('application/pdf; charset=utf-8');
      expect(response.headers['content-disposition']).toMatch(/attachment; filename="aging-report-\d{4}-\d{2}-\d{2}\.pdf"/);
      expect(response.text).toContain('PDF Report Placeholder');
    });

    it('should return 400 for invalid report type', async () => {
      const user = await prismaService.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await prismaService.workspace.create({
        data: {
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      await request(app.getHttpServer())
        .post(`/workspaces/${workspace.id}/tasks/reports/export`)
        .send({
          format: 'csv',
          reportType: 'invalid',
          filters: JSON.stringify({
            startDate: '2024-01-01',
            endDate: '2024-12-31',
          }),
        })
        .expect(400);
    });
  });
});