import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { 
  MoreHorizontal, 
  RefreshCw, 
  Settings, 
  Trash2, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  Zap,
  ExternalLink
} from 'lucide-react'
import { IntegrationService } from '@/services/integration'
import { Integration, IntegrationStatusInfo, SyncResult } from '@/types/api'
import { useWorkspaceStore } from '@/store/workspace'
import toast from 'react-hot-toast'
import { formatDistanceToNow } from 'date-fns'

interface IntegrationManagementProps {
  onEditIntegration: (integration: Integration) => void
  onDeleteIntegration: (integration: Integration) => void
}

export function IntegrationManagement({ 
  onEditIntegration, 
  onDeleteIntegration 
}: IntegrationManagementProps) {
  const { currentWorkspace } = useWorkspaceStore()
  const [integrations, setIntegrations] = useState<Integration[]>([])
  const [integrationStatuses, setIntegrationStatuses] = useState<Record<string, IntegrationStatusInfo>>({})
  const [loading, setLoading] = useState(true)
  const [syncingIntegrations, setSyncingIntegrations] = useState<Set<string>>(new Set())

  useEffect(() => {
    if (currentWorkspace) {
      loadIntegrations()
    }
  }, [currentWorkspace])

  const loadIntegrations = async () => {
    if (!currentWorkspace) return

    try {
      setLoading(true)
      const integrationsData = await IntegrationService.getWorkspaceIntegrations(currentWorkspace.id)
      setIntegrations(integrationsData)
      
      // Load status for each integration
      const statusPromises = integrationsData.map(async (integration) => {
        try {
          const status = await IntegrationService.getIntegrationStatus(currentWorkspace.id, integration.id)
          return { id: integration.id, status }
        } catch (error) {
          console.error(`Failed to load status for integration ${integration.id}:`, error)
          return null
        }
      })

      const statuses = await Promise.all(statusPromises)
      const statusMap: Record<string, IntegrationStatusInfo> = {}
      statuses.forEach(result => {
        if (result) {
          statusMap[result.id] = result.status
        }
      })
      setIntegrationStatuses(statusMap)
    } catch (error) {
      toast.error('Failed to load integrations')
      console.error('Error loading integrations:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSync = async (integration: Integration) => {
    if (!currentWorkspace) return

    try {
      setSyncingIntegrations(prev => new Set(prev).add(integration.id))
      const result = await IntegrationService.syncIntegration(currentWorkspace.id, integration.id)
      
      if (result.success) {
        toast.success(`Synced ${result.tasksProcessed} tasks from ${integration.name}`)
      } else {
        toast.error(`Sync failed: ${result.errors[0]?.message || 'Unknown error'}`)
      }
      
      // Reload integrations to get updated sync status
      await loadIntegrations()
    } catch (error) {
      toast.error('Failed to sync integration')
      console.error('Sync error:', error)
    } finally {
      setSyncingIntegrations(prev => {
        const newSet = new Set(prev)
        newSet.delete(integration.id)
        return newSet
      })
    }
  }

  const handleTestConnection = async (integration: Integration) => {
    if (!currentWorkspace) return

    try {
      const result = await IntegrationService.testConnection(currentWorkspace.id, integration.id)
      
      if (result.success) {
        toast.success(`Connection to ${integration.name} is working`)
      } else {
        toast.error(`Connection failed: ${result.message}`)
      }
    } catch (error) {
      toast.error('Failed to test connection')
      console.error('Connection test error:', error)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'ERROR':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case 'DISABLED':
        return <Clock className="h-4 w-4 text-gray-500" />
      case 'EXPIRED':
        return <AlertCircle className="h-4 w-4 text-orange-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
      case 'ERROR':
        return <Badge variant="destructive">Error</Badge>
      case 'DISABLED':
        return <Badge variant="secondary">Disabled</Badge>
      case 'EXPIRED':
        return <Badge variant="outline" className="border-orange-500 text-orange-700">Expired</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  const getProviderDisplayName = (provider: string) => {
    const names: Record<string, string> = {
      asana: 'Asana',
      trello: 'Trello',
      jira: 'Jira',
      clickup: 'ClickUp',
      monday: 'Monday.com',
      todoist: 'Todoist',
      notion: 'Notion',
      google_sheets: 'Google Sheets'
    }
    return names[provider] || provider
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(i => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (integrations.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Zap className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No integrations yet</h3>
          <p className="text-gray-600 text-center mb-4">
            Connect your favorite tools to start aggregating tasks
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {integrations.map((integration) => {
        const status = integrationStatuses[integration.id]
        const isSyncing = syncingIntegrations.has(integration.id)
        
        return (
          <Card key={integration.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(integration.status)}
                    <div>
                      <CardTitle className="text-base">{integration.name}</CardTitle>
                      <CardDescription>
                        {getProviderDisplayName(integration.provider)}
                      </CardDescription>
                    </div>
                  </div>
                  {getStatusBadge(integration.status)}
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleSync(integration)} disabled={isSyncing}>
                      <RefreshCw className={`h-4 w-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
                      {isSyncing ? 'Syncing...' : 'Sync Now'}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleTestConnection(integration)}>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Test Connection
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onEditIntegration(integration)}>
                      <Settings className="h-4 w-4 mr-2" />
                      Settings
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => onDeleteIntegration(integration)}
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">Sync Interval</p>
                  <p className="font-medium">{integration.config.syncInterval} min</p>
                </div>
                <div>
                  <p className="text-gray-600">Two-way Sync</p>
                  <p className="font-medium">
                    {integration.config.enableTwoWaySync ? 'Enabled' : 'Disabled'}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600">Last Sync</p>
                  <p className="font-medium">
                    {integration.lastSyncAt 
                      ? formatDistanceToNow(new Date(integration.lastSyncAt), { addSuffix: true })
                      : 'Never'
                    }
                  </p>
                </div>
                <div>
                  <p className="text-gray-600">Tasks Processed</p>
                  <p className="font-medium">
                    {status?.lastSyncResult?.tasksProcessed || 0}
                  </p>
                </div>
              </div>

              {status?.lastSyncResult && status.lastSyncResult.errors.length > 0 && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                    <p className="text-sm text-red-700 font-medium">
                      {status.lastSyncResult.errors.length} sync error(s)
                    </p>
                  </div>
                  <p className="text-sm text-red-600 mt-1">
                    {status.lastSyncResult.errors[0].message}
                  </p>
                </div>
              )}

              {status?.lastSyncResult && status.lastSyncResult.conflicts.length > 0 && (
                <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-md">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4 text-orange-500" />
                    <p className="text-sm text-orange-700 font-medium">
                      {status.lastSyncResult.conflicts.length} conflict(s) detected
                    </p>
                  </div>
                  <p className="text-sm text-orange-600 mt-1">
                    Some tasks have conflicting changes that need resolution
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}