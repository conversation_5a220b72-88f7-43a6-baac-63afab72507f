# Asana Integration Adapter

The Asana adapter allows TaskUnify to synchronize tasks with Asana projects and workspaces, providing comprehensive two-way sync capabilities and real-time webhook support.

## Features

- **OAuth Authentication**: Secure authentication using Asana OAuth2
- **Workspace & Project Filtering**: Sync tasks from specific workspaces or projects
- **Two-way Sync**: Read tasks from Asana and write updates back
- **Real-time Webhooks**: Receive instant notifications for task changes
- **Custom Field Support**: Handle Asana custom fields for priority and time estimates
- **Comprehensive Error Handling**: Robust error handling with retry logic

## Configuration

The adapter requires the following configuration:

```typescript
const config: IntegrationConfig = {
  syncInterval: 15, // minutes
  enableTwoWaySync: true,
  fieldMappings: [
    { localField: 'title', externalField: 'name' },
    { localField: 'description', externalField: 'notes' },
    { localField: 'status', externalField: 'completed' },
    { localField: 'dueDate', externalField: 'due_date' },
    { localField: 'assigneeId', externalField: 'assignee.gid' },
  ],
  filters: [
    {
      field: 'status',
      operator: 'in',
      value: ['todo', 'in_progress'],
    },
  ],
  customSettings: {
    credentials: {
      accessToken: 'oauth-access-token',
      refreshToken: 'oauth-refresh-token',
      expiresAt: new Date('2024-12-31T23:59:59Z'),
      userId: 'asana-user-id',
      userEmail: '<EMAIL>',
    },
    workspaceIds: ['workspace-1', 'workspace-2'], // Optional
    projectIds: ['project-1', 'project-2'], // Optional
  },
};
```

## Environment Variables

Set these environment variables for OAuth configuration:

```bash
ASANA_CLIENT_ID=your-asana-client-id
ASANA_CLIENT_SECRET=your-asana-client-secret
ASANA_CALLBACK_URL=http://localhost:3001/integrations/asana/callback
```

## Field Mapping

The adapter maps TaskUnify fields to Asana fields as follows:

| TaskUnify Field | Asana Field | Notes |
|----------------|-------------|-------|
| id | gid | Asana's global identifier |
| title | name | Task title/name |
| description | notes | Task description/notes |
| status | completed | Boolean: true = done, false = todo/in_progress |
| priority | custom_fields | Extracted from Priority custom field |
| assigneeId | assignee.gid | Asana user ID |
| assigneeName | assignee.name | Asana user display name |
| dueDate | due_date, due_time | Combined date and time |
| estimatedMinutes | custom_fields | From time/estimate custom fields (converted from hours) |
| tags | tags | Array of tag names |
| projectName | projects[0].name | Primary project name |
| sourceUrl | permalink_url | Direct link to task in Asana |

## Supported Operations

### Authentication
```typescript
const authResult = await adapter.authenticate(credentials);
// Returns: { success: boolean, user?: User, error?: string }
```

### Fetch Tasks
```typescript
const tasks = await adapter.fetchTasks(config);
// Returns: ExternalTask[]
```

### Update Task
```typescript
const updatedTask = await adapter.updateTask(taskId, {
  title: 'New Title',
  status: TaskStatus.DONE,
  dueDate: new Date('2024-01-15'),
  assigneeId: 'user-123',
});
```

### Create Task
```typescript
const newTask = await adapter.createTask({
  title: 'New Task',
  description: 'Task description',
  priority: TaskPriority.HIGH,
  dueDate: new Date('2024-01-15'),
  projectId: 'project-123',
});
```

### Setup Webhook
```typescript
const webhook = await adapter.setupWebhook('https://your-app.com/webhook');
// Returns: { id: string, url: string, events: string[], active: boolean }
```

### Validate Credentials
```typescript
const isValid = await adapter.validateCredentials(credentials);
// Returns: boolean
```

## Webhook Events

The adapter supports the following webhook events:

- `task.added` - New task created
- `task.changed` - Task updated
- `task.deleted` - Task deleted

Webhook payload includes the task GID and change information.

## Error Handling

The adapter handles various error scenarios:

### Authentication Errors (401, 403)
- Invalid or expired access tokens
- Insufficient permissions
- **Action**: Requires user to re-authenticate

### Rate Limiting (429)
- Asana API rate limits exceeded
- **Action**: Automatic retry with exponential backoff
- **Retry-After**: Respects Asana's retry-after header

### Network Errors
- Connection timeouts
- DNS resolution failures
- **Action**: Automatic retry with 30-second delay

### Validation Errors (400)
- Invalid request data
- Missing required fields
- **Action**: Log error, no retry

### Server Errors (500+)
- Asana API server issues
- **Action**: Automatic retry with 1-minute delay

## Custom Fields Support

The adapter automatically detects and maps common custom fields:

### Priority Fields
- Field names containing "priority"
- Maps enum values to TaskPriority (low, medium, high, urgent)

### Time Estimate Fields
- Field names containing "estimate", "time", or "hours"
- Converts hours to minutes for TaskUnify

### Example Custom Field Mapping
```typescript
// Asana custom field
{
  gid: 'field-123',
  name: 'Priority',
  type: 'enum',
  enum_value: {
    gid: 'enum-456',
    name: 'High'
  }
}

// Maps to TaskPriority.HIGH
```

## Filtering and Sync Options

### Workspace-Specific Sync
```typescript
customSettings: {
  workspaceIds: ['workspace-1', 'workspace-2'],
  // Syncs all projects in specified workspaces
}
```

### Project-Specific Sync
```typescript
customSettings: {
  projectIds: ['project-1', 'project-2'],
  // Syncs only specified projects
}
```

### Status Filtering
```typescript
filters: [
  {
    field: 'status',
    operator: 'in',
    value: [TaskStatus.TODO, TaskStatus.IN_PROGRESS],
  },
]
```

### Tag Filtering
```typescript
filters: [
  {
    field: 'tags',
    operator: 'contains',
    value: 'urgent',
  },
]
```

## Usage Example

```typescript
import { AsanaAdapter } from './asana.adapter';

const adapter = new AsanaAdapter();

// 1. Authenticate
const authResult = await adapter.authenticate(credentials);
if (!authResult.success) {
  throw new Error('Authentication failed');
}

// 2. Fetch tasks
const tasks = await adapter.fetchTasks(config);
console.log(`Fetched ${tasks.length} tasks`);

// 3. Create a task
const newTask = await adapter.createTask({
  title: 'Review PR #123',
  description: 'Review the new feature implementation',
  priority: TaskPriority.HIGH,
  dueDate: new Date('2024-01-15'),
  projectId: 'project-123',
});

// 4. Update task status
await adapter.updateTask(newTask.id, {
  status: TaskStatus.IN_PROGRESS,
});

// 5. Setup webhook for real-time updates
const webhook = await adapter.setupWebhook('https://app.taskunify.com/webhooks/asana');
```

## Testing

The adapter includes comprehensive unit tests covering:

- Authentication flows with various scenarios
- Task fetching with pagination and filtering
- Task creation and updates
- Error handling for all error types
- Webhook setup and configuration
- Custom field mapping
- Data validation and sanitization

Run tests with:
```bash
npm test -- asana.adapter.spec.ts
```

## API Rate Limits

Asana API has the following rate limits:
- **Standard**: 1,500 requests per minute
- **Premium**: 15,000 requests per minute

The adapter handles rate limiting automatically with:
- Exponential backoff retry strategy
- Respect for `Retry-After` headers
- Request batching where possible

## Limitations

### Read-Only Fields
Some Asana fields are read-only and cannot be updated via API:
- Task creation date
- Task completion date
- Task creator information

### Custom Field Limitations
- Only supports enum and number custom fields for priority/estimates
- Text custom fields are stored in metadata but not mapped to standard fields

### Webhook Limitations
- Webhooks are workspace-scoped, not project-scoped
- Webhook delivery is not guaranteed (implement idempotency)
- Maximum of 10 webhooks per workspace

### Project Access
- User must have access to projects for task sync
- Private projects require explicit permission
- Archived projects are excluded from sync

## Troubleshooting

### Common Issues

**Authentication Fails**
- Verify OAuth credentials are valid and not expired
- Check that the user has access to the specified workspaces/projects
- Ensure the access token has the required scopes

**No Tasks Returned**
- Verify workspace/project IDs are correct
- Check that the user has access to the specified resources
- Ensure tasks exist in the specified projects

**Webhook Not Receiving Events**
- Verify webhook URL is publicly accessible
- Check webhook is active in Asana
- Implement proper webhook signature verification

**Rate Limiting Issues**
- Reduce sync frequency
- Implement request batching
- Consider upgrading to Asana Premium for higher limits

### Debug Mode

Enable debug logging by setting the log level:
```typescript
// In your application configuration
LOG_LEVEL=debug
```

This will log all API requests and responses for troubleshooting.