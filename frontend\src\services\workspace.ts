import { apiService } from './api'
import { Workspace, WorkspaceMember } from '@/store/workspace'

export interface CreateWorkspaceRequest {
  name: string
  slug?: string
}

export interface UpdateWorkspaceRequest {
  name?: string
  settings?: {
    priorityWeights?: {
      dueDateProximity?: number
      effortEstimate?: number
      businessImpact?: number
      contextSwitching?: number
    }
    defaultSyncInterval?: number
    enableTwoWaySync?: boolean
  }
}

export interface InviteMemberRequest {
  email: string
  role: 'ADMIN' | 'MEMBER'
}

export interface UpdateMemberRequest {
  role?: 'ADMIN' | 'MEMBER'
  permissions?: string[]
}

export class WorkspaceService {
  async getWorkspaces(): Promise<Workspace[]> {
    return apiService.get<Workspace[]>('/workspaces')
  }

  async getWorkspace(id: string): Promise<Workspace> {
    return apiService.get<Workspace>(`/workspaces/${id}`)
  }

  async createWorkspace(data: CreateWorkspaceRequest): Promise<Workspace> {
    return apiService.post<Workspace>('/workspaces', data)
  }

  async updateWorkspace(id: string, data: UpdateWorkspaceRequest): Promise<Workspace> {
    return apiService.patch<Workspace>(`/workspaces/${id}`, data)
  }

  async deleteWorkspace(id: string): Promise<void> {
    return apiService.delete<void>(`/workspaces/${id}`)
  }

  async getMembers(workspaceId: string): Promise<WorkspaceMember[]> {
    return apiService.get<WorkspaceMember[]>(`/workspaces/${workspaceId}/members`)
  }

  async inviteMember(workspaceId: string, data: InviteMemberRequest): Promise<WorkspaceMember> {
    return apiService.post<WorkspaceMember>(`/workspaces/${workspaceId}/members`, data)
  }

  async updateMember(workspaceId: string, memberId: string, data: UpdateMemberRequest): Promise<WorkspaceMember> {
    return apiService.patch<WorkspaceMember>(`/workspaces/${workspaceId}/members/${memberId}`, data)
  }

  async removeMember(workspaceId: string, memberId: string): Promise<void> {
    return apiService.delete<void>(`/workspaces/${workspaceId}/members/${memberId}`)
  }
}

export const workspaceService = new WorkspaceService()