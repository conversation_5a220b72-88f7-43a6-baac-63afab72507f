import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import { OAuthCallbackPage } from '../OAuthCallbackPage'
import { IntegrationService } from '@/services/integration'
import { useWorkspaceStore } from '@/store/workspace'
import { IntegrationProvider } from '@/types/api'

// Mock dependencies
vi.mock('@/services/integration')
vi.mock('@/store/workspace')
vi.mock('react-hot-toast', () => ({
  default: {
    error: vi.fn(),
    success: vi.fn()
  }
}))

const mockIntegrationService = IntegrationService as any
const mockUseWorkspaceStore = useWorkspaceStore as any
const mockNavigate = vi.fn()

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useSearchParams: () => [new URLSearchParams('code=oauth-code&state=oauth-state')]
  }
})

const mockIntegration = {
  id: 'integration-1',
  workspaceId: 'workspace-1',
  provider: IntegrationProvider.ASANA,
  name: 'My Asana Integration',
  config: {
    syncInterval: 30,
    enableTwoWaySync: true,
    fieldMappings: [],
    filters: []
  },
  status: 'ACTIVE' as const,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

describe('OAuthCallbackPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    mockUseWorkspaceStore.mockReturnValue({
      currentWorkspace: {
        id: 'workspace-1',
        name: 'Test Workspace'
      }
    })

    // Mock sessionStorage
    Object.defineProperty(window, 'sessionStorage', {
      value: {
        getItem: vi.fn((key) => {
          const data = {
            'oauth_state': 'oauth-state',
            'oauth_provider': IntegrationProvider.ASANA,
            'oauth_integration_name': 'My Asana Integration',
            'oauth_config': JSON.stringify({
              syncInterval: 30,
              enableTwoWaySync: true,
              fieldMappings: [],
              filters: []
            })
          }
          return data[key] || null
        }),
        removeItem: vi.fn()
      },
      writable: true
    })

    mockIntegrationService.completeOAuth.mockResolvedValue(mockIntegration)
  })

  const renderWithRouter = (initialEntries = ['/integrations/oauth/callback?code=oauth-code&state=oauth-state']) => {
    return render(
      <MemoryRouter initialEntries={initialEntries}>
        <OAuthCallbackPage />
      </MemoryRouter>
    )
  }

  it('should show loading state initially', () => {
    renderWithRouter()

    expect(screen.getByText('Connecting Integration...')).toBeInTheDocument()
    expect(screen.getByText('Please wait while we set up your integration.')).toBeInTheDocument()
  })

  it('should complete OAuth flow successfully', async () => {
    renderWithRouter()

    await waitFor(() => {
      expect(mockIntegrationService.completeOAuth).toHaveBeenCalledWith(
        'workspace-1',
        IntegrationProvider.ASANA,
        'oauth-code',
        'oauth-state',
        'My Asana Integration',
        {
          syncInterval: 30,
          enableTwoWaySync: true,
          fieldMappings: [],
          filters: []
        }
      )
    })

    await waitFor(() => {
      expect(screen.getByText('Integration Connected!')).toBeInTheDocument()
    })
    
    expect(screen.getByText('Your integration has been successfully connected. Redirecting...')).toBeInTheDocument()
  })

  it('should clean up session storage after successful OAuth', async () => {
    renderWithRouter()

    await waitFor(() => {
      expect(mockIntegrationService.completeOAuth).toHaveBeenCalled()
    })

    await waitFor(() => {
      expect(window.sessionStorage.removeItem).toHaveBeenCalledWith('oauth_state')
    })
    
    expect(window.sessionStorage.removeItem).toHaveBeenCalledWith('oauth_provider')
    expect(window.sessionStorage.removeItem).toHaveBeenCalledWith('oauth_integration_name')
    expect(window.sessionStorage.removeItem).toHaveBeenCalledWith('oauth_config')
  })

  it('should redirect to integrations page after success', async () => {
    vi.useFakeTimers()
    
    renderWithRouter()

    await waitFor(() => {
      expect(screen.getByText('Integration Connected!')).toBeInTheDocument()
    })

    // Fast-forward time to trigger redirect
    vi.advanceTimersByTime(2000)

    expect(mockNavigate).toHaveBeenCalledWith('/integrations')
    
    vi.useRealTimers()
  })

  it('should handle OAuth error parameter', async () => {
    renderWithRouter(['/integrations/oauth/callback?error=access_denied'])

    await waitFor(() => {
      expect(screen.getByText('Connection Failed')).toBeInTheDocument()
    }, { timeout: 3000 })

    expect(screen.getByText('OAuth error: access_denied')).toBeInTheDocument()
    expect(mockIntegrationService.completeOAuth).not.toHaveBeenCalled()
  })

  it('should handle missing OAuth parameters', async () => {
    renderWithRouter(['/integrations/oauth/callback'])

    await waitFor(() => {
      expect(screen.getByText('Connection Failed')).toBeInTheDocument()
    }, { timeout: 3000 })

    expect(screen.getByText('Missing OAuth parameters')).toBeInTheDocument()
    expect(mockIntegrationService.completeOAuth).not.toHaveBeenCalled()
  })

  it('should handle missing session storage data', async () => {
    window.sessionStorage.getItem = vi.fn(() => null)

    renderWithRouter()

    await waitFor(() => {
      expect(screen.getByText('Connection Failed')).toBeInTheDocument()
    }, { timeout: 3000 })

    expect(screen.getByText('Missing OAuth session data')).toBeInTheDocument()
    expect(mockIntegrationService.completeOAuth).not.toHaveBeenCalled()
  })

  it('should handle invalid OAuth state', async () => {
    window.sessionStorage.getItem = vi.fn((key) => {
      if (key === 'oauth_state') return 'different-state'
      return 'mock-value'
    })

    renderWithRouter()

    await waitFor(() => {
      expect(screen.getByText('Connection Failed')).toBeInTheDocument()
    }, { timeout: 3000 })

    expect(screen.getByText('Invalid OAuth state parameter')).toBeInTheDocument()
    expect(mockIntegrationService.completeOAuth).not.toHaveBeenCalled()
  })

  it('should handle missing workspace', async () => {
    mockUseWorkspaceStore.mockReturnValue({
      currentWorkspace: null
    })

    renderWithRouter()

    await waitFor(() => {
      expect(screen.getByText('Connection Failed')).toBeInTheDocument()
    }, { timeout: 3000 })

    expect(screen.getByText('No workspace selected')).toBeInTheDocument()
    expect(mockIntegrationService.completeOAuth).not.toHaveBeenCalled()
  })

  it('should handle OAuth completion failure', async () => {
    mockIntegrationService.completeOAuth.mockRejectedValue(new Error('OAuth completion failed'))

    renderWithRouter()

    await waitFor(() => {
      expect(screen.getByText('Connection Failed')).toBeInTheDocument()
    }, { timeout: 3000 })

    expect(screen.getByText('OAuth completion failed')).toBeInTheDocument()
  })

  it('should clean up session storage on error', async () => {
    mockIntegrationService.completeOAuth.mockRejectedValue(new Error('OAuth completion failed'))

    renderWithRouter()

    await waitFor(() => {
      expect(screen.getByText('Connection Failed')).toBeInTheDocument()
    }, { timeout: 3000 })

    await waitFor(() => {
      expect(window.sessionStorage.removeItem).toHaveBeenCalledWith('oauth_state')
    })
    
    expect(window.sessionStorage.removeItem).toHaveBeenCalledWith('oauth_provider')
    expect(window.sessionStorage.removeItem).toHaveBeenCalledWith('oauth_integration_name')
    expect(window.sessionStorage.removeItem).toHaveBeenCalledWith('oauth_config')
  })

  it('should allow retry on error', async () => {
    const user = await import('@testing-library/user-event')
    
    mockIntegrationService.completeOAuth.mockRejectedValue(new Error('OAuth completion failed'))

    renderWithRouter()

    await waitFor(() => {
      expect(screen.getByText('Connection Failed')).toBeInTheDocument()
    })

    const retryButton = screen.getByRole('button', { name: 'Back to Integrations' })
    await user.default.setup().click(retryButton)

    expect(mockNavigate).toHaveBeenCalledWith('/integrations')
  })
})