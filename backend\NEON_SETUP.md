# Neon Database Setup for TaskUnify

This guide will help you set up a Neon PostgreSQL database for TaskUnify.

## Why Neon?

Neon is a serverless PostgreSQL database that offers:
- ✅ Automatic scaling and hibernation
- ✅ Branching for database schema changes
- ✅ Built-in connection pooling
- ✅ Free tier with generous limits
- ✅ No server management required

## Step-by-Step Setup

### 1. Create a Neon Account

1. Go to [Neon Console](https://console.neon.tech)
2. Sign up with GitHub, Google, or email
3. Verify your email if required

### 2. Create a New Project

1. Click "Create Project" or "New Project"
2. Choose your settings:
   - **Project Name**: `TaskUnify` (or your preferred name)
   - **Database Name**: `neondb` (default is fine)
   - **Region**: Choose closest to your users
   - **PostgreSQL Version**: 15 or 16 (latest)

3. Click "Create Project"

### 3. Get Your Connection String

1. After project creation, you'll see the connection details
2. Copy the connection string that looks like:
   ```
   postgresql://username:<EMAIL>/neondb?sslmode=require
   ```

### 4. Update Your Environment Variables

1. In your `backend/.env` file, update:
   ```env
   DATABASE_URL="your_neon_connection_string_here"
   DIRECT_URL="your_neon_connection_string_here"
   ```

2. Example:
   ```env
   DATABASE_URL="postgresql://alex:<EMAIL>/neondb?sslmode=require"
   DIRECT_URL="postgresql://alex:<EMAIL>/neondb?sslmode=require"
   ```

### 5. Run Database Setup

```bash
cd backend
npm run setup:dev
```

This will:
- Install dependencies
- Generate Prisma client
- Run database migrations
- Seed with test data
- Verify the setup

### 6. Verify Connection

```bash
npm run db:verify
```

You should see output showing successful connection and database statistics.

## Neon Console Features

### Database Dashboard
- View connection details
- Monitor usage and performance
- Manage database settings

### SQL Editor
- Run SQL queries directly in the browser
- Useful for debugging and data exploration

### Branching (Advanced)
- Create database branches for testing schema changes
- Merge changes back to main branch
- Perfect for development workflows

## Development Workflow

### Local Development
1. Use your main Neon database for development
2. Run migrations: `npm run prisma:migrate`
3. Seed data: `npm run prisma:seed`
4. View data: `npm run prisma:studio`

### Schema Changes
1. Modify `prisma/schema.prisma`
2. Create migration: `npm run prisma:migrate`
3. The migration will be applied to your Neon database

### Production Deployment
1. Create a separate Neon project for production
2. Set production `DATABASE_URL` in your deployment environment
3. Run migrations: `npm run prisma:deploy`

## Troubleshooting

### Connection Issues

**Error: "Can't reach database server"**
- Check your connection string format
- Ensure `sslmode=require` is included
- Verify the endpoint URL is correct

**Error: "Authentication failed"**
- Double-check username and password
- Regenerate password in Neon Console if needed

**Error: "Database does not exist"**
- Verify the database name in your connection string
- Check if you're connecting to the right project

### Performance Issues

**Slow queries**
- Use Neon's query insights in the console
- Add appropriate indexes to your schema
- Consider connection pooling for high-traffic apps

**Connection limits**
- Neon automatically handles connection pooling
- Free tier has connection limits - upgrade if needed

### Migration Issues

**Error: "Migration failed"**
- Check if the database is accessible
- Verify your `DIRECT_URL` is set correctly
- Try running migrations manually with `npx prisma migrate deploy`

## Neon Limits (Free Tier)

- **Storage**: 512 MB
- **Compute**: 1 vCPU, 256 MB RAM
- **Data Transfer**: 5 GB/month
- **Connections**: 100 concurrent
- **Projects**: 10 projects

These limits are generous for development and small applications. Upgrade to Pro for production use.

## Best Practices

### Security
- Never commit connection strings to version control
- Use environment variables for all database credentials
- Rotate passwords regularly

### Performance
- Use connection pooling in production
- Add indexes for frequently queried columns
- Monitor query performance in Neon Console

### Development
- Use database branching for testing schema changes
- Keep migrations small and focused
- Always backup before major schema changes

## Support

- **Neon Documentation**: https://neon.tech/docs
- **Neon Discord**: https://discord.gg/92vNTzKDGp
- **Prisma with Neon**: https://www.prisma.io/docs/guides/database/neon

## Next Steps

After setting up Neon:

1. **Start Development**: `npm run dev`
2. **View Database**: `npm run prisma:studio`
3. **Test API**: Visit `http://localhost:3001/api`
4. **Deploy**: Set up production Neon project when ready

Your TaskUnify application is now powered by Neon! 🚀