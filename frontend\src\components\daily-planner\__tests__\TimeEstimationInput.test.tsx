import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'

import { TimeEstimationInput } from '../TimeEstimationInput'

describe('TimeEstimationInput', () => {
  const mockOnChange = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  const renderComponent = (props = {}) => {
    const defaultProps = {
      value: 30,
      onChange: mockOnChange,
      ...props
    }

    return render(<TimeEstimationInput {...defaultProps} />)
  }

  it('renders with default label', () => {
    renderComponent()

    expect(screen.getByText('Estimated time')).toBeInTheDocument()
  })

  it('renders with custom label', () => {
    renderComponent({ label: 'Custom time label' })

    expect(screen.getByText('Custom time label')).toBeInTheDocument()
  })

  it('displays current value in input', () => {
    renderComponent({ value: 45 })

    expect(screen.getByDisplayValue('45')).toBeInTheDocument()
  })

  it('shows duration formatted correctly', () => {
    renderComponent({ value: 90 })

    // Use a more specific selector to avoid matching the button text
    const durationElement = screen.getByText('Duration:').nextElementSibling
    expect(durationElement).toHaveTextContent('1h 30m')
  })

  it('shows duration in minutes only for values under 60', () => {
    renderComponent({ value: 45 })

    expect(screen.getByText('45m')).toBeInTheDocument()
  })

  it('increments value when plus button is clicked', () => {
    renderComponent({ value: 30 })

    const incrementButton = screen.getByLabelText('Increment time')
    fireEvent.click(incrementButton)

    expect(mockOnChange).toHaveBeenCalledWith(35)
  })

  it('decrements value when minus button is clicked', () => {
    renderComponent({ value: 30 })

    const decrementButton = screen.getByLabelText('Decrement time')
    fireEvent.click(decrementButton)

    expect(mockOnChange).toHaveBeenCalledWith(25)
  })

  it('respects minimum value', () => {
    renderComponent({ value: 5, min: 5 })

    const decrementButton = screen.getByLabelText('Decrement time')
    expect(decrementButton).toBeDisabled()
  })

  it('respects maximum value', () => {
    renderComponent({ value: 480, max: 480 })

    const incrementButton = screen.getByLabelText('Increment time')
    expect(incrementButton).toBeDisabled()
  })

  it('uses custom step value', () => {
    renderComponent({ value: 30, step: 10 })

    const incrementButton = screen.getByLabelText('Increment time')
    fireEvent.click(incrementButton)

    expect(mockOnChange).toHaveBeenCalledWith(40)
  })

  it('handles direct input changes', () => {
    renderComponent({ value: 30 })

    const input = screen.getByDisplayValue('30')
    fireEvent.change(input, { target: { value: '60' } })

    expect(mockOnChange).toHaveBeenCalledWith(60)
  })

  it('ignores invalid input values', () => {
    renderComponent({ value: 30, min: 5, max: 480 })

    const input = screen.getByDisplayValue('30')
    fireEvent.change(input, { target: { value: '600' } }) // Above max

    expect(mockOnChange).not.toHaveBeenCalled()
  })

  it('ignores input values below minimum', () => {
    renderComponent({ value: 30, min: 5, max: 480 })

    const input = screen.getByDisplayValue('30')
    fireEvent.change(input, { target: { value: '2' } }) // Below min

    expect(mockOnChange).not.toHaveBeenCalled()
  })

  it('handles non-numeric input gracefully', () => {
    renderComponent({ value: 30 })

    const input = screen.getByDisplayValue('30')
    fireEvent.change(input, { target: { value: 'abc' } })

    expect(mockOnChange).not.toHaveBeenCalled()
  })

  it('shows quick preset buttons', () => {
    renderComponent()

    expect(screen.getByRole('button', { name: '15m' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '30m' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '1h' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '1h 30m' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '2h' })).toBeInTheDocument()
  })

  it('highlights selected preset button', () => {
    renderComponent({ value: 30 })

    const thirtyMinButton = screen.getByRole('button', { name: '30m' })
    expect(thirtyMinButton).toHaveClass('bg-primary') // or whatever the selected class is
  })

  it('calls onChange when preset button is clicked', () => {
    renderComponent({ value: 30 })

    const sixtyMinButton = screen.getByRole('button', { name: '1h' })
    fireEvent.click(sixtyMinButton)

    expect(mockOnChange).toHaveBeenCalledWith(60)
  })

  it('shows warning for tasks over 4 hours', () => {
    renderComponent({ value: 300 }) // 5 hours

    expect(screen.getByText(/over 4 hours/i)).toBeInTheDocument()
    expect(screen.getByText(/consider breaking it down/i)).toBeInTheDocument()
  })

  it('shows tip for very short tasks', () => {
    renderComponent({ value: 10 })

    expect(screen.getByText(/very short tasks/i)).toBeInTheDocument()
    expect(screen.getByText(/batched together/i)).toBeInTheDocument()
  })

  it('does not show warnings for normal duration tasks', () => {
    renderComponent({ value: 60 })

    expect(screen.queryByText(/over 4 hours/i)).not.toBeInTheDocument()
    expect(screen.queryByText(/very short tasks/i)).not.toBeInTheDocument()
  })

  it('formats complex durations correctly', () => {
    renderComponent({ value: 125 }) // 2h 5m

    expect(screen.getByText('2h 5m')).toBeInTheDocument()
  })

  it('handles zero minutes edge case', () => {
    renderComponent({ value: 60 }) // Exactly 1 hour

    // Use a more specific selector to avoid matching the button text
    const durationElement = screen.getByText('Duration:').nextElementSibling
    expect(durationElement).toHaveTextContent('1h')
  })

  it('shows minutes unit label', () => {
    renderComponent()

    expect(screen.getByText('minutes')).toBeInTheDocument()
  })

  it('shows quick select label', () => {
    renderComponent()

    expect(screen.getByText('Quick select:')).toBeInTheDocument()
  })

  it('applies custom min/max values to input', () => {
    renderComponent({ value: 30, min: 10, max: 120 })

    const input = screen.getByDisplayValue('30')
    expect(input).toHaveAttribute('min', '10')
    expect(input).toHaveAttribute('max', '120')
  })

  it('applies custom step to input', () => {
    renderComponent({ value: 30, step: 15 })

    const input = screen.getByDisplayValue('30')
    expect(input).toHaveAttribute('step', '15')
  })

  it('handles edge case of exactly minimum value', () => {
    renderComponent({ value: 5, min: 5 })

    const decrementButton = screen.getByLabelText('Decrement time')
    expect(decrementButton).toBeDisabled()
  })

  it('handles edge case of exactly maximum value', () => {
    renderComponent({ value: 480, max: 480 })

    const incrementButton = screen.getByLabelText('Increment time')
    expect(incrementButton).toBeDisabled()
  })

  it('maintains focus on input after increment/decrement', () => {
    renderComponent({ value: 30 })

    const input = screen.getByDisplayValue('30')
    input.focus()

    const incrementButton = screen.getByLabelText('Increment time')
    fireEvent.click(incrementButton)

    // Input should still be focused (this might need adjustment based on actual behavior)
    expect(document.activeElement).toBe(input)
  })
})