import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyDatabase() {
  try {
    console.log('🔍 Verifying database connection and schema...');

    // Test database connection
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Verify all tables exist and can be queried
    const userCount = await prisma.user.count();
    const workspaceCount = await prisma.workspace.count();
    const memberCount = await prisma.workspaceMember.count();
    const integrationCount = await prisma.integration.count();
    const taskCount = await prisma.task.count();
    const syncLogCount = await prisma.syncLog.count();

    console.log('📊 Database statistics:');
    console.log(`  Users: ${userCount}`);
    console.log(`  Workspaces: ${workspaceCount}`);
    console.log(`  Workspace Members: ${memberCount}`);
    console.log(`  Integrations: ${integrationCount}`);
    console.log(`  Tasks: ${taskCount}`);
    console.log(`  Sync Logs: ${syncLogCount}`);

    // Test a complex query to verify relationships work
    const workspaceWithData = await prisma.workspace.findFirst({
      include: {
        owner: true,
        members: {
          include: {
            user: true,
          },
        },
        integrations: true,
        tasks: {
          take: 3,
          orderBy: {
            priorityScore: 'desc',
          },
        },
      },
    });

    if (workspaceWithData) {
      console.log('🏢 Sample workspace data:');
      console.log(`  Name: ${workspaceWithData.name}`);
      console.log(`  Owner: ${workspaceWithData.owner.name}`);
      console.log(`  Members: ${workspaceWithData.members.length}`);
      console.log(`  Integrations: ${workspaceWithData.integrations.length}`);
      console.log(`  Tasks: ${workspaceWithData.tasks.length}`);
      
      if (workspaceWithData.tasks.length > 0) {
        console.log('📋 Top priority tasks:');
        workspaceWithData.tasks.forEach((task, index) => {
          console.log(`  ${index + 1}. ${task.title} (Score: ${task.priorityScore})`);
        });
      }
    }

    // Test indexes by running performance-critical queries
    console.log('⚡ Testing query performance...');
    
    const start = Date.now();
    await prisma.task.findMany({
      where: {
        workspaceId: workspaceWithData?.id,
        status: 'todo',
      },
      orderBy: {
        priorityScore: 'desc',
      },
      take: 10,
    });
    const queryTime = Date.now() - start;
    
    console.log(`  Task query completed in ${queryTime}ms`);

    console.log('✅ Database verification completed successfully!');
    
  } catch (error) {
    console.error('❌ Database verification failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

verifyDatabase();