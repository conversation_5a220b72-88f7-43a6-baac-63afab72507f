import { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react'
import { IntegrationService } from '@/services/integration'
import { IntegrationProvider, IntegrationConfig } from '@/types/api'
import { useWorkspaceStore } from '@/store/workspace'
import toast from 'react-hot-toast'

export function OAuthCallbackPage() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { currentWorkspace } = useWorkspaceStore()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [errorMessage, setErrorMessage] = useState('')

  useEffect(() => {
    handleOAuthCallback()
  }, [])

  const handleOAuthCallback = async () => {
    try {
      // Get OAuth parameters from URL
      const code = searchParams.get('code')
      const state = searchParams.get('state')
      const error = searchParams.get('error')

      if (error) {
        throw new Error(`OAuth error: ${error}`)
      }

      if (!code || !state) {
        throw new Error('Missing OAuth parameters')
      }

      // Get stored OAuth data from session storage
      const storedState = sessionStorage.getItem('oauth_state')
      const provider = sessionStorage.getItem('oauth_provider') as IntegrationProvider
      const integrationName = sessionStorage.getItem('oauth_integration_name')
      const configStr = sessionStorage.getItem('oauth_config')

      if (!storedState || !provider || !integrationName || !configStr) {
        throw new Error('Missing OAuth session data')
      }

      if (state !== storedState) {
        throw new Error('Invalid OAuth state parameter')
      }

      if (!currentWorkspace) {
        throw new Error('No workspace selected')
      }

      const config: Partial<IntegrationConfig> = JSON.parse(configStr)

      // Complete OAuth flow and create integration
      const integration = await IntegrationService.completeOAuth(
        currentWorkspace.id,
        provider,
        code,
        state,
        integrationName,
        config
      )

      // Clean up session storage
      sessionStorage.removeItem('oauth_state')
      sessionStorage.removeItem('oauth_provider')
      sessionStorage.removeItem('oauth_integration_name')
      sessionStorage.removeItem('oauth_config')

      setStatus('success')
      toast.success(`Successfully connected ${integration.name}`)

      // Redirect to integrations page after a short delay
      setTimeout(() => {
        navigate('/integrations')
      }, 2000)

    } catch (error) {
      console.error('OAuth callback error:', error)
      setStatus('error')
      setErrorMessage(error instanceof Error ? error.message : 'Unknown error occurred')
      
      // Clean up session storage on error
      sessionStorage.removeItem('oauth_state')
      sessionStorage.removeItem('oauth_provider')
      sessionStorage.removeItem('oauth_integration_name')
      sessionStorage.removeItem('oauth_config')
    }
  }

  const handleRetry = () => {
    navigate('/integrations')
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {status === 'loading' && (
              <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
            )}
            {status === 'success' && (
              <CheckCircle className="h-12 w-12 text-green-600" />
            )}
            {status === 'error' && (
              <AlertCircle className="h-12 w-12 text-red-600" />
            )}
          </div>
          
          <CardTitle>
            {status === 'loading' && 'Connecting Integration...'}
            {status === 'success' && 'Integration Connected!'}
            {status === 'error' && 'Connection Failed'}
          </CardTitle>
          
          <CardDescription>
            {status === 'loading' && 'Please wait while we set up your integration.'}
            {status === 'success' && 'Your integration has been successfully connected. Redirecting...'}
            {status === 'error' && 'There was a problem connecting your integration.'}
          </CardDescription>
        </CardHeader>
        
        {status === 'error' && (
          <CardContent className="space-y-4">
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-700">{errorMessage}</p>
            </div>
            
            <div className="flex space-x-3">
              <Button onClick={handleRetry} className="flex-1">
                Back to Integrations
              </Button>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  )
}