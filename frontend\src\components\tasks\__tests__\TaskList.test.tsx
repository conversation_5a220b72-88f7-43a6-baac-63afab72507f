import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { TaskList } from '../TaskList'
import { TaskService } from '@/services/task'
import { useWorkspaceStore } from '@/store/workspace'
import { Task, TaskStatus, TaskPriority, SyncStatus } from '@/types/task'

// Mock the TaskService
vi.mock('@/services/task', () => ({
  TaskService: {
    getTasks: vi.fn(),
    getFilterOptions: vi.fn(),
    updateTask: vi.fn(),
    deleteTask: vi.fn(),
    bulkUpdateTasks: vi.fn(),
  }
}))

// Mock the workspace store
vi.mock('@/store/workspace', () => ({
  useWorkspaceStore: vi.fn()
}))

// Mock date-fns
vi.mock('date-fns', () => ({
  formatDistanceToNow: vi.fn(() => '2 hours ago'),
  format: vi.fn(() => 'Dec 25, 2024 2:30 PM')
}))

const mockTasks: Task[] = [
  {
    id: 'task-1',
    workspaceId: 'workspace-1',
    integrationId: 'integration-1',
    externalId: 'ext-1',
    title: 'First Task',
    description: 'First task description',
    status: TaskStatus.TODO,
    priority: TaskPriority.HIGH,
    priorityScore: 85.5,
    assigneeId: 'user-1',
    assigneeName: 'John Doe',
    dueDate: '2024-12-25T14:30:00Z',
    estimatedMinutes: 120,
    tags: ['urgent'],
    projectName: 'Project A',
    sourceUrl: 'https://example.com/task/1',
    metadata: {},
    syncStatus: SyncStatus.SYNCED,
    createdAt: '2024-12-20T10:00:00Z',
    updatedAt: '2024-12-23T15:30:00Z',
    lastSyncAt: '2024-12-23T15:30:00Z',
    integration: {
      id: 'integration-1',
      name: 'Asana',
      provider: 'asana'
    }
  },
  {
    id: 'task-2',
    workspaceId: 'workspace-1',
    integrationId: 'integration-2',
    externalId: 'ext-2',
    title: 'Second Task',
    description: 'Second task description',
    status: TaskStatus.IN_PROGRESS,
    priority: TaskPriority.MEDIUM,
    priorityScore: 65.0,
    assigneeId: 'user-2',
    assigneeName: 'Jane Smith',
    dueDate: '2024-12-26T10:00:00Z',
    estimatedMinutes: 60,
    tags: ['bug'],
    projectName: 'Project B',
    sourceUrl: 'https://example.com/task/2',
    metadata: {},
    syncStatus: SyncStatus.SYNCED,
    createdAt: '2024-12-21T10:00:00Z',
    updatedAt: '2024-12-23T16:00:00Z',
    lastSyncAt: '2024-12-23T16:00:00Z',
    integration: {
      id: 'integration-2',
      name: 'Trello',
      provider: 'trello'
    }
  }
]

const mockFilterOptions = {
  assignees: [
    { id: 'user-1', name: 'John Doe' },
    { id: 'user-2', name: 'Jane Smith' }
  ],
  projects: ['Project A', 'Project B'],
  tags: ['urgent', 'bug'],
  integrations: [
    { id: 'integration-1', name: 'Asana', provider: 'asana' },
    { id: 'integration-2', name: 'Trello', provider: 'trello' }
  ]
}

const mockWorkspace = {
  id: 'workspace-1',
  name: 'Test Workspace',
  slug: 'test-workspace'
}

describe('TaskList', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    })
    
    vi.clearAllMocks()
    
    // Mock workspace store
    ;(useWorkspaceStore as any).mockReturnValue({
      currentWorkspace: mockWorkspace
    })

    // Mock TaskService methods
    ;(TaskService.getTasks as any).mockResolvedValue({
      tasks: mockTasks,
      total: 2,
      count: 2,
      offset: 0,
      limit: 50,
      hasMore: false
    })
    
    ;(TaskService.getFilterOptions as any).mockResolvedValue(mockFilterOptions)
    ;(TaskService.updateTask as any).mockResolvedValue(mockTasks[0])
    ;(TaskService.deleteTask as any).mockResolvedValue(undefined)
    ;(TaskService.bulkUpdateTasks as any).mockResolvedValue({ updatedCount: 2, errors: [] })
  })

  const renderTaskList = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <TaskList />
      </QueryClientProvider>
    )
  }

  it('should render task list with tasks', async () => {
    renderTaskList()

    await waitFor(() => {
      expect(screen.getByText('First Task')).toBeInTheDocument()
      expect(screen.getByText('Second Task')).toBeInTheDocument()
    })

    expect(screen.getByText('Tasks (2)')).toBeInTheDocument()
  })

  it('should show loading state initially', () => {
    renderTaskList()

    expect(screen.getByText('Loading tasks...')).toBeInTheDocument()
  })

  it('should show no workspace message when no workspace selected', () => {
    ;(useWorkspaceStore as any).mockReturnValue({
      currentWorkspace: null
    })

    renderTaskList()

    expect(screen.getByText('Please select a workspace to view tasks.')).toBeInTheDocument()
  })

  it('should show error state when tasks fail to load', async () => {
    ;(TaskService.getTasks as any).mockRejectedValue(new Error('Failed to load'))

    renderTaskList()

    await waitFor(() => {
      expect(screen.getByText('Failed to load tasks. Please try again.')).toBeInTheDocument()
    })
  })

  it('should show empty state when no tasks found', async () => {
    ;(TaskService.getTasks as any).mockResolvedValue({
      tasks: [],
      total: 0,
      count: 0,
      offset: 0,
      limit: 50,
      hasMore: false
    })

    renderTaskList()

    await waitFor(() => {
      expect(screen.getByText('No tasks found matching your criteria.')).toBeInTheDocument()
    })
  })

  it('should handle task selection', async () => {
    const user = userEvent.setup()
    renderTaskList()

    await waitFor(() => {
      expect(screen.getByText('First Task')).toBeInTheDocument()
    })

    // Select first task
    const firstCheckbox = screen.getAllByRole('checkbox')[1] // Skip "Select All" checkbox
    await user.click(firstCheckbox)

    // Should show bulk operations
    expect(screen.getByText('1 selected')).toBeInTheDocument()
  })

  it('should handle select all', async () => {
    const user = userEvent.setup()
    renderTaskList()

    await waitFor(() => {
      expect(screen.getByText('First Task')).toBeInTheDocument()
    })

    // Click select all
    const selectAllButton = screen.getByText('Select All')
    await user.click(selectAllButton)

    // Should show bulk operations with all tasks selected
    expect(screen.getByText('2 selected')).toBeInTheDocument()
  })

  it('should handle task click to open detail panel', async () => {
    const user = userEvent.setup()
    renderTaskList()

    await waitFor(() => {
      expect(screen.getByText('First Task')).toBeInTheDocument()
    })

    // Click on task title
    const taskTitle = screen.getByText('First Task')
    await user.click(taskTitle)

    // Should open detail panel
    expect(screen.getByText('Task Details')).toBeInTheDocument()
  })

  it('should handle search filter', async () => {
    const user = userEvent.setup()
    renderTaskList()

    await waitFor(() => {
      expect(screen.getByText('First Task')).toBeInTheDocument()
    })

    // Type in search box
    const searchInput = screen.getByPlaceholderText('Search tasks...')
    await user.type(searchInput, 'test query')

    // Should call getTasks with search parameter after debounce
    await waitFor(() => {
      expect(TaskService.getTasks).toHaveBeenCalledWith(
        mockWorkspace.id,
        expect.objectContaining({
          search: 'test query'
        })
      )
    }, { timeout: 500 })
  })

  it('should handle sort change', async () => {
    const user = userEvent.setup()
    renderTaskList()

    await waitFor(() => {
      expect(screen.getByText('First Task')).toBeInTheDocument()
    })

    // Change sort field
    const sortSelect = screen.getByDisplayValue('Priority Score')
    await user.click(sortSelect)
    await user.click(screen.getByText('Due Date'))

    expect(TaskService.getTasks).toHaveBeenCalledWith(
      mockWorkspace.id,
      expect.objectContaining({
        sortBy: 'dueDate'
      })
    )
  })

  it('should handle refresh', async () => {
    const user = userEvent.setup()
    renderTaskList()

    await waitFor(() => {
      expect(screen.getByText('First Task')).toBeInTheDocument()
    })

    // Click refresh button
    const refreshButton = screen.getByText('Refresh')
    await user.click(refreshButton)

    // Should call getTasks again
    expect(TaskService.getTasks).toHaveBeenCalledTimes(2)
  })

  it('should handle bulk update', async () => {
    const user = userEvent.setup()
    renderTaskList()

    await waitFor(() => {
      expect(screen.getByText('First Task')).toBeInTheDocument()
    })

    // Select tasks
    const selectAllButton = screen.getByText('Select All')
    await user.click(selectAllButton)

    // Click quick status update
    const doneButton = screen.getByText('Done')
    await user.click(doneButton)

    expect(TaskService.bulkUpdateTasks).toHaveBeenCalledWith(
      mockWorkspace.id,
      {
        taskIds: ['task-1', 'task-2'],
        updates: { status: TaskStatus.DONE }
      }
    )
  })

  it('should handle load more', async () => {
    const user = userEvent.setup()
    
    // Mock response with hasMore: true
    ;(TaskService.getTasks as any).mockResolvedValue({
      tasks: mockTasks,
      total: 100,
      count: 2,
      offset: 0,
      limit: 50,
      hasMore: true
    })

    renderTaskList()

    await waitFor(() => {
      expect(screen.getByText('First Task')).toBeInTheDocument()
    })

    // Should show load more button
    const loadMoreButton = screen.getByText('Load More')
    expect(loadMoreButton).toBeInTheDocument()

    await user.click(loadMoreButton)

    // Should call getTasks with increased offset
    expect(TaskService.getTasks).toHaveBeenCalledWith(
      mockWorkspace.id,
      expect.objectContaining({
        offset: 50
      })
    )
  })

  it('should reset state when workspace changes', async () => {
    const { rerender } = renderTaskList()

    await waitFor(() => {
      expect(screen.getByText('First Task')).toBeInTheDocument()
    })

    // Select a task
    const user = userEvent.setup()
    const firstCheckbox = screen.getAllByRole('checkbox')[1]
    await user.click(firstCheckbox)

    expect(screen.getByText('1 selected')).toBeInTheDocument()

    // Change workspace
    ;(useWorkspaceStore as any).mockReturnValue({
      currentWorkspace: { id: 'workspace-2', name: 'New Workspace' }
    })

    rerender(
      <QueryClientProvider client={queryClient}>
        <TaskList />
      </QueryClientProvider>
    )

    // Selection should be cleared
    await waitFor(() => {
      expect(screen.queryByText('1 selected')).not.toBeInTheDocument()
    })
  })
})