#!/usr/bin/env ts-node

import { execSync } from 'child_process';
import { readFileSync, writeFileSync } from 'fs';
import * as readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

async function main() {
  console.log('🚀 TaskUnify Neon Database Setup Helper\n');

  console.log('This script will help you configure TaskUnify to use Neon database.\n');

  // Check if .env exists
  let envContent = '';
  try {
    envContent = readFileSync('.env', 'utf8');
  } catch {
    console.log('📝 Creating .env file from .env.example...');
    try {
      const exampleContent = readFileSync('.env.example', 'utf8');
      writeFileSync('.env', exampleContent);
      envContent = exampleContent;
      console.log('✅ .env file created\n');
    } catch {
      console.error('❌ Could not create .env file. Please create it manually.');
      process.exit(1);
    }
  }

  // Check if DATABASE_URL is already configured
  if (envContent.includes('ep-') && envContent.includes('neon.tech')) {
    console.log('✅ Neon connection string already configured in .env\n');
    
    const reconfigure = await askQuestion('Do you want to reconfigure? (y/N): ');
    if (reconfigure.toLowerCase() !== 'y') {
      console.log('Skipping configuration. Running setup...\n');
      rl.close();
      
      try {
        execSync('npm run setup:dev', { stdio: 'inherit' });
      } catch (error) {
        console.error('❌ Setup failed. Please check your Neon connection string.');
        process.exit(1);
      }
      return;
    }
  }

  console.log('📋 Please follow these steps to get your Neon connection string:\n');
  console.log('1. Go to https://console.neon.tech');
  console.log('2. Sign in or create an account');
  console.log('3. Create a new project (or use existing one)');
  console.log('4. Copy the connection string from the dashboard');
  console.log('   It should look like: postgresql://username:<EMAIL>/neondb?sslmode=require\n');

  const connectionString = await askQuestion('Enter your Neon connection string: ');

  if (!connectionString) {
    console.log('❌ No connection string provided. Exiting.');
    rl.close();
    process.exit(1);
  }

  // Validate connection string format
  if (!connectionString.includes('neon.tech') || !connectionString.startsWith('postgresql://')) {
    console.log('⚠️  Warning: This doesn\'t look like a Neon connection string.');
    const proceed = await askQuestion('Do you want to continue anyway? (y/N): ');
    if (proceed.toLowerCase() !== 'y') {
      console.log('Exiting. Please check your connection string.');
      rl.close();
      process.exit(1);
    }
  }

  // Update .env file
  console.log('\n📝 Updating .env file...');
  
  let updatedEnv = envContent;
  
  // Update DATABASE_URL
  updatedEnv = updatedEnv.replace(
    /DATABASE_URL="[^"]*"/,
    `DATABASE_URL="${connectionString}"`
  );
  
  // Update DIRECT_URL
  updatedEnv = updatedEnv.replace(
    /DIRECT_URL="[^"]*"/,
    `DIRECT_URL="${connectionString}"`
  );

  writeFileSync('.env', updatedEnv);
  console.log('✅ .env file updated with Neon connection string\n');

  rl.close();

  // Run setup
  console.log('🔧 Running database setup...\n');
  
  try {
    execSync('npm run setup:dev', { stdio: 'inherit' });
    
    console.log('\n🎉 Setup completed successfully!');
    console.log('\n📚 Next steps:');
    console.log('   1. Start the development server: npm run dev');
    console.log('   2. Open Prisma Studio: npm run prisma:studio');
    console.log('   3. View API docs: http://localhost:3001/api');
    console.log('\n💡 Your TaskUnify app is now powered by Neon! 🚀');
    
  } catch (error) {
    console.error('\n❌ Setup failed. Please check your connection string and try again.');
    console.log('\n🔍 Troubleshooting:');
    console.log('   1. Verify your Neon connection string is correct');
    console.log('   2. Check that your Neon database is accessible');
    console.log('   3. Ensure you have internet connectivity');
    console.log('   4. Try running: npm run db:verify');
    process.exit(1);
  }
}

main().catch((error) => {
  console.error('❌ An error occurred:', error);
  rl.close();
  process.exit(1);
});