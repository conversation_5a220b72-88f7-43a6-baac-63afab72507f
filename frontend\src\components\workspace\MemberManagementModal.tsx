import { useState, useEffect } from 'react'
import { X, Plus, Mail, Crown, Shield, User, Trash2 } from 'lucide-react'
import { Workspace, WorkspaceMember, useWorkspaceStore } from '@/store/workspace'
import { workspaceService } from '@/services/workspace'
import toast from 'react-hot-toast'

interface MemberManagementModalProps {
  isOpen: boolean
  onClose: () => void
  workspace: Workspace
}

export function MemberManagementModal({ isOpen, onClose, workspace }: MemberManagementModalProps) {
  const [members, setMembers] = useState<WorkspaceMember[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showInviteForm, setShowInviteForm] = useState(false)
  const [inviteEmail, setInviteEmail] = useState('')
  const [inviteRole, setInviteRole] = useState<'ADMIN' | 'MEMBER'>('MEMBER')
  const [isInviting, setIsInviting] = useState(false)
  const [inviteError, setInviteError] = useState('')

  const { setMembers: setStoreMembers } = useWorkspaceStore()

  useEffect(() => {
    if (isOpen && workspace) {
      loadMembers()
    }
  }, [isOpen, workspace])

  const loadMembers = async () => {
    try {
      setIsLoading(true)
      const membersData = await workspaceService.getMembers(workspace.id)
      setMembers(membersData)
      setStoreMembers(membersData)
    } catch (error) {
      toast.error('Failed to load workspace members')
      console.error('Error loading members:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleInviteMember = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!inviteEmail.trim()) {
      setInviteError('Email is required')
      return
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(inviteEmail)) {
      setInviteError('Please enter a valid email address')
      return
    }

    setIsInviting(true)
    setInviteError('')

    try {
      const newMember = await workspaceService.inviteMember(workspace.id, {
        email: inviteEmail.trim(),
        role: inviteRole
      })
      
      setMembers(prev => [...prev, newMember])
      setInviteEmail('')
      setInviteRole('MEMBER')
      setShowInviteForm(false)
      toast.success('Member invited successfully')
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to invite member'
      setInviteError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsInviting(false)
    }
  }

  const handleUpdateMemberRole = async (memberId: string, newRole: 'ADMIN' | 'MEMBER') => {
    try {
      const updatedMember = await workspaceService.updateMember(workspace.id, memberId, {
        role: newRole
      })
      
      setMembers(prev => prev.map(member => 
        member.id === memberId ? updatedMember : member
      ))
      toast.success('Member role updated successfully')
    } catch (error) {
      toast.error('Failed to update member role')
      console.error('Error updating member role:', error)
    }
  }

  const handleRemoveMember = async (memberId: string) => {
    if (!confirm('Are you sure you want to remove this member from the workspace?')) {
      return
    }

    try {
      await workspaceService.removeMember(workspace.id, memberId)
      setMembers(prev => prev.filter(member => member.id !== memberId))
      toast.success('Member removed successfully')
    } catch (error) {
      toast.error('Failed to remove member')
      console.error('Error removing member:', error)
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'OWNER':
        return <Crown className="h-4 w-4 text-yellow-500" />
      case 'ADMIN':
        return <Shield className="h-4 w-4 text-blue-500" />
      default:
        return <User className="h-4 w-4 text-gray-500" />
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'OWNER':
        return 'Owner'
      case 'ADMIN':
        return 'Admin'
      default:
        return 'Member'
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Manage Members - {workspace.name}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
              data-testid="close-modal-button"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="p-6">
            {/* Invite Member Section */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-md font-medium text-gray-900">Team Members</h3>
                <button
                  onClick={() => setShowInviteForm(!showInviteForm)}
                  className="bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 flex items-center gap-2 text-sm"
                  data-testid="invite-member-button"
                >
                  <Plus className="h-4 w-4" />
                  Invite Member
                </button>
              </div>

              {showInviteForm && (
                <div className="bg-gray-50 p-4 rounded-lg mb-4">
                  <form onSubmit={handleInviteMember} className="space-y-4">
                    <div>
                      <label htmlFor="invite-email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address
                      </label>
                      <input
                        id="invite-email"
                        type="email"
                        value={inviteEmail}
                        onChange={(e) => {
                          setInviteEmail(e.target.value)
                          setInviteError('')
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="<EMAIL>"
                        disabled={isInviting}
                        data-testid="invite-email-input"
                      />
                    </div>

                    <div>
                      <label htmlFor="invite-role" className="block text-sm font-medium text-gray-700 mb-1">
                        Role
                      </label>
                      <select
                        id="invite-role"
                        value={inviteRole}
                        onChange={(e) => setInviteRole(e.target.value as 'ADMIN' | 'MEMBER')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        disabled={isInviting}
                        data-testid="invite-role-select"
                      >
                        <option value="MEMBER">Member</option>
                        <option value="ADMIN">Admin</option>
                      </select>
                    </div>

                    {inviteError && (
                      <p className="text-sm text-red-600" data-testid="invite-error">
                        {inviteError}
                      </p>
                    )}

                    <div className="flex justify-end space-x-3">
                      <button
                        type="button"
                        onClick={() => {
                          setShowInviteForm(false)
                          setInviteEmail('')
                          setInviteError('')
                        }}
                        className="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                        disabled={isInviting}
                        data-testid="cancel-invite-button"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
                        disabled={isInviting}
                        data-testid="send-invite-button"
                      >
                        {isInviting ? 'Sending...' : 'Send Invite'}
                      </button>
                    </div>
                  </form>
                </div>
              )}
            </div>

            {/* Members List */}
            <div>
              {isLoading ? (
                <div className="text-center py-8 text-gray-500">
                  Loading members...
                </div>
              ) : members.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No members found. Invite someone to get started.
                </div>
              ) : (
                <div className="space-y-3">
                  {members.map((member) => (
                    <div
                      key={member.id}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                      data-testid={`member-${member.id}`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          {getRoleIcon(member.role)}
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {member.userId}
                            </p>
                            <p className="text-xs text-gray-500">
                              {getRoleLabel(member.role)} • Joined {new Date(member.joinedAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {member.role !== 'OWNER' && (
                          <>
                            <select
                              value={member.role}
                              onChange={(e) => handleUpdateMemberRole(member.id, e.target.value as 'ADMIN' | 'MEMBER')}
                              className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              data-testid={`role-select-${member.id}`}
                            >
                              <option value="MEMBER">Member</option>
                              <option value="ADMIN">Admin</option>
                            </select>
                            <button
                              onClick={() => handleRemoveMember(member.id)}
                              className="p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded"
                              title="Remove member"
                              data-testid={`remove-member-${member.id}`}
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-end p-6 border-t border-gray-200">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              data-testid="close-button"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}