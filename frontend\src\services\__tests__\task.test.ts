import { describe, it, expect, vi, beforeEach } from 'vitest'
import { TaskService } from '../task'
import { apiClient } from '../api'
import { TaskStatus, TaskPriority, TaskSortField, SortOrder } from '@/types/task'

// Mock the API service
vi.mock('../api', () => ({
  apiService: {
    get: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
  }
}))

import { apiService } from '../api'
const mockApiService = apiService as any

describe('TaskService', () => {
  const workspaceId = 'workspace-123'
  const taskId = 'task-123'

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getTasks', () => {
    it('should fetch tasks with default parameters', async () => {
      const mockResponse = {
        data: {
          tasks: [],
          total: 0,
          count: 0,
          offset: 0,
          limit: 50,
          hasMore: false
        }
      }
      mockApiService.get.mockResolvedValue(mockResponse.data)

      const result = await TaskService.getTasks(workspaceId)

      expect(mockApiService.get).toHaveBeenCalledWith(`/workspaces/${workspaceId}/tasks?`)
      expect(result).toEqual(mockResponse.data)
    })

    it('should fetch tasks with query parameters', async () => {
      const query = {
        limit: 20,
        offset: 10,
        status: TaskStatus.TODO,
        priority: TaskPriority.HIGH,
        search: 'test query',
        sortBy: TaskSortField.DUE_DATE,
        sortOrder: SortOrder.ASC,
        tags: ['urgent', 'bug']
      }

      const mockResponse = {
        data: {
          tasks: [],
          total: 0,
          count: 0,
          offset: 10,
          limit: 20,
          hasMore: false
        }
      }
      mockApiService.get.mockResolvedValue(mockResponse.data)

      await TaskService.getTasks(workspaceId, query)

      const expectedUrl = `/workspaces/${workspaceId}/tasks?limit=20&offset=10&sortBy=dueDate&sortOrder=asc&status=todo&priority=high&search=test+query&tags=urgent%2Cbug`
      expect(mockApiService.get).toHaveBeenCalledWith(expectedUrl)
    })

    it('should handle date filters', async () => {
      const query = {
        dueDateFrom: '2024-01-01T00:00:00Z',
        dueDateTo: '2024-12-31T23:59:59Z'
      }

      mockApiService.get.mockResolvedValue({})

      await TaskService.getTasks(workspaceId, query)

      const expectedUrl = `/workspaces/${workspaceId}/tasks?dueDateFrom=2024-01-01T00%3A00%3A00Z&dueDateTo=2024-12-31T23%3A59%3A59Z`
      expect(mockApiService.get).toHaveBeenCalledWith(expectedUrl)
    })
  })

  describe('getTask', () => {
    it('should fetch a single task', async () => {
      const mockTask = {
        id: taskId,
        title: 'Test Task',
        status: TaskStatus.TODO
      }
      mockApiService.get.mockResolvedValue(mockTask)

      const result = await TaskService.getTask(workspaceId, taskId)

      expect(mockApiService.get).toHaveBeenCalledWith(`/workspaces/${workspaceId}/tasks/${taskId}`)
      expect(result).toEqual(mockTask)
    })
  })

  describe('updateTask', () => {
    it('should update a task', async () => {
      const updates = {
        title: 'Updated Task',
        status: TaskStatus.IN_PROGRESS,
        priority: TaskPriority.HIGH
      }
      const mockUpdatedTask = {
        id: taskId,
        ...updates
      }
      mockApiService.patch.mockResolvedValue(mockUpdatedTask)

      const result = await TaskService.updateTask(workspaceId, taskId, updates)

      expect(mockApiService.patch).toHaveBeenCalledWith(
        `/workspaces/${workspaceId}/tasks/${taskId}`,
        updates
      )
      expect(result).toEqual(mockUpdatedTask)
    })
  })

  describe('deleteTask', () => {
    it('should delete a task', async () => {
      mockApiService.delete.mockResolvedValue({})

      await TaskService.deleteTask(workspaceId, taskId)

      expect(mockApiService.delete).toHaveBeenCalledWith(`/workspaces/${workspaceId}/tasks/${taskId}`)
    })
  })

  describe('bulkUpdateTasks', () => {
    it('should bulk update tasks', async () => {
      const request = {
        taskIds: ['task-1', 'task-2'],
        updates: {
          status: TaskStatus.DONE,
          priority: TaskPriority.LOW
        }
      }
      const mockResponse = {
        updatedCount: 2,
        errors: []
      }
      mockApiService.patch.mockResolvedValue(mockResponse)

      const result = await TaskService.bulkUpdateTasks(workspaceId, request)

      expect(mockApiService.patch).toHaveBeenCalledWith(
        `/workspaces/${workspaceId}/tasks/bulk`,
        request
      )
      expect(result).toEqual(mockResponse)
    })
  })

  describe('searchTasks', () => {
    it('should search tasks with query', async () => {
      const searchQuery = 'bug fix'
      const filters = { status: TaskStatus.TODO }
      const mockResponse = { tasks: [], total: 0 }
      mockApiService.get.mockResolvedValue(mockResponse)

      await TaskService.searchTasks(workspaceId, searchQuery, filters)

      const expectedUrl = `/workspaces/${workspaceId}/tasks?status=todo&search=bug+fix`
      expect(mockApiService.get).toHaveBeenCalledWith(expectedUrl)
    })
  })

  describe('getFilterOptions', () => {
    it('should fetch filter options', async () => {
      const mockOptions = {
        assignees: [{ id: 'user-1', name: 'John Doe' }],
        projects: ['Project A', 'Project B'],
        tags: ['urgent', 'bug'],
        integrations: [{ id: 'int-1', name: 'Asana', provider: 'asana' }]
      }
      mockApiService.get.mockResolvedValue(mockOptions)

      const result = await TaskService.getFilterOptions(workspaceId)

      expect(mockApiService.get).toHaveBeenCalledWith(`/workspaces/${workspaceId}/tasks/filter-options`)
      expect(result).toEqual(mockOptions)
    })
  })
})