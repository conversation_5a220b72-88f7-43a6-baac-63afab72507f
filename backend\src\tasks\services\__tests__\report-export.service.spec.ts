import { Test, TestingModule } from '@nestjs/testing';
import { ReportExportService } from '../report-export.service';
import { 
  CompletionRateReport, 
  TaskAgingReport, 
  VelocityReport 
} from '../../interfaces/progress-tracking.interface';
import { ExportFormat, ReportType } from '../../dto/export-report.dto';

describe('ReportExportService', () => {
  let service: ReportExportService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ReportExportService],
    }).compile();

    service = module.get<ReportExportService>(ReportExportService);
  });

  describe('exportReport', () => {
    const mockCompletionRateReport: CompletionRateReport = {
      overall: {
        totalTasks: 100,
        completedTasks: 75,
        completionRate: 75,
      },
      bySource: [
        {
          source: 'asana',
          sourceName: 'Asana Integration',
          totalTasks: 50,
          completedTasks: 40,
          completionRate: 80,
        },
        {
          source: 'trello',
          sourceName: 'Trello Integration',
          totalTasks: 50,
          completedTasks: 35,
          completionRate: 70,
        },
      ],
      byProject: [
        {
          project: 'Project A',
          totalTasks: 60,
          completedTasks: 45,
          completionRate: 75,
        },
        {
          project: 'Project B',
          totalTasks: 40,
          completedTasks: 30,
          completionRate: 75,
        },
      ],
      byTimePeriod: [
        {
          period: '2024-01-01 to 2024-01-07',
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-01-07'),
          totalTasks: 25,
          completedTasks: 20,
          completionRate: 80,
        },
      ],
      filters: {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
      },
      generatedAt: new Date('2024-01-15T10:00:00Z'),
    };

    it('should export completion rate report as CSV', async () => {
      const result = await service.exportReport(
        mockCompletionRateReport,
        ReportType.COMPLETION,
        ExportFormat.CSV
      );

      expect(result.filename).toMatch(/completion-report-\d{4}-\d{2}-\d{2}\.csv/);
      expect(result.mimeType).toBe('text/csv');
      expect(result.data).toBeInstanceOf(Buffer);

      const csvContent = result.data.toString('utf-8');
      expect(csvContent).toContain('Report Type,Completion Rate Report');
      expect(csvContent).toContain('Overall Statistics');
      expect(csvContent).toContain('100,75,75.00');
      expect(csvContent).toContain('Completion Rate by Source');
      expect(csvContent).toContain('asana,Asana Integration,50,40,80.00');
      expect(csvContent).toContain('Completion Rate by Project');
      expect(csvContent).toContain('"Project A",60,45,75.00');
      expect(csvContent).toContain('Completion Rate by Time Period');
    });

    it('should export task aging report as CSV', async () => {
      const mockTaskAgingReport: TaskAgingReport = {
        totalActiveTasks: 50,
        tasksByAgeRange: [
          {
            ageRange: { label: '0-7 days', minDays: 0, maxDays: 7 },
            taskCount: 20,
            tasks: [
              {
                id: 'task-1',
                title: 'Recent Task',
                ageInDays: 3,
                dueDate: new Date('2024-01-20'),
                status: 'todo',
                project: 'Project A',
                assignee: 'John Doe',
                source: 'asana',
                sourceName: 'Asana Integration',
              },
            ],
          },
          {
            ageRange: { label: '30+ days', minDays: 31, maxDays: null },
            taskCount: 10,
            tasks: [
              {
                id: 'task-2',
                title: 'Old Task',
                ageInDays: 45,
                dueDate: null,
                status: 'in_progress',
                project: null,
                assignee: null,
                source: 'trello',
                sourceName: 'Trello Integration',
              },
            ],
          },
        ],
        filters: {
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
        },
        generatedAt: new Date('2024-01-15T10:00:00Z'),
      };

      const result = await service.exportReport(
        mockTaskAgingReport,
        ReportType.AGING,
        ExportFormat.CSV
      );

      expect(result.filename).toMatch(/aging-report-\d{4}-\d{2}-\d{2}\.csv/);
      expect(result.mimeType).toBe('text/csv');

      const csvContent = result.data.toString('utf-8');
      expect(csvContent).toContain('Report Type,Task Aging Report');
      expect(csvContent).toContain('Total Active Tasks,50');
      expect(csvContent).toContain('Summary by Age Range');
      expect(csvContent).toContain('"0-7 days",20');
      expect(csvContent).toContain('"30+ days",10');
      expect(csvContent).toContain('Detailed Task List');
      expect(csvContent).toContain('task-1,"Recent Task",3,2024-01-20,todo,"Project A","John Doe",asana,"Asana Integration"');
      expect(csvContent).toContain('task-2,"Old Task",45,,in_progress,"","",trello,"Trello Integration"');
    });

    it('should export velocity report as CSV', async () => {
      const mockVelocityReport: VelocityReport = {
        weeklyData: [
          {
            weekStart: '2024-01-01',
            weekEnd: '2024-01-07',
            tasksCompleted: 10,
            estimatedMinutes: 600,
          },
          {
            weekStart: '2024-01-08',
            weekEnd: '2024-01-14',
            tasksCompleted: 12,
            estimatedMinutes: 720,
          },
        ],
        averageTasksPerWeek: 11,
        trendAnalysis: {
          direction: 'increasing',
          percentage: 15.5,
          recentAverage: 12,
          earlierAverage: 10,
        },
        velocityBySource: [
          {
            source: 'asana',
            sourceName: 'Asana Integration',
            totalTasksCompleted: 22,
            totalEstimatedMinutes: 1320,
            averageTasksPerWeek: 11,
          },
        ],
        filters: {
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
        },
        generatedAt: new Date('2024-01-15T10:00:00Z'),
      };

      const result = await service.exportReport(
        mockVelocityReport,
        ReportType.VELOCITY,
        ExportFormat.CSV
      );

      expect(result.filename).toMatch(/velocity-report-\d{4}-\d{2}-\d{2}\.csv/);
      expect(result.mimeType).toBe('text/csv');

      const csvContent = result.data.toString('utf-8');
      expect(csvContent).toContain('Report Type,Velocity Report');
      expect(csvContent).toContain('Average Tasks Per Week,11.00');
      expect(csvContent).toContain('Trend Analysis');
      expect(csvContent).toContain('increasing,15.50,12.00,10.00');
      expect(csvContent).toContain('Weekly Velocity Data');
      expect(csvContent).toContain('2024-01-01,2024-01-07,10,600');
      expect(csvContent).toContain('2024-01-08,2024-01-14,12,720');
      expect(csvContent).toContain('Velocity by Source');
      expect(csvContent).toContain('asana,"Asana Integration",22,1320,11.00');
    });

    it('should export report as PDF placeholder', async () => {
      const result = await service.exportReport(
        mockCompletionRateReport,
        ReportType.COMPLETION,
        ExportFormat.PDF
      );

      expect(result.filename).toMatch(/completion-report-\d{4}-\d{2}-\d{2}\.pdf/);
      expect(result.mimeType).toBe('application/pdf');
      expect(result.data).toBeInstanceOf(Buffer);

      const pdfContent = result.data.toString('utf-8');
      expect(pdfContent).toContain('PDF Report Placeholder');
      expect(pdfContent).toContain('Report Type: completion');
      expect(pdfContent).toContain('Generated At:');
    });

    it('should throw error for unsupported export format', async () => {
      await expect(
        service.exportReport(
          mockCompletionRateReport,
          ReportType.COMPLETION,
          'xml' as ExportFormat
        )
      ).rejects.toThrow('Unsupported export format: xml');
    });

    it('should throw error for unsupported report type', async () => {
      await expect(
        service.exportReport(
          mockCompletionRateReport,
          'unknown' as ReportType,
          ExportFormat.CSV
        )
      ).rejects.toThrow('Unsupported report type: unknown');
    });
  });

  describe('CSV generation edge cases', () => {
    it('should handle empty data gracefully', async () => {
      const emptyCompletionReport: CompletionRateReport = {
        overall: {
          totalTasks: 0,
          completedTasks: 0,
          completionRate: 0,
        },
        bySource: [],
        byProject: [],
        byTimePeriod: [],
        filters: {
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
        },
        generatedAt: new Date('2024-01-15T10:00:00Z'),
      };

      const result = await service.exportReport(
        emptyCompletionReport,
        ReportType.COMPLETION,
        ExportFormat.CSV
      );

      const csvContent = result.data.toString('utf-8');
      expect(csvContent).toContain('0,0,0.00');
      expect(csvContent).toContain('Completion Rate by Source');
      expect(csvContent).toContain('Completion Rate by Project');
    });

    it('should properly escape CSV values with commas and quotes', async () => {
      const reportWithSpecialChars: CompletionRateReport = {
        overall: {
          totalTasks: 10,
          completedTasks: 5,
          completionRate: 50,
        },
        bySource: [],
        byProject: [
          {
            project: 'Project with, comma and "quotes"',
            totalTasks: 10,
            completedTasks: 5,
            completionRate: 50,
          },
        ],
        byTimePeriod: [],
        filters: {
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
        },
        generatedAt: new Date('2024-01-15T10:00:00Z'),
      };

      const result = await service.exportReport(
        reportWithSpecialChars,
        ReportType.COMPLETION,
        ExportFormat.CSV
      );

      const csvContent = result.data.toString('utf-8');
      expect(csvContent).toContain('"Project with, comma and "quotes"",10,5,50.00');
    });
  });
});