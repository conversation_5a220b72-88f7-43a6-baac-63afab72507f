// Database types and enums for TaskUnify
// These complement the generated Prisma types with additional type safety

export enum UserRole {
  OWNER = 'OWNER',
  ADMIN = 'ADMIN',
  MEMBER = 'MEMBER',
}

export enum IntegrationProvider {
  ASANA = 'asana',
  TRELLO = 'trello',
  JIRA = 'jira',
  CLICKUP = 'clickup',
  MONDAY = 'monday',
  TODOIST = 'todoist',
  NOTION = 'notion',
  GOOGLE_SHEETS = 'google_sheets',
}

export enum IntegrationStatus {
  ACTIVE = 'ACTIVE',
  ERROR = 'ERROR',
  DISABLED = 'DISABLED',
}

export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  DONE = 'done',
  CANCELLED = 'cancelled',
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum SyncStatus {
  SYNCED = 'synced',
  PENDING = 'pending',
  ERROR = 'error',
  CONFLICT = 'conflict',
}

export enum SyncOperation {
  FULL_SYNC = 'FULL_SYNC',
  INCREMENTAL_SYNC = 'INCREMENTAL_SYNC',
  WEBHOOK_SYNC = 'WEBHOOK_SYNC',
  MANUAL_SYNC = 'MANUAL_SYNC',
}

export enum SyncLogStatus {
  SUCCESS = 'SUCCESS',
  PARTIAL_SUCCESS = 'PARTIAL_SUCCESS',
  FAILED = 'FAILED',
  IN_PROGRESS = 'IN_PROGRESS',
}

// Workspace settings interface
export interface WorkspaceSettings {
  priorityWeights: {
    dueDateProximity: number;
    effortEstimate: number;
    businessImpact: number;
    contextSwitching: number;
  };
  defaultSyncInterval: number;
  enableTwoWaySync: boolean;
  timezone?: string;
  workingHours?: {
    start: string;
    end: string;
    days: number[];
  };
}

// Integration configuration interface
export interface IntegrationConfig {
  syncInterval: number;
  enableTwoWaySync: boolean;
  fieldMappings: FieldMapping[];
  filters: SyncFilter[];
  webhookUrl?: string;
  customSettings?: Record<string, any>;
}

export interface FieldMapping {
  source: string;
  target: string;
  transform?: string;
}

export interface SyncFilter {
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'in' | 'notIn';
  value: any;
}

// Task metadata interfaces for different providers
export interface AsanaTaskMetadata {
  asanaGid: string;
  parentTask?: string;
  subtasks: string[];
  customFields?: Record<string, any>;
}

export interface TrelloTaskMetadata {
  trelloId: string;
  boardId: string;
  listId: string;
  labels?: string[];
  checklist?: Array<{
    id: string;
    name: string;
    completed: boolean;
  }>;
}

export interface GoogleSheetsTaskMetadata {
  sheetRow: number;
  lastModified: string;
  sheetId?: string;
  range?: string;
}

// Sync error interface
export interface SyncError {
  type: 'AUTH_ERROR' | 'API_ERROR' | 'NETWORK_ERROR' | 'VALIDATION_ERROR';
  message: string;
  details?: Record<string, any>;
  timestamp: string;
  retryable: boolean;
}

// Priority calculation interface
export interface PriorityFactors {
  dueDateScore: number;
  effortScore: number;
  impactScore: number;
  contextScore: number;
}

// API response interfaces
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
  requestId: string;
}

// Database query options
export interface TaskQueryOptions {
  workspaceId: string;
  status?: TaskStatus[];
  priority?: TaskPriority[];
  assigneeId?: string;
  integrationId?: string;
  projectName?: string;
  tags?: string[];
  dueDateFrom?: Date;
  dueDateTo?: Date;
  search?: string;
  sortBy?: 'priority' | 'dueDate' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface WorkspaceQueryOptions {
  userId: string;
  role?: UserRole[];
  includeMembers?: boolean;
  includeIntegrations?: boolean;
  includeTasks?: boolean;
}

// Webhook payload interfaces
export interface WebhookPayload {
  event: string;
  data: Record<string, any>;
  timestamp: string;
  source: IntegrationProvider;
  integrationId: string;
}

// Bulk operation interfaces
export interface BulkTaskUpdate {
  taskIds: string[];
  updates: {
    status?: TaskStatus;
    priority?: TaskPriority;
    assigneeId?: string;
    dueDate?: Date;
    tags?: string[];
  };
}

export interface BulkOperationResult {
  success: number;
  failed: number;
  errors: Array<{
    taskId: string;
    error: string;
  }>;
}