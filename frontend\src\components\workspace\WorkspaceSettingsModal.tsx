import { useState, useEffect } from 'react'
import { X } from 'lucide-react'
import { Workspace } from '@/store/workspace'

interface WorkspaceSettingsModalProps {
  isOpen: boolean
  onClose: () => void
  workspace: Workspace
  onSubmit: (data: any) => Promise<void>
}

export function WorkspaceSettingsModal({ isOpen, onClose, workspace, onSubmit }: WorkspaceSettingsModalProps) {
  const [name, setName] = useState('')
  const [priorityWeights, setPriorityWeights] = useState({
    dueDateProximity: 0.3,
    effortEstimate: 0.2,
    businessImpact: 0.3,
    contextSwitching: 0.2
  })
  const [defaultSyncInterval, setDefaultSyncInterval] = useState(15)
  const [enableTwoWaySync, setEnableTwoWaySync] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<{ name?: string }>({})

  useEffect(() => {
    if (workspace) {
      setName(workspace.name)
      setPriorityWeights(workspace.settings.priorityWeights)
      setDefaultSyncInterval(workspace.settings.defaultSyncInterval)
      setEnableTwoWaySync(workspace.settings.enableTwoWaySync)
    }
  }, [workspace])

  const validateForm = () => {
    const newErrors: { name?: string } = {}

    if (!name.trim()) {
      newErrors.name = 'Workspace name is required'
    } else if (name.trim().length < 2) {
      newErrors.name = 'Workspace name must be at least 2 characters'
    }

    // Validate priority weights sum to 1.0
    const totalWeight = Object.values(priorityWeights).reduce((sum, weight) => sum + weight, 0)
    if (Math.abs(totalWeight - 1.0) > 0.01) {
      // Auto-normalize weights
      const normalizedWeights = { ...priorityWeights }
      Object.keys(normalizedWeights).forEach(key => {
        normalizedWeights[key as keyof typeof normalizedWeights] = 
          normalizedWeights[key as keyof typeof normalizedWeights] / totalWeight
      })
      setPriorityWeights(normalizedWeights)
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      await onSubmit({
        name: name.trim(),
        settings: {
          priorityWeights,
          defaultSyncInterval,
          enableTwoWaySync
        }
      })
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  const handleWeightChange = (key: keyof typeof priorityWeights, value: number) => {
    setPriorityWeights(prev => ({
      ...prev,
      [key]: Math.max(0, Math.min(1, value))
    }))
  }

  if (!isOpen) return null

  const totalWeight = Object.values(priorityWeights).reduce((sum, weight) => sum + weight, 0)

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Workspace Settings</h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
              disabled={isSubmitting}
              data-testid="close-modal-button"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Basic Settings */}
            <div>
              <h3 className="text-md font-medium text-gray-900 mb-4">Basic Settings</h3>
              <div>
                <label htmlFor="workspace-name" className="block text-sm font-medium text-gray-700 mb-1">
                  Workspace Name *
                </label>
                <input
                  id="workspace-name"
                  type="text"
                  value={name}
                  onChange={(e) => {
                    setName(e.target.value)
                    if (errors.name) {
                      setErrors(prev => ({ ...prev, name: undefined }))
                    }
                  }}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  disabled={isSubmitting}
                  data-testid="workspace-name-input"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600" data-testid="name-error">
                    {errors.name}
                  </p>
                )}
              </div>
            </div>

            {/* Priority Weights */}
            <div>
              <h3 className="text-md font-medium text-gray-900 mb-4">Priority Calculation Weights</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Due Date Proximity ({(priorityWeights.dueDateProximity * 100).toFixed(0)}%)
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.05"
                    value={priorityWeights.dueDateProximity}
                    onChange={(e) => handleWeightChange('dueDateProximity', parseFloat(e.target.value))}
                    className="w-full"
                    data-testid="due-date-weight-slider"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Effort Estimate ({(priorityWeights.effortEstimate * 100).toFixed(0)}%)
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.05"
                    value={priorityWeights.effortEstimate}
                    onChange={(e) => handleWeightChange('effortEstimate', parseFloat(e.target.value))}
                    className="w-full"
                    data-testid="effort-weight-slider"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Business Impact ({(priorityWeights.businessImpact * 100).toFixed(0)}%)
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.05"
                    value={priorityWeights.businessImpact}
                    onChange={(e) => handleWeightChange('businessImpact', parseFloat(e.target.value))}
                    className="w-full"
                    data-testid="impact-weight-slider"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Context Switching ({(priorityWeights.contextSwitching * 100).toFixed(0)}%)
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.05"
                    value={priorityWeights.contextSwitching}
                    onChange={(e) => handleWeightChange('contextSwitching', parseFloat(e.target.value))}
                    className="w-full"
                    data-testid="context-weight-slider"
                  />
                </div>
                
                <div className="text-sm text-gray-600">
                  Total: {(totalWeight * 100).toFixed(0)}%
                  {Math.abs(totalWeight - 1.0) > 0.01 && (
                    <span className="text-amber-600 ml-2">
                      (Weights will be normalized to 100%)
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Sync Settings */}
            <div>
              <h3 className="text-md font-medium text-gray-900 mb-4">Synchronization Settings</h3>
              <div className="space-y-4">
                <div>
                  <label htmlFor="sync-interval" className="block text-sm font-medium text-gray-700 mb-1">
                    Default Sync Interval (minutes)
                  </label>
                  <select
                    id="sync-interval"
                    value={defaultSyncInterval}
                    onChange={(e) => setDefaultSyncInterval(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={isSubmitting}
                    data-testid="sync-interval-select"
                  >
                    <option value={5}>5 minutes</option>
                    <option value={15}>15 minutes</option>
                    <option value={30}>30 minutes</option>
                    <option value={60}>1 hour</option>
                    <option value={120}>2 hours</option>
                    <option value={240}>4 hours</option>
                  </select>
                </div>
                
                <div className="flex items-center">
                  <input
                    id="enable-two-way-sync"
                    type="checkbox"
                    checked={enableTwoWaySync}
                    onChange={(e) => setEnableTwoWaySync(e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    disabled={isSubmitting}
                    data-testid="two-way-sync-checkbox"
                  />
                  <label htmlFor="enable-two-way-sync" className="ml-2 block text-sm text-gray-700">
                    Enable two-way synchronization
                  </label>
                </div>
                <p className="text-xs text-gray-500">
                  When enabled, changes made in TaskUnify will sync back to source tools
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50"
                disabled={isSubmitting}
                data-testid="cancel-button"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
                disabled={isSubmitting}
                data-testid="save-button"
              >
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}