import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { CreateWorkspaceDto } from './dto/create-workspace.dto';
import { UpdateWorkspaceDto } from './dto/update-workspace.dto';
import { InviteMemberDto } from './dto/invite-member.dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import {
  Workspace,
  WorkspaceMember,
  WorkspaceRole,
  WorkspaceWithMembers,
  WorkspaceMemberWithUser,
} from './entities/workspace.entity';

@Injectable()
export class WorkspacesService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(createWorkspaceDto: CreateWorkspaceDto, ownerId: string): Promise<Workspace> {
    const { name, slug, settings = {} } = createWorkspaceDto;
    
    // Generate slug if not provided
    const finalSlug = slug || this.generateSlug(name);
    
    // Check if slug is already taken
    const existingWorkspace = await this.databaseService.workspace.findUnique({
      where: { slug: finalSlug },
    });
    
    if (existingWorkspace) {
      throw new ConflictException('Workspace slug already exists');
    }

    // Create workspace with owner as first member
    const workspace = await this.databaseService.workspace.create({
      data: {
        name,
        slug: finalSlug,
        ownerId,
        settings,
        members: {
          create: {
            userId: ownerId,
            role: WorkspaceRole.OWNER,
            permissions: [],
          },
        },
      },
    });

    return workspace;
  }

  async findAll(userId: string): Promise<WorkspaceWithMembers[]> {
    const workspaces = await this.databaseService.workspace.findMany({
      where: {
        members: {
          some: {
            userId,
          },
        },
      },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatarUrl: true,
              },
            },
          },
        },
        _count: {
          select: {
            members: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    return workspaces.map((workspace) => ({
      ...workspace,
      members: workspace.members as any, // Type assertion for Prisma result
      memberCount: workspace._count.members,
    }));
  }

  async findOne(id: string, userId: string): Promise<WorkspaceWithMembers> {
    const workspace = await this.databaseService.workspace.findUnique({
      where: { id },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatarUrl: true,
              },
            },
          },
        },
        _count: {
          select: {
            members: true,
          },
        },
      },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    // Check if user has access to this workspace
    const userMembership = workspace.members.find(member => member.userId === userId);
    if (!userMembership) {
      throw new ForbiddenException('Access denied to this workspace');
    }

    return {
      ...workspace,
      members: workspace.members as any, // Type assertion for Prisma result
      memberCount: workspace._count.members,
    };
  }

  async update(id: string, updateWorkspaceDto: UpdateWorkspaceDto, userId: string): Promise<Workspace> {
    const workspace = await this.findOne(id, userId);
    
    // Check if user has admin permissions
    const userMembership = workspace.members.find(member => member.userId === userId);
    if (!this.hasAdminPermissions(userMembership.role)) {
      throw new ForbiddenException('Insufficient permissions to update workspace');
    }

    const { slug, ...updateData } = updateWorkspaceDto;
    
    // If slug is being updated, check for conflicts
    if (slug && slug !== workspace.slug) {
      const existingWorkspace = await this.databaseService.workspace.findUnique({
        where: { slug },
      });
      
      if (existingWorkspace) {
        throw new ConflictException('Workspace slug already exists');
      }
    }

    return this.databaseService.workspace.update({
      where: { id },
      data: {
        ...updateData,
        ...(slug && { slug }),
      },
    });
  }

  async remove(id: string, userId: string): Promise<void> {
    const workspace = await this.findOne(id, userId);
    
    // Only owner can delete workspace
    if (workspace.ownerId !== userId) {
      throw new ForbiddenException('Only workspace owner can delete the workspace');
    }

    await this.databaseService.workspace.delete({
      where: { id },
    });
  }

  async inviteMember(workspaceId: string, inviteMemberDto: InviteMemberDto, inviterId: string): Promise<WorkspaceMember> {
    const workspace = await this.findOne(workspaceId, inviterId);
    
    // Check if inviter has admin permissions
    const inviterMembership = workspace.members.find(member => member.userId === inviterId);
    if (!this.hasAdminPermissions(inviterMembership.role)) {
      throw new ForbiddenException('Insufficient permissions to invite members');
    }

    const { email, role = WorkspaceRole.MEMBER, permissions = [] } = inviteMemberDto;

    // Find user by email
    const user = await this.databaseService.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw new NotFoundException('User not found with this email');
    }

    // Check if user is already a member
    const existingMember = workspace.members.find(member => member.userId === user.id);
    if (existingMember) {
      throw new ConflictException('User is already a member of this workspace');
    }

    // Only owner can invite other owners/admins
    if ((role === WorkspaceRole.OWNER || role === WorkspaceRole.ADMIN) && workspace.ownerId !== inviterId) {
      throw new ForbiddenException('Only workspace owner can invite admins or owners');
    }

    return this.databaseService.workspaceMember.create({
      data: {
        userId: user.id,
        workspaceId,
        role: role as string,
        permissions,
      },
    }) as any;
  }

  async getMembers(workspaceId: string, userId: string): Promise<WorkspaceMemberWithUser[]> {
    const workspace = await this.findOne(workspaceId, userId);
    
    return workspace.members as WorkspaceMemberWithUser[];
  }

  async updateMember(
    workspaceId: string,
    memberId: string,
    updateMemberDto: UpdateMemberDto,
    updaterId: string,
  ): Promise<WorkspaceMember> {
    const workspace = await this.findOne(workspaceId, updaterId);
    
    // Check if updater has admin permissions
    const updaterMembership = workspace.members.find(member => member.userId === updaterId);
    if (!this.hasAdminPermissions(updaterMembership.role)) {
      throw new ForbiddenException('Insufficient permissions to update member');
    }

    // Find the member to update
    const memberToUpdate = workspace.members.find(member => member.id === memberId);
    if (!memberToUpdate) {
      throw new NotFoundException('Member not found in this workspace');
    }

    // Prevent owner from being demoted by non-owners
    if (memberToUpdate.role === WorkspaceRole.OWNER && workspace.ownerId !== updaterId) {
      throw new ForbiddenException('Only workspace owner can modify owner permissions');
    }

    // Only owner can promote to owner/admin
    const { role } = updateMemberDto;
    if ((role === WorkspaceRole.OWNER || role === WorkspaceRole.ADMIN) && workspace.ownerId !== updaterId) {
      throw new ForbiddenException('Only workspace owner can promote to admin or owner');
    }

    return this.databaseService.workspaceMember.update({
      where: { id: memberId },
      data: {
        ...updateMemberDto,
        ...(updateMemberDto.role && { role: updateMemberDto.role as string }),
      },
    }) as any;
  }

  async removeMember(workspaceId: string, memberId: string, removerId: string): Promise<void> {
    const workspace = await this.findOne(workspaceId, removerId);
    
    // Check if remover has admin permissions
    const removerMembership = workspace.members.find(member => member.userId === removerId);
    if (!this.hasAdminPermissions(removerMembership.role)) {
      throw new ForbiddenException('Insufficient permissions to remove member');
    }

    // Find the member to remove
    const memberToRemove = workspace.members.find(member => member.id === memberId);
    if (!memberToRemove) {
      throw new NotFoundException('Member not found in this workspace');
    }

    // Prevent owner from being removed
    if (memberToRemove.role === WorkspaceRole.OWNER) {
      throw new ForbiddenException('Workspace owner cannot be removed');
    }

    // Only owner can remove admins
    if (memberToRemove.role === WorkspaceRole.ADMIN && workspace.ownerId !== removerId) {
      throw new ForbiddenException('Only workspace owner can remove admins');
    }

    await this.databaseService.workspaceMember.delete({
      where: { id: memberId },
    });
  }

  async leaveWorkspace(workspaceId: string, userId: string): Promise<void> {
    const workspace = await this.findOne(workspaceId, userId);
    
    // Owner cannot leave workspace
    if (workspace.ownerId === userId) {
      throw new ForbiddenException('Workspace owner cannot leave the workspace. Transfer ownership or delete the workspace instead.');
    }

    const userMembership = workspace.members.find(member => member.userId === userId);
    if (!userMembership) {
      throw new NotFoundException('User is not a member of this workspace');
    }

    await this.databaseService.workspaceMember.delete({
      where: { id: userMembership.id },
    });
  }

  async transferOwnership(workspaceId: string, newOwnerId: string, currentOwnerId: string): Promise<Workspace> {
    const workspace = await this.findOne(workspaceId, currentOwnerId);
    
    // Only current owner can transfer ownership
    if (workspace.ownerId !== currentOwnerId) {
      throw new ForbiddenException('Only current owner can transfer ownership');
    }

    // Check if new owner is a member
    const newOwnerMembership = workspace.members.find(member => member.userId === newOwnerId);
    if (!newOwnerMembership) {
      throw new NotFoundException('New owner must be a member of the workspace');
    }

    // Update workspace owner and member roles
    await this.databaseService.$transaction([
      // Update workspace owner
      this.databaseService.workspace.update({
        where: { id: workspaceId },
        data: { ownerId: newOwnerId },
      }),
      // Update new owner's role
      this.databaseService.workspaceMember.update({
        where: { id: newOwnerMembership.id },
        data: { role: WorkspaceRole.OWNER },
      }),
      // Update previous owner's role to admin
      this.databaseService.workspaceMember.updateMany({
        where: { 
          workspaceId,
          userId: currentOwnerId,
        },
        data: { role: WorkspaceRole.ADMIN },
      }),
    ]);

    return this.databaseService.workspace.findUnique({
      where: { id: workspaceId },
    }) as any;
  }

  // Permission validation methods
  hasAdminPermissions(role: string | WorkspaceRole): boolean {
    return role === WorkspaceRole.OWNER || role === WorkspaceRole.ADMIN;
  }

  hasOwnerPermissions(role: string | WorkspaceRole): boolean {
    return role === WorkspaceRole.OWNER;
  }

  canAccessWorkspace(membership: WorkspaceMember | undefined): boolean {
    return !!membership;
  }

  canEditWorkspace(role: string | WorkspaceRole): boolean {
    return this.hasAdminPermissions(role);
  }

  canDeleteWorkspace(role: string | WorkspaceRole): boolean {
    return this.hasOwnerPermissions(role);
  }

  canInviteMembers(role: string | WorkspaceRole): boolean {
    return this.hasAdminPermissions(role);
  }

  canRemoveMembers(role: string | WorkspaceRole): boolean {
    return this.hasAdminPermissions(role);
  }

  canPromoteMembers(role: string | WorkspaceRole): boolean {
    return this.hasOwnerPermissions(role);
  }

  // Utility methods
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }
}