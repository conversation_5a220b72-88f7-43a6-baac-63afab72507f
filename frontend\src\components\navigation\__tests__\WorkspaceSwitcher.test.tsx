import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { vi } from 'vitest'
import { WorkspaceSwitcher } from '../WorkspaceSwitcher'
import { useWorkspaceStore } from '@/store/workspace'

// Mock the workspace store
vi.mock('@/store/workspace')
const mockUseWorkspaceStore = useWorkspaceStore as any

// Mock react-router-dom
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  }
})

const mockWorkspaces = [
  {
    id: '1',
    name: 'Personal Workspace',
    slug: 'personal',
    ownerId: 'user1',
    settings: {
      priorityWeights: {
        dueDateProximity: 0.3,
        effortEstimate: 0.2,
        businessImpact: 0.3,
        contextSwitching: 0.2
      },
      defaultSyncInterval: 15,
      enableTwoWaySync: true
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'Team Workspace',
    slug: 'team',
    ownerId: 'user1',
    settings: {
      priorityWeights: {
        dueDateProximity: 0.3,
        effortEstimate: 0.2,
        businessImpact: 0.3,
        contextSwitching: 0.2
      },
      defaultSyncInterval: 15,
      enableTwoWaySync: true
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
]

const mockSwitchWorkspace = vi.fn()

const renderWorkspaceSwitcher = () => {
  return render(
    <BrowserRouter>
      <WorkspaceSwitcher />
    </BrowserRouter>
  )
}

describe('WorkspaceSwitcher', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockUseWorkspaceStore.mockReturnValue({
      workspaces: mockWorkspaces,
      currentWorkspace: mockWorkspaces[0],
      switchWorkspace: mockSwitchWorkspace,
      setWorkspaces: vi.fn(),
      setCurrentWorkspace: vi.fn(),
      addWorkspace: vi.fn(),
      updateWorkspace: vi.fn(),
      removeWorkspace: vi.fn(),
      setMembers: vi.fn(),
      addMember: vi.fn(),
      updateMember: vi.fn(),
      removeMember: vi.fn(),
      setLoading: vi.fn(),
      members: [],
      isLoading: false
    })
  })

  it('renders the current workspace name', () => {
    renderWorkspaceSwitcher()
    
    expect(screen.getByTestId('workspace-switcher-button')).toHaveTextContent('Personal Workspace')
  })

  it('shows "Select Workspace" when no current workspace', () => {
    mockUseWorkspaceStore.mockReturnValue({
      workspaces: mockWorkspaces,
      currentWorkspace: null,
      switchWorkspace: mockSwitchWorkspace,
      setWorkspaces: vi.fn(),
      setCurrentWorkspace: vi.fn(),
      addWorkspace: vi.fn(),
      updateWorkspace: vi.fn(),
      removeWorkspace: vi.fn(),
      setMembers: vi.fn(),
      addMember: vi.fn(),
      updateMember: vi.fn(),
      removeMember: vi.fn(),
      setLoading: vi.fn(),
      members: [],
      isLoading: false
    })

    renderWorkspaceSwitcher()
    
    expect(screen.getByTestId('workspace-switcher-button')).toHaveTextContent('Select Workspace')
  })

  it('opens dropdown when clicked', () => {
    renderWorkspaceSwitcher()
    
    const button = screen.getByTestId('workspace-switcher-button')
    fireEvent.click(button)
    
    expect(screen.getByTestId('workspace-option-1')).toBeInTheDocument()
    expect(screen.getByTestId('workspace-option-2')).toBeInTheDocument()
  })

  it('shows check mark for current workspace', () => {
    renderWorkspaceSwitcher()
    
    const button = screen.getByTestId('workspace-switcher-button')
    fireEvent.click(button)
    
    const currentWorkspaceOption = screen.getByTestId('workspace-option-1')
    expect(currentWorkspaceOption.querySelector('svg')).toBeInTheDocument() // Check icon
  })

  it('switches workspace when option is clicked', async () => {
    renderWorkspaceSwitcher()
    
    const button = screen.getByTestId('workspace-switcher-button')
    fireEvent.click(button)
    
    const teamWorkspaceOption = screen.getByTestId('workspace-option-2')
    fireEvent.click(teamWorkspaceOption)
    
    expect(mockSwitchWorkspace).toHaveBeenCalledWith('2')
  })

  it('navigates to create workspace when create button is clicked', () => {
    renderWorkspaceSwitcher()
    
    const button = screen.getByTestId('workspace-switcher-button')
    fireEvent.click(button)
    
    const createButton = screen.getByTestId('create-workspace-button')
    fireEvent.click(createButton)
    
    expect(mockNavigate).toHaveBeenCalledWith('/workspaces?action=create')
  })

  it('navigates to manage workspaces when manage button is clicked', () => {
    renderWorkspaceSwitcher()
    
    const button = screen.getByTestId('workspace-switcher-button')
    fireEvent.click(button)
    
    const manageButton = screen.getByTestId('manage-workspaces-button')
    fireEvent.click(manageButton)
    
    expect(mockNavigate).toHaveBeenCalledWith('/workspaces')
  })

  it('closes dropdown when clicking outside', () => {
    renderWorkspaceSwitcher()
    
    const button = screen.getByTestId('workspace-switcher-button')
    fireEvent.click(button)
    
    expect(screen.getByTestId('workspace-option-1')).toBeInTheDocument()
    
    // Click outside
    fireEvent.click(document.body)
    
    expect(screen.queryByTestId('workspace-option-1')).not.toBeInTheDocument()
  })

  it('closes dropdown after switching workspace', async () => {
    renderWorkspaceSwitcher()
    
    const button = screen.getByTestId('workspace-switcher-button')
    fireEvent.click(button)
    
    const teamWorkspaceOption = screen.getByTestId('workspace-option-2')
    fireEvent.click(teamWorkspaceOption)
    
    await waitFor(() => {
      expect(screen.queryByTestId('workspace-option-1')).not.toBeInTheDocument()
    })
  })
})