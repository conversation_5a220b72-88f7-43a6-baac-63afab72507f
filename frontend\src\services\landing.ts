import { apiService } from './api'

export interface EarlyAccessRequest {
  email: string
  source?: string
  utm_campaign?: string
  utm_source?: string
  utm_medium?: string
}

export interface EarlyAccessResponse {
  success: boolean
  message: string
  waitlistPosition?: number
}

export const landingService = {
  async submitEarlyAccess(data: EarlyAccessRequest): Promise<EarlyAccessResponse> {
    return apiService.post<EarlyAccessResponse>('/landing/early-access', data)
  },

  async getWaitlistStats(): Promise<{ totalSignups: number; recentSignups: number }> {
    return apiService.get<{ totalSignups: number; recentSignups: number }>('/landing/stats')
  },
}