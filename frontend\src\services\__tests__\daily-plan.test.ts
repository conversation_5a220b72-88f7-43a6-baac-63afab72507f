import { describe, it, expect, vi, beforeEach } from 'vitest'
import { dailyPlanService } from '../daily-plan'
import { apiService } from '../api'

// Mock the api module
vi.mock('../api')

const mockApi = vi.mocked(apiService)

const mockDailyPlan = {
  id: 'plan-1',
  workspaceId: 'workspace-1',
  userId: 'user-1',
  planDate: new Date('2024-01-15'),
  totalEstimatedMinutes: 240,
  totalCompletedMinutes: 120,
  completionProgress: 50,
  exceedsRecommendedTime: false,
  tasks: [
    {
      id: 'plan-task-1',
      taskId: 'task-1',
      estimatedMinutes: 120,
      actualMinutes: 60,
      orderIndex: 0,
      completedAt: null,
      task: {
        id: 'task-1',
        title: 'Test Task',
        description: 'Test description',
        status: 'todo' as const,
        priority: 'high' as const,
        priorityScore: 85,
        dueDate: null,
        tags: ['urgent'],
        projectName: 'Test Project',
        sourceUrl: 'https://example.com/task-1'
      }
    }
  ],
  createdAt: new Date('2024-01-15'),
  updatedAt: new Date('2024-01-15')
}

const mockCreateRequest = {
  planDate: '2024-01-15',
  tasks: [
    {
      taskId: 'task-1',
      estimatedMinutes: 60,
      orderIndex: 0
    }
  ]
}

const mockUpdateRequest = {
  estimatedMinutes: 90,
  actualMinutes: 45,
  orderIndex: 1
}

describe('dailyPlanService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getDailyPlan', () => {
    it('fetches daily plan successfully', async () => {
      mockApi.get.mockResolvedValue({ data: mockDailyPlan })

      const result = await dailyPlanService.getDailyPlan('workspace-1', '2024-01-15')

      expect(mockApi.get).toHaveBeenCalledWith('/workspaces/workspace-1/daily-plans/2024-01-15')
      expect(result).toEqual(mockDailyPlan)
    })

    it('returns null when daily plan is not found (404)', async () => {
      const error = new Error('Not found')
      ;(error as any).response = { status: 404 }
      mockApi.get.mockRejectedValue(error)

      const result = await dailyPlanService.getDailyPlan('workspace-1', '2024-01-15')

      expect(result).toBeNull()
    })

    it('throws error for non-404 errors', async () => {
      const error = new Error('Server error')
      ;(error as any).response = { status: 500 }
      mockApi.get.mockRejectedValue(error)

      await expect(
        dailyPlanService.getDailyPlan('workspace-1', '2024-01-15')
      ).rejects.toThrow('Server error')
    })

    it('throws error when no response object exists', async () => {
      const error = new Error('Network error')
      mockApi.get.mockRejectedValue(error)

      await expect(
        dailyPlanService.getDailyPlan('workspace-1', '2024-01-15')
      ).rejects.toThrow('Network error')
    })
  })

  describe('createDailyPlan', () => {
    it('creates daily plan successfully', async () => {
      mockApi.post.mockResolvedValue({ data: mockDailyPlan })

      const result = await dailyPlanService.createDailyPlan('workspace-1', mockCreateRequest)

      expect(mockApi.post).toHaveBeenCalledWith(
        '/workspaces/workspace-1/daily-plans',
        mockCreateRequest
      )
      expect(result).toEqual(mockDailyPlan)
    })

    it('throws error when creation fails', async () => {
      const error = new Error('Creation failed')
      mockApi.post.mockRejectedValue(error)

      await expect(
        dailyPlanService.createDailyPlan('workspace-1', mockCreateRequest)
      ).rejects.toThrow('Creation failed')
    })
  })

  describe('updateDailyPlan', () => {
    it('updates daily plan successfully', async () => {
      mockApi.put.mockResolvedValue({ data: mockDailyPlan })

      const result = await dailyPlanService.updateDailyPlan(
        'workspace-1',
        'plan-1',
        mockCreateRequest
      )

      expect(mockApi.put).toHaveBeenCalledWith(
        '/workspaces/workspace-1/daily-plans/plan-1',
        mockCreateRequest
      )
      expect(result).toEqual(mockDailyPlan)
    })

    it('throws error when update fails', async () => {
      const error = new Error('Update failed')
      mockApi.put.mockRejectedValue(error)

      await expect(
        dailyPlanService.updateDailyPlan('workspace-1', 'plan-1', mockCreateRequest)
      ).rejects.toThrow('Update failed')
    })
  })

  describe('updateDailyPlanTask', () => {
    it('updates daily plan task successfully', async () => {
      mockApi.patch.mockResolvedValue({ data: {} })

      await dailyPlanService.updateDailyPlanTask(
        'workspace-1',
        'plan-1',
        'task-1',
        mockUpdateRequest
      )

      expect(mockApi.patch).toHaveBeenCalledWith(
        '/workspaces/workspace-1/daily-plans/plan-1/tasks/task-1',
        mockUpdateRequest
      )
    })

    it('throws error when task update fails', async () => {
      const error = new Error('Task update failed')
      mockApi.patch.mockRejectedValue(error)

      await expect(
        dailyPlanService.updateDailyPlanTask(
          'workspace-1',
          'plan-1',
          'task-1',
          mockUpdateRequest
        )
      ).rejects.toThrow('Task update failed')
    })
  })

  describe('completeDailyPlanTask', () => {
    it('completes daily plan task successfully', async () => {
      mockApi.patch.mockResolvedValue({ data: {} })

      await dailyPlanService.completeDailyPlanTask('workspace-1', 'plan-1', 'task-1')

      expect(mockApi.patch).toHaveBeenCalledWith(
        '/workspaces/workspace-1/daily-plans/plan-1/tasks/task-1/complete'
      )
    })

    it('throws error when task completion fails', async () => {
      const error = new Error('Task completion failed')
      mockApi.patch.mockRejectedValue(error)

      await expect(
        dailyPlanService.completeDailyPlanTask('workspace-1', 'plan-1', 'task-1')
      ).rejects.toThrow('Task completion failed')
    })
  })

  describe('removeDailyPlanTask', () => {
    it('removes daily plan task successfully', async () => {
      mockApi.delete.mockResolvedValue({ data: {} })

      await dailyPlanService.removeDailyPlanTask('workspace-1', 'plan-1', 'task-1')

      expect(mockApi.delete).toHaveBeenCalledWith(
        '/workspaces/workspace-1/daily-plans/plan-1/tasks/task-1'
      )
    })

    it('throws error when task removal fails', async () => {
      const error = new Error('Task removal failed')
      mockApi.delete.mockRejectedValue(error)

      await expect(
        dailyPlanService.removeDailyPlanTask('workspace-1', 'plan-1', 'task-1')
      ).rejects.toThrow('Task removal failed')
    })
  })

  describe('API endpoint construction', () => {
    it('constructs correct endpoints for different workspace IDs', async () => {
      mockApi.get.mockResolvedValue({ data: mockDailyPlan })

      await dailyPlanService.getDailyPlan('different-workspace', '2024-01-15')

      expect(mockApi.get).toHaveBeenCalledWith(
        '/workspaces/different-workspace/daily-plans/2024-01-15'
      )
    })

    it('constructs correct endpoints for different dates', async () => {
      mockApi.get.mockResolvedValue({ data: mockDailyPlan })

      await dailyPlanService.getDailyPlan('workspace-1', '2024-02-20')

      expect(mockApi.get).toHaveBeenCalledWith(
        '/workspaces/workspace-1/daily-plans/2024-02-20'
      )
    })

    it('constructs correct endpoints for task operations', async () => {
      mockApi.patch.mockResolvedValue({ data: {} })

      await dailyPlanService.updateDailyPlanTask(
        'ws-123',
        'plan-456',
        'task-789',
        mockUpdateRequest
      )

      expect(mockApi.patch).toHaveBeenCalledWith(
        '/workspaces/ws-123/daily-plans/plan-456/tasks/task-789',
        mockUpdateRequest
      )
    })
  })

  describe('error handling', () => {
    it('preserves error messages from API', async () => {
      const apiError = new Error('Validation failed: Invalid task ID')
      mockApi.post.mockRejectedValue(apiError)

      await expect(
        dailyPlanService.createDailyPlan('workspace-1', mockCreateRequest)
      ).rejects.toThrow('Validation failed: Invalid task ID')
    })

    it('handles network errors', async () => {
      const networkError = new Error('Network Error')
      mockApi.get.mockRejectedValue(networkError)

      await expect(
        dailyPlanService.getDailyPlan('workspace-1', '2024-01-15')
      ).rejects.toThrow('Network Error')
    })

    it('handles timeout errors', async () => {
      const timeoutError = new Error('Request timeout')
      mockApi.put.mockRejectedValue(timeoutError)

      await expect(
        dailyPlanService.updateDailyPlan('workspace-1', 'plan-1', mockCreateRequest)
      ).rejects.toThrow('Request timeout')
    })
  })

  describe('request data validation', () => {
    it('sends correct data structure for create request', async () => {
      mockApi.post.mockResolvedValue({ data: mockDailyPlan })

      const createData = {
        planDate: '2024-01-15',
        tasks: [
          { taskId: 'task-1', estimatedMinutes: 60, orderIndex: 0 },
          { taskId: 'task-2', estimatedMinutes: 30, orderIndex: 1 }
        ]
      }

      await dailyPlanService.createDailyPlan('workspace-1', createData)

      expect(mockApi.post).toHaveBeenCalledWith(
        '/workspaces/workspace-1/daily-plans',
        createData
      )
    })

    it('sends correct data structure for update request', async () => {
      mockApi.patch.mockResolvedValue({ data: {} })

      const updateData = {
        estimatedMinutes: 45,
        actualMinutes: 30,
        orderIndex: 2,
        completedAt: new Date('2024-01-15T15:30:00Z')
      }

      await dailyPlanService.updateDailyPlanTask(
        'workspace-1',
        'plan-1',
        'task-1',
        updateData
      )

      expect(mockApi.patch).toHaveBeenCalledWith(
        '/workspaces/workspace-1/daily-plans/plan-1/tasks/task-1',
        updateData
      )
    })
  })
})