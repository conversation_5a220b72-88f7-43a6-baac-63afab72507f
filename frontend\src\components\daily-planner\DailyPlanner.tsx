import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd'
import { format, startOfDay } from 'date-fns'
import { Calendar, Clock, AlertTriangle, Plus } from 'lucide-react'

import { useWorkspaceStore } from '@/store/workspace'
import { dailyPlanService } from '@/services/daily-plan'
import { TaskService } from '@/services/task'
import { DailyPlan, Task, CreateDailyPlanRequest } from '@/types/task'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'

import { DailyPlanTask } from './DailyPlanTask'
import { TaskSelector } from './TaskSelector'
import { TimeEstimationInput } from './TimeEstimationInput'
import { ProgressTracker } from './ProgressTracker'

interface DailyPlannerProps {
  selectedDate?: Date
}

export const DailyPlanner: React.FC<DailyPlannerProps> = ({ 
  selectedDate = new Date() 
}) => {
  const { currentWorkspace } = useWorkspaceStore()
  const queryClient = useQueryClient()
  
  const [showTaskSelector, setShowTaskSelector] = useState(false)
  const [draggedTask, setDraggedTask] = useState<Task | null>(null)
  
  const planDate = format(startOfDay(selectedDate), 'yyyy-MM-dd')
  
  // Query for daily plan
  const { data: dailyPlan, isLoading } = useQuery({
    queryKey: ['daily-plan', currentWorkspace?.id, planDate],
    queryFn: () => dailyPlanService.getDailyPlan(currentWorkspace!.id, planDate),
    enabled: !!currentWorkspace?.id,
  })

  // Query for available tasks
  const { data: availableTasks } = useQuery({
    queryKey: ['tasks', currentWorkspace?.id, 'available'],
    queryFn: () => TaskService.getTasks(currentWorkspace!.id, {
      status: 'todo',
      limit: 100,
      sortBy: 'priorityScore',
      sortOrder: 'desc'
    }),
    enabled: !!currentWorkspace?.id,
  })

  // Mutation for creating/updating daily plan
  const createPlanMutation = useMutation({
    mutationFn: (data: CreateDailyPlanRequest) => 
      dailyPlanService.createDailyPlan(currentWorkspace!.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['daily-plan', currentWorkspace?.id, planDate] })
    }
  })

  const updatePlanMutation = useMutation({
    mutationFn: (data: CreateDailyPlanRequest) => 
      dailyPlanService.updateDailyPlan(currentWorkspace!.id, dailyPlan!.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['daily-plan', currentWorkspace?.id, planDate] })
    }
  })

  // Handle drag and drop
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination || !dailyPlan) return

    const { source, destination } = result
    
    if (source.droppableId === 'available-tasks' && destination.droppableId === 'daily-plan') {
      // Adding task to daily plan
      const task = availableTasks?.tasks.find(t => t.id === result.draggableId)
      if (task) {
        setDraggedTask(task)
        setShowTaskSelector(true)
      }
    } else if (source.droppableId === 'daily-plan' && destination.droppableId === 'daily-plan') {
      // Reordering tasks in daily plan
      const newTasks = Array.from(dailyPlan.tasks)
      const [reorderedTask] = newTasks.splice(source.index, 1)
      newTasks.splice(destination.index, 0, reorderedTask)

      const updatedTasks = newTasks.map((task, index) => ({
        taskId: task.taskId,
        estimatedMinutes: task.estimatedMinutes,
        orderIndex: index
      }))

      updatePlanMutation.mutate({
        planDate,
        tasks: updatedTasks
      })
    }
  }

  // Handle adding task with time estimation
  const handleAddTask = (taskId: string, estimatedMinutes: number) => {
    const existingTasks = dailyPlan?.tasks || []
    const newTasks = [
      ...existingTasks.map(task => ({
        taskId: task.taskId,
        estimatedMinutes: task.estimatedMinutes,
        orderIndex: task.orderIndex
      })),
      {
        taskId,
        estimatedMinutes,
        orderIndex: existingTasks.length
      }
    ]

    if (dailyPlan) {
      updatePlanMutation.mutate({ planDate, tasks: newTasks })
    } else {
      createPlanMutation.mutate({ planDate, tasks: newTasks })
    }

    setShowTaskSelector(false)
    setDraggedTask(null)
  }

  // Handle removing task from daily plan
  const handleRemoveTask = (taskId: string) => {
    if (!dailyPlan) return

    const updatedTasks = dailyPlan.tasks
      .filter(task => task.taskId !== taskId)
      .map((task, index) => ({
        taskId: task.taskId,
        estimatedMinutes: task.estimatedMinutes,
        orderIndex: index
      }))

    updatePlanMutation.mutate({ planDate, tasks: updatedTasks })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const totalEstimated = dailyPlan?.totalEstimatedMinutes || 0
  const exceedsRecommended = totalEstimated > 480 // 8 hours

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Calendar className="h-5 w-5 text-gray-500" />
          <h2 className="text-xl font-semibold">
            Daily Plan - {format(selectedDate, 'MMMM d, yyyy')}
          </h2>
        </div>
        <Button
          onClick={() => setShowTaskSelector(true)}
          className="flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Add Task</span>
        </Button>
      </div>

      {/* Progress Overview */}
      {dailyPlan && (
        <ProgressTracker
          totalEstimated={dailyPlan.totalEstimatedMinutes}
          totalCompleted={dailyPlan.totalCompletedMinutes}
          completionProgress={dailyPlan.completionProgress}
          exceedsRecommended={dailyPlan.exceedsRecommendedTime}
        />
      )}

      {/* Time Warning */}
      {exceedsRecommended && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Your daily plan exceeds 8 hours ({Math.round(totalEstimated / 60 * 10) / 10} hours planned). 
            Consider rescheduling some tasks to maintain a healthy workload.
          </AlertDescription>
        </Alert>
      )}

      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Available Tasks */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium text-gray-600">
                Available Tasks
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Droppable droppableId="available-tasks">
                {(provided) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className="space-y-2 min-h-[200px]"
                  >
                    {availableTasks?.tasks
                      .filter(task => !dailyPlan?.tasks.some(pt => pt.taskId === task.id))
                      .slice(0, 10)
                      .map((task, index) => (
                        <Draggable key={task.id} draggableId={task.id} index={index}>
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className={`p-3 border rounded-lg cursor-move transition-colors ${
                                snapshot.isDragging 
                                  ? 'bg-blue-50 border-blue-200' 
                                  : 'bg-white hover:bg-gray-50'
                              }`}
                            >
                              <div className="font-medium text-sm">{task.title}</div>
                              <div className="flex items-center justify-between mt-1">
                                <Badge variant="outline" className="text-xs">
                                  {task.priority || 'medium'}
                                </Badge>
                                <div className="flex items-center text-xs text-gray-500">
                                  <Clock className="h-3 w-3 mr-1" />
                                  {task.estimatedMinutes || 30}m
                                </div>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </CardContent>
          </Card>

          {/* Daily Plan */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Today's Plan</span>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Clock className="h-4 w-4" />
                  <span>{Math.round(totalEstimated / 60 * 10) / 10}h planned</span>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Droppable droppableId="daily-plan">
                {(provided) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className="space-y-3 min-h-[300px]"
                  >
                    {dailyPlan?.tasks.length === 0 ? (
                      <div className="text-center py-12 text-gray-500">
                        <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No tasks planned for today</p>
                        <p className="text-sm">Drag tasks from the left or click "Add Task"</p>
                      </div>
                    ) : (
                      dailyPlan?.tasks.map((planTask, index) => (
                        <Draggable key={planTask.id} draggableId={planTask.id} index={index}>
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className={snapshot.isDragging ? 'opacity-50' : ''}
                            >
                              <DailyPlanTask
                                planTask={planTask}
                                dragHandleProps={provided.dragHandleProps}
                                onRemove={() => handleRemoveTask(planTask.taskId)}
                                workspaceId={currentWorkspace!.id}
                                planId={dailyPlan!.id}
                              />
                            </div>
                          )}
                        </Draggable>
                      ))
                    )}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </CardContent>
          </Card>
        </div>
      </DragDropContext>

      {/* Task Selector Modal */}
      {showTaskSelector && (
        <TaskSelector
          isOpen={showTaskSelector}
          onClose={() => {
            setShowTaskSelector(false)
            setDraggedTask(null)
          }}
          onAddTask={handleAddTask}
          availableTasks={availableTasks?.tasks || []}
          selectedTask={draggedTask}
        />
      )}
    </div>
  )
}