import { useState } from 'react'
import { formatDistanceToNow, format } from 'date-fns'
import { ExternalLink, Calendar, Clock, User, Tag, Building2 } from 'lucide-react'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { TaskStatusBadge } from './TaskStatusBadge'
import { TaskPriorityBadge } from './TaskPriorityBadge'
import { TaskSyncStatusBadge } from './TaskSyncStatusBadge'
import { Task } from '@/types/task'
import { cn } from '@/lib/utils'

interface TaskListItemProps {
  task: Task
  isSelected: boolean
  onSelectionChange: (taskId: string, selected: boolean) => void
  onTaskClick: (task: Task) => void
  className?: string
}

export function TaskListItem({
  task,
  isSelected,
  onSelectionChange,
  onTaskClick,
  className
}: TaskListItemProps) {
  const [isHovered, setIsHovered] = useState(false)

  const formatDueDate = (dueDate: string) => {
    const date = new Date(dueDate)
    const now = new Date()
    const isOverdue = date < now
    const isToday = date.toDateString() === now.toDateString()
    const isTomorrow = date.toDateString() === new Date(now.getTime() + 24 * 60 * 60 * 1000).toDateString()

    if (isToday) return { text: 'Today', className: 'text-orange-600' }
    if (isTomorrow) return { text: 'Tomorrow', className: 'text-blue-600' }
    if (isOverdue) return { text: `Overdue by ${formatDistanceToNow(date)}`, className: 'text-red-600' }
    
    return { 
      text: formatDistanceToNow(date, { addSuffix: true }), 
      className: 'text-gray-600' 
    }
  }

  const dueDate = task.dueDate ? formatDueDate(task.dueDate) : null

  return (
    <Card 
      className={cn(
        'transition-all duration-200 hover:shadow-md cursor-pointer',
        isSelected && 'ring-2 ring-blue-500 ring-opacity-50',
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Selection checkbox */}
          <Checkbox
            checked={isSelected}
            onCheckedChange={(checked) => onSelectionChange(task.id, !!checked)}
            className="mt-1"
          />

          {/* Task content */}
          <div className="flex-1 min-w-0" onClick={() => onTaskClick(task)}>
            {/* Header with title and priority score */}
            <div className="flex items-start justify-between gap-2 mb-2">
              <h3 className="font-medium text-gray-900 truncate flex-1">
                {task.title}
              </h3>
              <div className="flex items-center gap-2 flex-shrink-0">
                <TaskPriorityBadge 
                  priority={task.priority} 
                  priorityScore={task.priorityScore}
                  showIcon
                />
                {task.priorityScore > 0 && (
                  <Badge variant="outline" className="text-xs">
                    {Math.round(task.priorityScore)}
                  </Badge>
                )}
              </div>
            </div>

            {/* Description */}
            {task.description && (
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                {task.description}
              </p>
            )}

            {/* Metadata row */}
            <div className="flex items-center gap-4 text-xs text-gray-500 mb-3">
              {/* Status */}
              <TaskStatusBadge status={task.status} />

              {/* Due date */}
              {dueDate && (
                <div className={cn('flex items-center gap-1', dueDate.className)}>
                  <Calendar className="h-3 w-3" />
                  {dueDate.text}
                </div>
              )}

              {/* Estimated time */}
              {task.estimatedMinutes && (
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {task.estimatedMinutes}m
                </div>
              )}

              {/* Assignee */}
              {task.assigneeName && (
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  {task.assigneeName}
                </div>
              )}

              {/* Project */}
              {task.projectName && (
                <div className="flex items-center gap-1">
                  <Building2 className="h-3 w-3" />
                  {task.projectName}
                </div>
              )}
            </div>

            {/* Tags */}
            {task.tags.length > 0 && (
              <div className="flex items-center gap-1 mb-3">
                <Tag className="h-3 w-3 text-gray-400" />
                <div className="flex gap-1 flex-wrap">
                  {task.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {task.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{task.tags.length - 3} more
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Footer with source and sync status */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {/* Integration source */}
                {task.integration && (
                  <Badge variant="outline" className="text-xs">
                    {task.integration.name}
                  </Badge>
                )}

                {/* Sync status */}
                <TaskSyncStatusBadge syncStatus={task.syncStatus} />
              </div>

              {/* External link */}
              {(isHovered || isSelected) && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={(e) => {
                    e.stopPropagation()
                    window.open(task.sourceUrl, '_blank')
                  }}
                  title="Open in source tool"
                >
                  <ExternalLink className="h-3 w-3" />
                </Button>
              )}
            </div>

            {/* Last updated */}
            <div className="text-xs text-gray-400 mt-2">
              Updated {formatDistanceToNow(new Date(task.updatedAt), { addSuffix: true })}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}