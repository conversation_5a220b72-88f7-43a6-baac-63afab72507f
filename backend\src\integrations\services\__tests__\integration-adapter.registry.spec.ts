import { Test, TestingModule } from '@nestjs/testing';
import { IntegrationAdapterRegistry } from '../integration-adapter.registry';
import { IntegrationAdapter } from '../../interfaces/integration-adapter.interface';
import { IntegrationProvider, OAuthCredentials, IntegrationConfig, ExternalTask, TaskUpdate, CreateTaskRequest, AuthResult } from '../../types';

// Mock adapter for testing
class MockIntegrationAdapter implements IntegrationAdapter {
  constructor(
    private provider: IntegrationProvider,
    private twoWaySync: boolean = true,
    private webhooks: boolean = false,
  ) {}

  getProvider(): string {
    return this.provider;
  }

  supportsTwoWaySync(): boolean {
    return this.twoWaySync;
  }

  supportsWebhooks(): boolean {
    return this.webhooks;
  }

  async authenticate(credentials: OAuthCredentials): Promise<AuthResult> {
    return { success: true };
  }

  async fetchTasks(config: IntegrationConfig): Promise<ExternalTask[]> {
    return [];
  }

  async updateTask(taskId: string, updates: TaskUpdate): Promise<ExternalTask> {
    return {} as ExternalTask;
  }

  async createTask(task: CreateTaskRequest): Promise<ExternalTask> {
    return {} as ExternalTask;
  }

  async validateCredentials(credentials: any): Promise<boolean> {
    return true;
  }
}

describe('IntegrationAdapterRegistry', () => {
  let registry: IntegrationAdapterRegistry;
  let mockAsanaAdapter: MockIntegrationAdapter;
  let mockTrelloAdapter: MockIntegrationAdapter;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [IntegrationAdapterRegistry],
    }).compile();

    registry = module.get<IntegrationAdapterRegistry>(IntegrationAdapterRegistry);
    mockAsanaAdapter = new MockIntegrationAdapter(IntegrationProvider.ASANA, true, true);
    mockTrelloAdapter = new MockIntegrationAdapter(IntegrationProvider.TRELLO, true, false);
  });

  it('should be defined', () => {
    expect(registry).toBeDefined();
  });

  describe('register', () => {
    it('should register an adapter successfully', () => {
      registry.register(IntegrationProvider.ASANA, mockAsanaAdapter);
      
      expect(registry.hasAdapter(IntegrationProvider.ASANA)).toBe(true);
    });

    it('should allow overriding an existing adapter', () => {
      const newAdapter = new MockIntegrationAdapter(IntegrationProvider.ASANA, false, false);
      
      registry.register(IntegrationProvider.ASANA, mockAsanaAdapter);
      registry.register(IntegrationProvider.ASANA, newAdapter);
      
      const retrievedAdapter = registry.getAdapter(IntegrationProvider.ASANA);
      expect(retrievedAdapter).toBe(newAdapter);
    });
  });

  describe('getAdapter', () => {
    it('should return registered adapter', () => {
      registry.register(IntegrationProvider.ASANA, mockAsanaAdapter);
      
      const adapter = registry.getAdapter(IntegrationProvider.ASANA);
      expect(adapter).toBe(mockAsanaAdapter);
    });

    it('should throw error for unregistered adapter', () => {
      expect(() => registry.getAdapter(IntegrationProvider.ASANA))
        .toThrow('No adapter registered for provider: asana');
    });
  });

  describe('hasAdapter', () => {
    it('should return true for registered adapter', () => {
      registry.register(IntegrationProvider.ASANA, mockAsanaAdapter);
      
      expect(registry.hasAdapter(IntegrationProvider.ASANA)).toBe(true);
    });

    it('should return false for unregistered adapter', () => {
      expect(registry.hasAdapter(IntegrationProvider.ASANA)).toBe(false);
    });
  });

  describe('getRegisteredProviders', () => {
    it('should return empty array when no adapters registered', () => {
      const providers = registry.getRegisteredProviders();
      expect(providers).toEqual([]);
    });

    it('should return all registered providers', () => {
      registry.register(IntegrationProvider.ASANA, mockAsanaAdapter);
      registry.register(IntegrationProvider.TRELLO, mockTrelloAdapter);
      
      const providers = registry.getRegisteredProviders();
      expect(providers).toContain(IntegrationProvider.ASANA);
      expect(providers).toContain(IntegrationProvider.TRELLO);
      expect(providers).toHaveLength(2);
    });
  });

  describe('getAdapterCapabilities', () => {
    it('should return adapter capabilities', () => {
      registry.register(IntegrationProvider.ASANA, mockAsanaAdapter);
      
      const capabilities = registry.getAdapterCapabilities(IntegrationProvider.ASANA);
      expect(capabilities).toEqual({
        supportsTwoWaySync: true,
        supportsWebhooks: true,
      });
    });

    it('should throw error for unregistered adapter', () => {
      expect(() => registry.getAdapterCapabilities(IntegrationProvider.ASANA))
        .toThrow('No adapter registered for provider: asana');
    });
  });

  describe('getAllCapabilities', () => {
    it('should return empty map when no adapters registered', () => {
      const capabilities = registry.getAllCapabilities();
      expect(capabilities.size).toBe(0);
    });

    it('should return capabilities for all registered adapters', () => {
      registry.register(IntegrationProvider.ASANA, mockAsanaAdapter);
      registry.register(IntegrationProvider.TRELLO, mockTrelloAdapter);
      
      const capabilities = registry.getAllCapabilities();
      
      expect(capabilities.size).toBe(2);
      expect(capabilities.get(IntegrationProvider.ASANA)).toEqual({
        supportsTwoWaySync: true,
        supportsWebhooks: true,
      });
      expect(capabilities.get(IntegrationProvider.TRELLO)).toEqual({
        supportsTwoWaySync: true,
        supportsWebhooks: false,
      });
    });
  });

  describe('validateRequiredAdapters', () => {
    it('should not throw when all required adapters are registered', () => {
      registry.register(IntegrationProvider.ASANA, mockAsanaAdapter);
      registry.register(IntegrationProvider.TRELLO, mockTrelloAdapter);
      
      expect(() => registry.validateRequiredAdapters([
        IntegrationProvider.ASANA,
        IntegrationProvider.TRELLO,
      ])).not.toThrow();
    });

    it('should throw when required adapters are missing', () => {
      registry.register(IntegrationProvider.ASANA, mockAsanaAdapter);
      
      expect(() => registry.validateRequiredAdapters([
        IntegrationProvider.ASANA,
        IntegrationProvider.TRELLO,
        IntegrationProvider.JIRA,
      ])).toThrow('Missing required adapters: trello, jira');
    });

    it('should not throw for empty required adapters list', () => {
      expect(() => registry.validateRequiredAdapters([])).not.toThrow();
    });
  });

  describe('getStats', () => {
    it('should return correct stats for empty registry', () => {
      const stats = registry.getStats();
      
      expect(stats).toEqual({
        totalAdapters: 0,
        twoWaySyncSupported: 0,
        webhooksSupported: 0,
        providers: [],
      });
    });

    it('should return correct stats for registered adapters', () => {
      registry.register(IntegrationProvider.ASANA, mockAsanaAdapter);
      registry.register(IntegrationProvider.TRELLO, mockTrelloAdapter);
      
      const stats = registry.getStats();
      
      expect(stats.totalAdapters).toBe(2);
      expect(stats.twoWaySyncSupported).toBe(2);
      expect(stats.webhooksSupported).toBe(1);
      expect(stats.providers).toContain(IntegrationProvider.ASANA);
      expect(stats.providers).toContain(IntegrationProvider.TRELLO);
    });

    it('should count capabilities correctly', () => {
      const noSyncAdapter = new MockIntegrationAdapter(IntegrationProvider.JIRA, false, false);
      const webhookAdapter = new MockIntegrationAdapter(IntegrationProvider.CLICKUP, true, true);
      
      registry.register(IntegrationProvider.JIRA, noSyncAdapter);
      registry.register(IntegrationProvider.CLICKUP, webhookAdapter);
      
      const stats = registry.getStats();
      
      expect(stats.totalAdapters).toBe(2);
      expect(stats.twoWaySyncSupported).toBe(1);
      expect(stats.webhooksSupported).toBe(1);
    });
  });
});