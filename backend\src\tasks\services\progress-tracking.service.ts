import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../../database/database.service';
import { 
  CompletionRateReport, 
  TaskAgingReport, 
  VelocityReport,
  ProgressReportFilters,
  ReportTimeRange,
  AgeRange
} from '../interfaces/progress-tracking.interface';

@Injectable()
export class ProgressTrackingService {
  constructor(private readonly prisma: DatabaseService) {}

  /**
   * Calculate completion rates by source tool, project, and time period
   */
  async getCompletionRateReport(
    workspaceId: string,
    filters: ProgressReportFilters
  ): Promise<CompletionRateReport> {
    const { startDate, endDate, source, project, assignee } = filters;
    
    // Build where clause for filtering
    const whereClause: any = {
      workspaceId,
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    };

    if (source) {
      whereClause.integration = { provider: source };
    }
    if (project) {
      whereClause.projectName = project;
    }
    if (assignee) {
      whereClause.assigneeId = assignee;
    }

    // Get total tasks and completed tasks
    const [totalTasks, completedTasks] = await Promise.all([
      this.prisma.task.count({ where: whereClause }),
      this.prisma.task.count({
        where: {
          ...whereClause,
          status: 'done',
        },
      }),
    ]);

    // Get completion rates by source
    const completionBySource = await this.prisma.task.groupBy({
      by: ['integrationId'],
      where: whereClause,
      _count: {
        _all: true,
      },
    });

    const completionBySourceWithProvider = await Promise.all(
      completionBySource.map(async (group) => {
        const integration = await this.prisma.integration.findUnique({
          where: { id: group.integrationId },
          select: { provider: true, name: true },
        });

        const completedCount = await this.prisma.task.count({
          where: {
            ...whereClause,
            integrationId: group.integrationId,
            status: 'done',
          },
        });

        return {
          source: integration?.provider || 'unknown',
          sourceName: integration?.name || 'Unknown',
          totalTasks: group._count._all,
          completedTasks: completedCount,
          completionRate: group._count._all > 0 ? (completedCount / group._count._all) * 100 : 0,
        };
      })
    );

    // Get completion rates by project
    const completionByProject = await this.prisma.task.groupBy({
      by: ['projectName'],
      where: {
        ...whereClause,
        projectName: { not: null },
      },
      _count: {
        _all: true,
      },
    });

    const completionByProjectWithRates = await Promise.all(
      completionByProject.map(async (group) => {
        const completedCount = await this.prisma.task.count({
          where: {
            ...whereClause,
            projectName: group.projectName,
            status: 'done',
          },
        });

        return {
          project: group.projectName || 'No Project',
          totalTasks: group._count._all,
          completedTasks: completedCount,
          completionRate: group._count._all > 0 ? (completedCount / group._count._all) * 100 : 0,
        };
      })
    );

    // Get completion rates by time period (weekly)
    const completionByWeek = await this.getWeeklyCompletionRates(workspaceId, filters);

    return {
      overall: {
        totalTasks,
        completedTasks,
        completionRate: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0,
      },
      bySource: completionBySourceWithProvider,
      byProject: completionByProjectWithRates,
      byTimePeriod: completionByWeek,
      filters,
      generatedAt: new Date(),
    };
  }

  /**
   * Generate task aging report with configurable age ranges
   */
  async getTaskAgingReport(
    workspaceId: string,
    filters: ProgressReportFilters,
    ageRanges: AgeRange[] = [
      { label: '0-7 days', minDays: 0, maxDays: 7 },
      { label: '8-30 days', minDays: 8, maxDays: 30 },
      { label: '30+ days', minDays: 31, maxDays: null },
    ]
  ): Promise<TaskAgingReport> {
    const { source, project, assignee } = filters;
    const now = new Date();

    // Build where clause for filtering (exclude completed tasks)
    const whereClause: any = {
      workspaceId,
      status: { not: 'done' },
    };

    if (source) {
      whereClause.integration = { provider: source };
    }
    if (project) {
      whereClause.projectName = project;
    }
    if (assignee) {
      whereClause.assigneeId = assignee;
    }

    // Get all non-completed tasks
    const tasks = await this.prisma.task.findMany({
      where: whereClause,
      select: {
        id: true,
        title: true,
        createdAt: true,
        dueDate: true,
        status: true,
        projectName: true,
        assigneeName: true,
        integration: {
          select: {
            provider: true,
            name: true,
          },
        },
      },
    });

    // Group tasks by age ranges
    const tasksByAgeRange = ageRanges.map((range) => {
      const tasksInRange = tasks.filter((task) => {
        const ageInDays = Math.floor((now.getTime() - task.createdAt.getTime()) / (1000 * 60 * 60 * 24));
        
        if (range.maxDays === null) {
          return ageInDays >= range.minDays;
        }
        return ageInDays >= range.minDays && ageInDays <= range.maxDays;
      });

      return {
        ageRange: range,
        taskCount: tasksInRange.length,
        tasks: tasksInRange.map((task) => ({
          id: task.id,
          title: task.title,
          ageInDays: Math.floor((now.getTime() - task.createdAt.getTime()) / (1000 * 60 * 60 * 24)),
          dueDate: task.dueDate,
          status: task.status,
          project: task.projectName,
          assignee: task.assigneeName,
          source: task.integration.provider,
          sourceName: task.integration.name,
        })),
      };
    });

    return {
      totalActiveTasks: tasks.length,
      tasksByAgeRange,
      filters,
      generatedAt: new Date(),
    };
  }

  /**
   * Calculate velocity tracking with trend analysis
   */
  async getVelocityReport(
    workspaceId: string,
    filters: ProgressReportFilters,
    weeksBack: number = 12
  ): Promise<VelocityReport> {
    const { source, project, assignee } = filters;
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - (weeksBack * 7));

    // Build where clause for filtering
    const whereClause: any = {
      workspaceId,
      status: 'done',
      updatedAt: {
        gte: startDate,
        lte: endDate,
      },
    };

    if (source) {
      whereClause.integration = { provider: source };
    }
    if (project) {
      whereClause.projectName = project;
    }
    if (assignee) {
      whereClause.assigneeId = assignee;
    }

    // Get completed tasks grouped by week
    const completedTasks = await this.prisma.task.findMany({
      where: whereClause,
      select: {
        id: true,
        updatedAt: true,
        estimatedMinutes: true,
        integration: {
          select: {
            provider: true,
            name: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'asc',
      },
    });

    // Group tasks by week
    const weeklyData = [];
    const currentWeek = new Date(startDate);
    
    for (let i = 0; i < weeksBack; i++) {
      const weekStart = new Date(currentWeek);
      const weekEnd = new Date(currentWeek);
      weekEnd.setDate(weekEnd.getDate() + 6);

      const tasksThisWeek = completedTasks.filter((task) => {
        const taskDate = new Date(task.updatedAt);
        return taskDate >= weekStart && taskDate <= weekEnd;
      });

      const totalEstimatedMinutes = tasksThisWeek.reduce(
        (sum, task) => sum + (task.estimatedMinutes || 0),
        0
      );

      weeklyData.push({
        weekStart: weekStart.toISOString().split('T')[0],
        weekEnd: weekEnd.toISOString().split('T')[0],
        tasksCompleted: tasksThisWeek.length,
        estimatedMinutes: totalEstimatedMinutes,
      });

      currentWeek.setDate(currentWeek.getDate() + 7);
    }

    // Calculate trend analysis
    const recentWeeks = weeklyData.slice(-4); // Last 4 weeks
    const earlierWeeks = weeklyData.slice(-8, -4); // 4 weeks before that

    const recentAverage = recentWeeks.reduce((sum, week) => sum + week.tasksCompleted, 0) / recentWeeks.length;
    const earlierAverage = earlierWeeks.reduce((sum, week) => sum + week.tasksCompleted, 0) / earlierWeeks.length;

    const trendPercentage = earlierAverage > 0 ? ((recentAverage - earlierAverage) / earlierAverage) * 100 : 0;

    // Get velocity by source
    const velocityBySource = await this.getVelocityBySource(workspaceId, filters, startDate, endDate);

    return {
      weeklyData,
      averageTasksPerWeek: weeklyData.reduce((sum, week) => sum + week.tasksCompleted, 0) / weeklyData.length,
      trendAnalysis: {
        direction: trendPercentage > 5 ? 'increasing' : trendPercentage < -5 ? 'decreasing' : 'stable',
        percentage: Math.abs(trendPercentage),
        recentAverage,
        earlierAverage,
      },
      velocityBySource,
      filters,
      generatedAt: new Date(),
    };
  }

  /**
   * Get available filter options for reports
   */
  async getReportFilterOptions(workspaceId: string) {
    const [sources, projects, assignees] = await Promise.all([
      this.prisma.integration.findMany({
        where: { workspaceId },
        select: { provider: true, name: true },
        distinct: ['provider'],
      }),
      this.prisma.task.findMany({
        where: { 
          workspaceId,
          projectName: { not: null },
        },
        select: { projectName: true },
        distinct: ['projectName'],
      }),
      this.prisma.task.findMany({
        where: { 
          workspaceId,
          assigneeId: { not: null },
          assigneeName: { not: null },
        },
        select: { assigneeId: true, assigneeName: true },
        distinct: ['assigneeId'],
      }),
    ]);

    return {
      sources: sources.map(s => ({ value: s.provider, label: s.name })),
      projects: projects.map(p => ({ value: p.projectName, label: p.projectName })),
      assignees: assignees.map(a => ({ value: a.assigneeId, label: a.assigneeName })),
    };
  }

  /**
   * Helper method to get weekly completion rates
   */
  private async getWeeklyCompletionRates(
    workspaceId: string,
    filters: ProgressReportFilters
  ): Promise<ReportTimeRange[]> {
    const { startDate, endDate, source, project, assignee } = filters;
    const weeklyData = [];
    
    const currentWeek = new Date(startDate);
    while (currentWeek <= endDate) {
      const weekStart = new Date(currentWeek);
      const weekEnd = new Date(currentWeek);
      weekEnd.setDate(weekEnd.getDate() + 6);
      
      if (weekEnd > endDate) {
        weekEnd.setTime(endDate.getTime());
      }

      const whereClause: any = {
        workspaceId,
        createdAt: {
          gte: weekStart,
          lte: weekEnd,
        },
      };

      if (source) {
        whereClause.integration = { provider: source };
      }
      if (project) {
        whereClause.projectName = project;
      }
      if (assignee) {
        whereClause.assigneeId = assignee;
      }

      const [totalTasks, completedTasks] = await Promise.all([
        this.prisma.task.count({ where: whereClause }),
        this.prisma.task.count({
          where: {
            ...whereClause,
            status: 'done',
          },
        }),
      ]);

      weeklyData.push({
        period: `${weekStart.toISOString().split('T')[0]} to ${weekEnd.toISOString().split('T')[0]}`,
        startDate: weekStart,
        endDate: weekEnd,
        totalTasks,
        completedTasks,
        completionRate: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0,
      });

      currentWeek.setDate(currentWeek.getDate() + 7);
    }

    return weeklyData;
  }

  /**
   * Helper method to get velocity by source
   */
  private async getVelocityBySource(
    workspaceId: string,
    filters: ProgressReportFilters,
    startDate: Date,
    endDate: Date
  ) {
    const { source, project, assignee } = filters;

    const whereClause: any = {
      workspaceId,
      status: 'done',
      updatedAt: {
        gte: startDate,
        lte: endDate,
      },
    };

    if (source) {
      whereClause.integration = { provider: source };
    }
    if (project) {
      whereClause.projectName = project;
    }
    if (assignee) {
      whereClause.assigneeId = assignee;
    }

    const velocityBySource = await this.prisma.task.groupBy({
      by: ['integrationId'],
      where: whereClause,
      _count: {
        _all: true,
      },
      _sum: {
        estimatedMinutes: true,
      },
    });

    return Promise.all(
      velocityBySource.map(async (group) => {
        const integration = await this.prisma.integration.findUnique({
          where: { id: group.integrationId },
          select: { provider: true, name: true },
        });

        const weeksInPeriod = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7));

        return {
          source: integration?.provider || 'unknown',
          sourceName: integration?.name || 'Unknown',
          totalTasksCompleted: group._count._all,
          totalEstimatedMinutes: group._sum.estimatedMinutes || 0,
          averageTasksPerWeek: group._count._all / weeksInPeriod,
        };
      })
    );
  }
}