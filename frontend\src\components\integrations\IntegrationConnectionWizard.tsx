import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, CheckCircle, AlertCircle, ExternalLink } from 'lucide-react'
import { IntegrationService } from '@/services/integration'
import { IntegrationProvider, IntegrationConfig } from '@/types/api'
import { useWorkspaceStore } from '@/store/workspace'
import toast from 'react-hot-toast'

interface IntegrationConnectionWizardProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: (integration: any) => void
  provider?: IntegrationProvider
}

interface ProviderInfo {
  provider: IntegrationProvider
  name: string
  description: string
  logoUrl: string
  capabilities: {
    supportsTwoWaySync: boolean
    supportsWebhooks: boolean
    supportsFieldMapping: boolean
  }
}

type WizardStep = 'provider' | 'oauth' | 'config' | 'complete'

export function IntegrationConnectionWizard({
  isOpen,
  onClose,
  onSuccess,
  provider: initialProvider
}: IntegrationConnectionWizardProps) {
  const { currentWorkspace } = useWorkspaceStore()
  const [currentStep, setCurrentStep] = useState<WizardStep>('provider')
  const [selectedProvider, setSelectedProvider] = useState<IntegrationProvider | null>(initialProvider || null)
  const [providers, setProviders] = useState<ProviderInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [oauthLoading, setOauthLoading] = useState(false)
  const [integrationName, setIntegrationName] = useState('')
  const [config, setConfig] = useState<Partial<IntegrationConfig>>({
    syncInterval: 30,
    enableTwoWaySync: true,
    fieldMappings: [],
    filters: [],
    webhookEnabled: false
  })

  useEffect(() => {
    if (isOpen) {
      loadProviders()
      if (initialProvider) {
        setSelectedProvider(initialProvider)
        setCurrentStep('oauth')
      } else {
        setCurrentStep('provider')
      }
    }
  }, [isOpen, initialProvider])

  const loadProviders = async () => {
    try {
      setLoading(true)
      const providersData = await IntegrationService.getAvailableProviders()
      setProviders(providersData)
    } catch (error) {
      toast.error('Failed to load integration providers')
      console.error('Error loading providers:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleProviderSelect = (provider: IntegrationProvider) => {
    setSelectedProvider(provider)
    const providerInfo = providers.find(p => p.provider === provider)
    if (providerInfo) {
      setIntegrationName(providerInfo.name)
    }
    setCurrentStep('oauth')
  }

  const handleOAuthConnect = async () => {
    if (!selectedProvider || !currentWorkspace) return

    try {
      setOauthLoading(true)
      const redirectUri = `${window.location.origin}/integrations/oauth/callback`
      const { authUrl, state } = await IntegrationService.getOAuthUrl(
        currentWorkspace.id,
        selectedProvider,
        redirectUri
      )

      // Store state and provider for OAuth callback
      sessionStorage.setItem('oauth_state', state)
      sessionStorage.setItem('oauth_provider', selectedProvider)
      sessionStorage.setItem('oauth_integration_name', integrationName)
      sessionStorage.setItem('oauth_config', JSON.stringify(config))

      // Redirect to OAuth provider
      window.location.href = authUrl
    } catch (error) {
      toast.error('Failed to initiate OAuth connection')
      console.error('OAuth error:', error)
      setOauthLoading(false)
    }
  }

  const handleConfigUpdate = (key: keyof IntegrationConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleComplete = () => {
    onClose()
    // Reset wizard state
    setCurrentStep('provider')
    setSelectedProvider(null)
    setIntegrationName('')
    setConfig({
      syncInterval: 30,
      enableTwoWaySync: true,
      fieldMappings: [],
      filters: [],
      webhookEnabled: false
    })
  }

  const selectedProviderInfo = providers.find(p => p.provider === selectedProvider)

  const renderProviderSelection = () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium mb-2">Choose an Integration</h3>
        <p className="text-sm text-gray-600 mb-4">
          Select the service you want to connect to TaskUnify
        </p>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {providers.map((provider) => (
            <Card
              key={provider.provider}
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleProviderSelect(provider.provider)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <img
                    src={provider.logoUrl}
                    alt={provider.name}
                    className="w-8 h-8 rounded"
                    onError={(e) => {
                      e.currentTarget.src = '/api/placeholder/32/32'
                    }}
                  />
                  <div>
                    <CardTitle className="text-base">{provider.name}</CardTitle>
                    <CardDescription className="text-sm">
                      {provider.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex flex-wrap gap-1">
                  {provider.capabilities.supportsTwoWaySync && (
                    <Badge variant="secondary" className="text-xs">Two-way sync</Badge>
                  )}
                  {provider.capabilities.supportsWebhooks && (
                    <Badge variant="secondary" className="text-xs">Real-time</Badge>
                  )}
                  {provider.capabilities.supportsFieldMapping && (
                    <Badge variant="secondary" className="text-xs">Field mapping</Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )

  const renderOAuthStep = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">Connect to {selectedProviderInfo?.name}</h3>
        <p className="text-sm text-gray-600">
          Configure your integration settings and authorize access
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="integration-name">Integration Name</Label>
          <Input
            id="integration-name"
            value={integrationName}
            onChange={(e) => setIntegrationName(e.target.value)}
            placeholder={`My ${selectedProviderInfo?.name} Integration`}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="sync-interval">Sync Interval (minutes)</Label>
            <Select
              value={config.syncInterval?.toString()}
              onValueChange={(value) => handleConfigUpdate('syncInterval', parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="15">15 minutes</SelectItem>
                <SelectItem value="30">30 minutes</SelectItem>
                <SelectItem value="60">1 hour</SelectItem>
                <SelectItem value="240">4 hours</SelectItem>
                <SelectItem value="1440">24 hours</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {selectedProviderInfo?.capabilities.supportsTwoWaySync && (
            <div className="flex items-center space-x-2">
              <Switch
                id="two-way-sync"
                checked={config.enableTwoWaySync}
                onCheckedChange={(checked) => handleConfigUpdate('enableTwoWaySync', checked)}
              />
              <Label htmlFor="two-way-sync">Enable two-way sync</Label>
            </div>
          )}
        </div>

        {selectedProviderInfo?.capabilities.supportsWebhooks && (
          <div className="flex items-center space-x-2">
            <Switch
              id="webhooks"
              checked={config.webhookEnabled}
              onCheckedChange={(checked) => handleConfigUpdate('webhookEnabled', checked)}
            />
            <Label htmlFor="webhooks">Enable real-time updates (webhooks)</Label>
          </div>
        )}
      </div>

      <div className="flex space-x-3">
        <Button
          variant="outline"
          onClick={() => setCurrentStep('provider')}
        >
          Back
        </Button>
        <Button
          onClick={handleOAuthConnect}
          disabled={!integrationName.trim() || oauthLoading}
          className="flex items-center space-x-2"
        >
          {oauthLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <ExternalLink className="h-4 w-4" />
          )}
          <span>Connect to {selectedProviderInfo?.name}</span>
        </Button>
      </div>
    </div>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 'provider':
        return renderProviderSelection()
      case 'oauth':
        return renderOAuthStep()
      default:
        return null
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Integration</DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          {renderStepContent()}
        </div>
      </DialogContent>
    </Dialog>
  )
}