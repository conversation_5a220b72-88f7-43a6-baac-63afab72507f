import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { TasksController } from '../tasks.controller';
import { QuickTaskService } from '../services/quick-task.service';
import { TasksService } from '../services/tasks.service';
import { PrioritizationService } from '../services/prioritization.service';
import { DailyPlanningService } from '../services/daily-planning.service';
import { CreateQuickTaskDto, QuickTaskDestination } from '../dto/create-quick-task.dto';
import { QuickTaskResponseDto } from '../dto/quick-task-response.dto';

describe('Quick Task API (e2e)', () => {
  let app: INestApplication;
  let quickTaskService: jest.Mocked<QuickTaskService>;

  const mockWorkspaceId = 'workspace-123';

  const mockQuickTaskResponse: QuickTaskResponseDto = {
    success: true,
    message: 'Quick task created successfully in personal inbox',
    task: {
      id: 'task-123',
      workspaceId: mockWorkspaceId,
      integrationId: 'personal',
      externalId: 'personal-123',
      title: 'Test Quick Task',
      description: 'Test description',
      status: 'todo',
      priority: 'medium',
      assigneeId: 'user-123',
      assigneeName: null,
      dueDate: new Date('2024-12-31'),
      estimatedMinutes: null,
      tags: ['quick-task'],
      projectName: null,
      sourceUrl: 'http://localhost:3000/workspaces/workspace-123/tasks',
      metadata: {},
      syncStatus: 'synced',
      priorityScore: 50.0,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastSyncAt: new Date(),
    },
    destination: QuickTaskDestination.PERSONAL_INBOX,
    addAnother: false,
  };

  const mockPreferences = {
    hasGoogleSheetsIntegration: true,
    defaultDestination: QuickTaskDestination.GOOGLE_SHEETS,
    keyboardShortcuts: {
      openQuickTask: 'Ctrl+Shift+T',
      submitAndClose: 'Ctrl+Enter',
      submitAndAddAnother: 'Ctrl+Shift+Enter',
    },
  };

  beforeEach(async () => {
    const mockQuickTaskService = {
      createQuickTask: jest.fn(),
      getQuickTaskPreferences: jest.fn(),
    };

    const mockTasksService = {
      getTasks: jest.fn(),
      createTask: jest.fn(),
      getTask: jest.fn(),
      updateTask: jest.fn(),
      deleteTask: jest.fn(),
      bulkUpdateTasks: jest.fn(),
      searchTasks: jest.fn(),
      getWorkspacePrioritizationSettings: jest.fn(),
      updateWorkspacePrioritizationSettings: jest.fn(),
      updateWorkspacePriorityScores: jest.fn(),
      updateTaskPriorityScore: jest.fn(),
    };

    const mockPrioritizationService = {
      validateWeights: jest.fn(),
      normalizeWeights: jest.fn(),
    };

    const mockDailyPlanningService = {
      createOrUpdateDailyPlan: jest.fn(),
      getDailyPlan: jest.fn(),
      updateDailyPlan: jest.fn(),
      completeDailyPlanTask: jest.fn(),
      deleteDailyPlan: jest.fn(),
      getDailyPlansForRange: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [TasksController],
      providers: [
        {
          provide: TasksService,
          useValue: mockTasksService,
        },
        {
          provide: PrioritizationService,
          useValue: mockPrioritizationService,
        },
        {
          provide: DailyPlanningService,
          useValue: mockDailyPlanningService,
        },
        {
          provide: QuickTaskService,
          useValue: mockQuickTaskService,
        },
      ],
    }).compile();

    app = module.createNestApplication();
    await app.init();

    quickTaskService = module.get(QuickTaskService);
  });

  afterEach(async () => {
    await app.close();
    jest.clearAllMocks();
  });

  describe('POST /workspaces/:workspaceId/tasks/quick', () => {
    it('should create quick task successfully', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
        description: 'Test description',
        dueDate: '2024-12-31T23:59:59Z',
      };

      quickTaskService.createQuickTask.mockResolvedValue(mockQuickTaskResponse);

      const response = await request(app.getHttpServer())
        .post(`/workspaces/${mockWorkspaceId}/tasks/quick`)
        .send(createQuickTaskDto)
        .expect(201);

      expect(response.body).toEqual(mockQuickTaskResponse);
      expect(quickTaskService.createQuickTask).toHaveBeenCalledWith(
        mockWorkspaceId,
        'current-user-id', // TODO: This will be extracted from JWT in real implementation
        createQuickTaskDto,
      );
    });

    it('should create quick task with Google Sheets destination', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
        destination: QuickTaskDestination.GOOGLE_SHEETS,
        addAnother: true,
      };

      const googleSheetsResponse: QuickTaskResponseDto = {
        ...mockQuickTaskResponse,
        message: 'Quick task created successfully in Google Sheets',
        destination: QuickTaskDestination.GOOGLE_SHEETS,
        externalUrl: 'https://docs.google.com/spreadsheets/d/sheet123/edit#gid=0',
        addAnother: true,
      };

      quickTaskService.createQuickTask.mockResolvedValue(googleSheetsResponse);

      const response = await request(app.getHttpServer())
        .post(`/workspaces/${mockWorkspaceId}/tasks/quick`)
        .send(createQuickTaskDto)
        .expect(201);

      expect(response.body).toEqual(googleSheetsResponse);
      expect(response.body.destination).toBe(QuickTaskDestination.GOOGLE_SHEETS);
      expect(response.body.externalUrl).toBeDefined();
      expect(response.body.addAnother).toBe(true);
    });

    it('should validate required fields', async () => {
      const invalidDto = {
        description: 'Missing title',
      };

      await request(app.getHttpServer())
        .post(`/workspaces/${mockWorkspaceId}/tasks/quick`)
        .send(invalidDto)
        .expect(400);

      expect(quickTaskService.createQuickTask).not.toHaveBeenCalled();
    });

    it('should validate enum values', async () => {
      const invalidDto = {
        title: 'Test Task',
        destination: 'invalid_destination',
      };

      await request(app.getHttpServer())
        .post(`/workspaces/${mockWorkspaceId}/tasks/quick`)
        .send(invalidDto)
        .expect(400);

      expect(quickTaskService.createQuickTask).not.toHaveBeenCalled();
    });

    it('should validate date format', async () => {
      const invalidDto = {
        title: 'Test Task',
        dueDate: 'invalid-date',
      };

      await request(app.getHttpServer())
        .post(`/workspaces/${mockWorkspaceId}/tasks/quick`)
        .send(invalidDto)
        .expect(400);

      expect(quickTaskService.createQuickTask).not.toHaveBeenCalled();
    });

    it('should handle service errors', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
        destination: QuickTaskDestination.GOOGLE_SHEETS,
      };

      quickTaskService.createQuickTask.mockRejectedValue(
        new Error('No active Google Sheets integration found')
      );

      await request(app.getHttpServer())
        .post(`/workspaces/${mockWorkspaceId}/tasks/quick`)
        .send(createQuickTaskDto)
        .expect(500);
    });
  });

  describe('GET /workspaces/:workspaceId/tasks/quick/preferences', () => {
    it('should get quick task preferences successfully', async () => {
      quickTaskService.getQuickTaskPreferences.mockResolvedValue(mockPreferences);

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${mockWorkspaceId}/tasks/quick/preferences`)
        .expect(200);

      expect(response.body).toEqual(mockPreferences);
      expect(quickTaskService.getQuickTaskPreferences).toHaveBeenCalledWith(mockWorkspaceId);
    });

    it('should return preferences without Google Sheets integration', async () => {
      const preferencesWithoutGoogleSheets = {
        ...mockPreferences,
        hasGoogleSheetsIntegration: false,
        defaultDestination: QuickTaskDestination.PERSONAL_INBOX,
      };

      quickTaskService.getQuickTaskPreferences.mockResolvedValue(preferencesWithoutGoogleSheets);

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${mockWorkspaceId}/tasks/quick/preferences`)
        .expect(200);

      expect(response.body.hasGoogleSheetsIntegration).toBe(false);
      expect(response.body.defaultDestination).toBe(QuickTaskDestination.PERSONAL_INBOX);
      expect(response.body.keyboardShortcuts).toBeDefined();
    });

    it('should handle service errors', async () => {
      quickTaskService.getQuickTaskPreferences.mockRejectedValue(
        new Error('Database connection failed')
      );

      await request(app.getHttpServer())
        .get(`/workspaces/${mockWorkspaceId}/tasks/quick/preferences`)
        .expect(500);
    });
  });

  describe('API Documentation', () => {
    it('should have proper OpenAPI documentation for quick task endpoints', () => {
      // This test ensures that the endpoints are properly documented
      // In a real application, you might want to test the generated OpenAPI spec
      expect(TasksController.prototype.createQuickTask).toBeDefined();
      expect(TasksController.prototype.getQuickTaskPreferences).toBeDefined();
    });
  });
});