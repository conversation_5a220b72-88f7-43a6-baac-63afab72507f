import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { OAuthCredentials } from '../types';

/**
 * Service for encrypting and decrypting OAuth credentials
 * Uses AES-256-CBC encryption for secure credential storage
 */
@Injectable()
export class CredentialEncryptionService {
  private readonly logger = new Logger(CredentialEncryptionService.name);
  private readonly algorithm = 'aes-256-cbc';
  private readonly keyLength = 32; // 256 bits
  private readonly ivLength = 16; // 128 bits
  private readonly encryptionKey: Buffer;

  constructor(private readonly configService: ConfigService) {
    const key = this.configService.get<string>('ENCRYPTION_KEY');
    if (!key) {
      throw new Error('ENCRYPTION_KEY environment variable is required');
    }

    // Derive a consistent key from the provided key
    this.encryptionKey = crypto.scryptSync(key, 'salt', this.keyLength);
  }

  /**
   * Encrypt OAuth credentials for secure storage
   * @param credentials OAuth credentials to encrypt
   * @returns Encrypted credentials as base64 string
   */
  encrypt(credentials: OAuthCredentials): string {
    try {
      const plaintext = JSON.stringify(credentials);
      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipheriv(this.algorithm, this.encryptionKey, iv);

      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // Combine IV and encrypted data
      const combined = Buffer.concat([
        iv,
        Buffer.from(encrypted, 'hex')
      ]);

      return combined.toString('base64');
    } catch (error) {
      this.logger.error('Failed to encrypt credentials:', error);
      throw new Error('Credential encryption failed');
    }
  }

  /**
   * Decrypt OAuth credentials from storage
   * @param encryptedData Encrypted credentials as base64 string
   * @returns Decrypted OAuth credentials
   */
  decrypt(encryptedData: string): OAuthCredentials {
    try {
      const combined = Buffer.from(encryptedData, 'base64');
      
      // Extract IV and encrypted data
      const iv = combined.subarray(0, this.ivLength);
      const encrypted = combined.subarray(this.ivLength);

      const decipher = crypto.createDecipheriv(this.algorithm, this.encryptionKey, iv);

      let decrypted = decipher.update(encrypted, undefined, 'utf8');
      decrypted += decipher.final('utf8');

      const credentials = JSON.parse(decrypted);
      
      // Convert date strings back to Date objects
      if (credentials.expiresAt) {
        credentials.expiresAt = new Date(credentials.expiresAt);
      }

      return credentials;
    } catch (error) {
      this.logger.error('Failed to decrypt credentials:', error);
      throw new Error('Credential decryption failed');
    }
  }

  /**
   * Validate that encrypted data can be decrypted
   * @param encryptedData Encrypted data to validate
   * @returns True if data can be decrypted successfully
   */
  validate(encryptedData: string): boolean {
    try {
      this.decrypt(encryptedData);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Generate a secure encryption key for environment configuration
   * This is a utility method for initial setup
   * @returns Random encryption key as hex string
   */
  static generateEncryptionKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Mask sensitive credential data for logging
   * @param credentials OAuth credentials to mask
   * @returns Masked credentials safe for logging
   */
  maskCredentials(credentials: OAuthCredentials): Partial<OAuthCredentials> {
    return {
      tokenType: credentials.tokenType,
      scope: credentials.scope,
      expiresAt: credentials.expiresAt,
      userId: credentials.userId,
      userEmail: credentials.userEmail,
      accessToken: credentials.accessToken ? `${credentials.accessToken.substring(0, 8)}...` : undefined,
      refreshToken: credentials.refreshToken ? `${credentials.refreshToken.substring(0, 8)}...` : undefined,
    };
  }
}