import {
  Controller,
  Get,
  Put,
  Post,
  Delete,
  Patch,
  Param,
  Query,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { TasksService } from './services/tasks.service';
import { PrioritizationService } from './services/prioritization.service';
import { DailyPlanningService } from './services/daily-planning.service';
import { QuickTaskService } from './services/quick-task.service';
import { ProgressTrackingService } from './services/progress-tracking.service';
import { ReportExportService } from './services/report-export.service';
import { UpdatePrioritizationSettingsDto } from './dto/update-prioritization-settings.dto';
import { GetTasksQueryDto } from './dto/get-tasks-query.dto';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { BulkUpdateTasksDto } from './dto/bulk-update-tasks.dto';
import { PaginatedTasksResponseDto } from './dto/paginated-tasks-response.dto';
import { CreateDailyPlanDto } from './dto/create-daily-plan.dto';
import { UpdateDailyPlanDto } from './dto/update-daily-plan.dto';
import { CompleteDailyPlanTaskDto } from './dto/complete-daily-plan-task.dto';
import { DailyPlanResponseDto } from './dto/daily-plan-response.dto';
import { CreateQuickTaskDto } from './dto/create-quick-task.dto';
import { QuickTaskResponseDto } from './dto/quick-task-response.dto';
import { ProgressReportFiltersDto } from './dto/progress-report-filters.dto';
import { TaskAgingQueryDto } from './dto/task-aging-query.dto';
import { VelocityReportQueryDto } from './dto/velocity-report-query.dto';
import { ExportReportDto } from './dto/export-report.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('tasks')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('workspaces/:workspaceId/tasks')
export class TasksController {
  constructor(
    private readonly tasksService: TasksService,
    private readonly prioritizationService: PrioritizationService,
    private readonly dailyPlanningService: DailyPlanningService,
    private readonly quickTaskService: QuickTaskService,
    private readonly progressTrackingService: ProgressTrackingService,
    private readonly reportExportService: ReportExportService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get tasks with filtering, sorting, and pagination' })
  @ApiResponse({ 
    status: 200, 
    description: 'Tasks retrieved successfully',
    type: PaginatedTasksResponseDto
  })
  async getTasks(
    @Param('workspaceId') workspaceId: string,
    @Query() query: GetTasksQueryDto,
  ): Promise<PaginatedTasksResponseDto> {
    return this.tasksService.getTasks(workspaceId, query);
  }

  @Get('search')
  @ApiOperation({ summary: 'Search tasks using full-text search' })
  @ApiResponse({ 
    status: 200, 
    description: 'Search results retrieved successfully',
    type: PaginatedTasksResponseDto
  })
  async searchTasks(
    @Param('workspaceId') workspaceId: string,
    @Query('q') searchQuery: string,
    @Query() query: GetTasksQueryDto,
  ): Promise<PaginatedTasksResponseDto> {
    if (!searchQuery || searchQuery.trim().length === 0) {
      return this.tasksService.getTasks(workspaceId, query);
    }
    return this.tasksService.searchTasks(workspaceId, searchQuery.trim(), query);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new task' })
  @ApiResponse({ status: 201, description: 'Task created successfully' })
  async createTask(
    @Param('workspaceId') workspaceId: string,
    @Body() createTaskDto: CreateTaskDto,
  ) {
    return this.tasksService.createTask(workspaceId, createTaskDto);
  }

  @Get(':taskId')
  @ApiOperation({ summary: 'Get a specific task' })
  @ApiResponse({ status: 200, description: 'Task retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Task not found' })
  async getTask(
    @Param('workspaceId') workspaceId: string,
    @Param('taskId') taskId: string,
  ) {
    return this.tasksService.getTask(workspaceId, taskId);
  }

  @Patch(':taskId')
  @ApiOperation({ summary: 'Update a specific task' })
  @ApiResponse({ status: 200, description: 'Task updated successfully' })
  @ApiResponse({ status: 404, description: 'Task not found' })
  async updateTask(
    @Param('workspaceId') workspaceId: string,
    @Param('taskId') taskId: string,
    @Body() updateTaskDto: UpdateTaskDto,
  ) {
    return this.tasksService.updateTask(workspaceId, taskId, updateTaskDto);
  }

  @Delete(':taskId')
  @ApiOperation({ summary: 'Delete a specific task' })
  @ApiResponse({ status: 204, description: 'Task deleted successfully' })
  @ApiResponse({ status: 404, description: 'Task not found' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteTask(
    @Param('workspaceId') workspaceId: string,
    @Param('taskId') taskId: string,
  ) {
    await this.tasksService.deleteTask(workspaceId, taskId);
  }

  @Patch('bulk')
  @ApiOperation({ summary: 'Bulk update multiple tasks' })
  @ApiResponse({ status: 200, description: 'Tasks updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid task IDs or updates' })
  async bulkUpdateTasks(
    @Param('workspaceId') workspaceId: string,
    @Body() bulkUpdateDto: BulkUpdateTasksDto,
  ) {
    return this.tasksService.bulkUpdateTasks(workspaceId, bulkUpdateDto);
  }

  @Get('prioritization/settings')
  @ApiOperation({ summary: 'Get workspace prioritization settings' })
  @ApiResponse({ status: 200, description: 'Prioritization settings retrieved successfully' })
  async getPrioritizationSettings(@Param('workspaceId') workspaceId: string) {
    return this.tasksService.getWorkspacePrioritizationSettings(workspaceId);
  }

  @Put('prioritization/settings')
  @ApiOperation({ summary: 'Update workspace prioritization settings' })
  @ApiResponse({ status: 200, description: 'Prioritization settings updated successfully' })
  @HttpCode(HttpStatus.OK)
  async updatePrioritizationSettings(
    @Param('workspaceId') workspaceId: string,
    @Body() updateDto: UpdatePrioritizationSettingsDto,
  ) {
    // Validate weights if provided
    if (updateDto.weights) {
      const isValid = this.prioritizationService.validateWeights(updateDto.weights);
      if (!isValid) {
        // Normalize weights if they don't sum to 1.0
        updateDto.weights = this.prioritizationService.normalizeWeights(updateDto.weights);
      }
    }

    await this.tasksService.updateWorkspacePrioritizationSettings(workspaceId, updateDto);
    
    return {
      message: 'Prioritization settings updated successfully',
      settings: await this.tasksService.getWorkspacePrioritizationSettings(workspaceId),
    };
  }

  @Post('prioritization/recalculate')
  @ApiOperation({ summary: 'Recalculate priority scores for all tasks in workspace' })
  @ApiResponse({ status: 200, description: 'Priority scores recalculated successfully' })
  @HttpCode(HttpStatus.OK)
  async recalculatePriorityScores(@Param('workspaceId') workspaceId: string) {
    const settings = await this.tasksService.getWorkspacePrioritizationSettings(workspaceId);
    await this.tasksService.updateWorkspacePriorityScores(workspaceId, settings);
    
    return {
      message: 'Priority scores recalculated successfully',
    };
  }

  @Put(':taskId/priority')
  @ApiOperation({ summary: 'Update priority score for a specific task' })
  @ApiResponse({ status: 200, description: 'Task priority score updated successfully' })
  async updateTaskPriorityScore(
    @Param('workspaceId') workspaceId: string,
    @Param('taskId') taskId: string,
  ) {
    const settings = await this.tasksService.getWorkspacePrioritizationSettings(workspaceId);
    const updatedTask = await this.tasksService.updateTaskPriorityScore(taskId, settings);
    
    return {
      message: 'Task priority score updated successfully',
      task: updatedTask,
    };
  }

  // Daily Planning Endpoints

  @Post('daily-plan')
  @ApiOperation({ summary: 'Create or update daily plan for current user' })
  @ApiResponse({ 
    status: 201, 
    description: 'Daily plan created/updated successfully',
    type: DailyPlanResponseDto
  })
  async createDailyPlan(
    @Param('workspaceId') workspaceId: string,
    @Body() createDailyPlanDto: CreateDailyPlanDto,
    // TODO: Get userId from JWT token when auth is implemented
    // For now, we'll use a placeholder
  ): Promise<DailyPlanResponseDto> {
    const userId = 'current-user-id'; // TODO: Extract from JWT
    return this.dailyPlanningService.createOrUpdateDailyPlan(
      workspaceId,
      userId,
      createDailyPlanDto,
    );
  }

  @Get('daily-plan/:planDate')
  @ApiOperation({ summary: 'Get daily plan for a specific date' })
  @ApiResponse({ 
    status: 200, 
    description: 'Daily plan retrieved successfully',
    type: DailyPlanResponseDto
  })
  @ApiResponse({ status: 404, description: 'Daily plan not found' })
  async getDailyPlan(
    @Param('workspaceId') workspaceId: string,
    @Param('planDate') planDate: string,
  ): Promise<DailyPlanResponseDto | null> {
    const userId = 'current-user-id'; // TODO: Extract from JWT
    return this.dailyPlanningService.getDailyPlan(workspaceId, userId, planDate);
  }

  @Patch('daily-plan/:planDate')
  @ApiOperation({ summary: 'Update daily plan (for drag-and-drop reordering)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Daily plan updated successfully',
    type: DailyPlanResponseDto
  })
  @ApiResponse({ status: 404, description: 'Daily plan not found' })
  async updateDailyPlan(
    @Param('workspaceId') workspaceId: string,
    @Param('planDate') planDate: string,
    @Body() updateDailyPlanDto: UpdateDailyPlanDto,
  ): Promise<DailyPlanResponseDto> {
    const userId = 'current-user-id'; // TODO: Extract from JWT
    return this.dailyPlanningService.updateDailyPlan(
      workspaceId,
      userId,
      planDate,
      updateDailyPlanDto,
    );
  }

  @Post('daily-plan/:planDate/tasks/:taskId/complete')
  @ApiOperation({ summary: 'Mark a daily plan task as complete' })
  @ApiResponse({ 
    status: 200, 
    description: 'Task marked as complete successfully',
    type: DailyPlanResponseDto
  })
  @ApiResponse({ status: 404, description: 'Daily plan or task not found' })
  async completeDailyPlanTask(
    @Param('workspaceId') workspaceId: string,
    @Param('planDate') planDate: string,
    @Param('taskId') taskId: string,
    @Body() completeDailyPlanTaskDto: CompleteDailyPlanTaskDto,
  ): Promise<DailyPlanResponseDto> {
    const userId = 'current-user-id'; // TODO: Extract from JWT
    return this.dailyPlanningService.completeDailyPlanTask(
      workspaceId,
      userId,
      planDate,
      taskId,
      completeDailyPlanTaskDto,
    );
  }

  @Delete('daily-plan/:planDate')
  @ApiOperation({ summary: 'Delete daily plan' })
  @ApiResponse({ status: 204, description: 'Daily plan deleted successfully' })
  @ApiResponse({ status: 404, description: 'Daily plan not found' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteDailyPlan(
    @Param('workspaceId') workspaceId: string,
    @Param('planDate') planDate: string,
  ): Promise<void> {
    const userId = 'current-user-id'; // TODO: Extract from JWT
    await this.dailyPlanningService.deleteDailyPlan(workspaceId, userId, planDate);
  }

  @Get('daily-plans')
  @ApiOperation({ summary: 'Get daily plans for a date range' })
  @ApiResponse({ 
    status: 200, 
    description: 'Daily plans retrieved successfully',
    type: [DailyPlanResponseDto]
  })
  async getDailyPlansForRange(
    @Param('workspaceId') workspaceId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ): Promise<DailyPlanResponseDto[]> {
    const userId = 'current-user-id'; // TODO: Extract from JWT
    return this.dailyPlanningService.getDailyPlansForRange(
      workspaceId,
      userId,
      startDate,
      endDate,
    );
  }

  // Quick Task Creation Endpoints

  @Post('quick')
  @ApiOperation({ summary: 'Create a quick task in personal inbox or Google Sheets' })
  @ApiResponse({ 
    status: 201, 
    description: 'Quick task created successfully',
    type: QuickTaskResponseDto
  })
  @ApiResponse({ status: 400, description: 'Invalid request or missing Google Sheets integration' })
  async createQuickTask(
    @Param('workspaceId') workspaceId: string,
    @Body() createQuickTaskDto: CreateQuickTaskDto,
  ): Promise<QuickTaskResponseDto> {
    const userId = 'current-user-id'; // TODO: Extract from JWT
    return this.quickTaskService.createQuickTask(workspaceId, userId, createQuickTaskDto);
  }

  @Get('quick/preferences')
  @ApiOperation({ summary: 'Get quick task creation preferences and keyboard shortcuts' })
  @ApiResponse({ 
    status: 200, 
    description: 'Quick task preferences retrieved successfully'
  })
  async getQuickTaskPreferences(
    @Param('workspaceId') workspaceId: string,
  ) {
    return this.quickTaskService.getQuickTaskPreferences(workspaceId);
  }

  // Progress Tracking and Reporting Endpoints

  @Get('reports/completion-rate')
  @ApiOperation({ summary: 'Get completion rate report by source, project, and time period' })
  @ApiResponse({ 
    status: 200, 
    description: 'Completion rate report generated successfully'
  })
  async getCompletionRateReport(
    @Param('workspaceId') workspaceId: string,
    @Query() filters: ProgressReportFiltersDto,
  ) {
    return this.progressTrackingService.getCompletionRateReport(workspaceId, filters);
  }

  @Get('reports/task-aging')
  @ApiOperation({ summary: 'Get task aging report with configurable age ranges' })
  @ApiResponse({ 
    status: 200, 
    description: 'Task aging report generated successfully'
  })
  async getTaskAgingReport(
    @Param('workspaceId') workspaceId: string,
    @Query() filters: ProgressReportFiltersDto,
    @Query() agingQuery: TaskAgingQueryDto,
  ) {
    return this.progressTrackingService.getTaskAgingReport(
      workspaceId, 
      filters, 
      agingQuery.ageRanges
    );
  }

  @Get('reports/velocity')
  @ApiOperation({ summary: 'Get velocity report with trend analysis' })
  @ApiResponse({ 
    status: 200, 
    description: 'Velocity report generated successfully'
  })
  async getVelocityReport(
    @Param('workspaceId') workspaceId: string,
    @Query() filters: ProgressReportFiltersDto,
    @Query() velocityQuery: VelocityReportQueryDto,
  ) {
    return this.progressTrackingService.getVelocityReport(
      workspaceId, 
      filters, 
      velocityQuery.weeksBack
    );
  }

  @Get('reports/filter-options')
  @ApiOperation({ summary: 'Get available filter options for reports' })
  @ApiResponse({ 
    status: 200, 
    description: 'Report filter options retrieved successfully'
  })
  async getReportFilterOptions(
    @Param('workspaceId') workspaceId: string,
  ) {
    return this.progressTrackingService.getReportFilterOptions(workspaceId);
  }

  @Post('reports/export')
  @ApiOperation({ summary: 'Export report data in CSV or PDF format' })
  @ApiResponse({ 
    status: 200, 
    description: 'Report exported successfully'
  })
  async exportReport(
    @Param('workspaceId') workspaceId: string,
    @Body() exportDto: ExportReportDto,
  ) {
    // Parse filters from JSON string
    const filters = JSON.parse(exportDto.filters);
    
    // Get the appropriate report data
    let reportData;
    switch (exportDto.reportType) {
      case 'completion':
        reportData = await this.progressTrackingService.getCompletionRateReport(workspaceId, filters);
        break;
      case 'aging':
        reportData = await this.progressTrackingService.getTaskAgingReport(workspaceId, filters);
        break;
      case 'velocity':
        reportData = await this.progressTrackingService.getVelocityReport(workspaceId, filters);
        break;
      default:
        throw new Error(`Unsupported report type: ${exportDto.reportType}`);
    }

    // Export the report
    const exportResult = await this.reportExportService.exportReport(
      reportData,
      exportDto.reportType as any,
      exportDto.format
    );

    return {
      filename: exportResult.filename,
      mimeType: exportResult.mimeType,
      data: exportResult.data.toString('base64'),
    };
  }
}