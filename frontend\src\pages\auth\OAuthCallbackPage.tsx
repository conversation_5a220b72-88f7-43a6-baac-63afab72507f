import { useEffect } from 'react'
import { useParams, useNavigate, useSearchParams } from 'react-router-dom'
import { useAuthStore } from '@/store/auth'
import toast from 'react-hot-toast'

export function OAuthCallbackPage() {
  const { provider } = useParams<{ provider: string }>()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const { login, setLoading } = useAuthStore()

  useEffect(() => {
    const handleOAuthCallback = async () => {
      setLoading(true)
      
      try {
        const code = searchParams.get('code')
        const error = searchParams.get('error')
        const state = searchParams.get('state')

        if (error) {
          console.error('OAuth error:', error)
          toast.error('Authentication failed')
          navigate('/auth/login')
          return
        }

        if (!code) {
          console.error('No authorization code received')
          toast.error('Authentication failed')
          navigate('/auth/login')
          return
        }

        // TODO: Implement actual OAuth callback API call
        console.log('OAuth callback:', { provider, code, state })
        
        // Mock successful OAuth login for now
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        const mockUser = {
          id: '1',
          email: '<EMAIL>',
          name: 'John Doe',
          avatar: 'https://via.placeholder.com/32',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
        
        login(mockUser, 'mock-jwt-token')
        toast.success(`Successfully signed in with ${provider}!`)
        navigate('/dashboard')
      } catch (error) {
        console.error('OAuth callback error:', error)
        toast.error('Authentication failed')
        navigate('/auth/login')
      } finally {
        setLoading(false)
      }
    }

    handleOAuthCallback()
  }, [provider, searchParams, navigate, login, setLoading])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-lg shadow-lg p-6 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Completing sign in...
          </h2>
          <p className="text-gray-600">
            Please wait while we complete your {provider} authentication.
          </p>
        </div>
      </div>
    </div>
  )
}