import { apiService } from './api'
import {
  Integration,
  CreateIntegrationRequest,
  UpdateIntegrationRequest,
  SyncResult,
  IntegrationStatusInfo,
  IntegrationProvider,
  ApiResponse
} from '@/types/api'

export class IntegrationService {
  /**
   * Get all integrations for a workspace
   */
  static async getWorkspaceIntegrations(workspaceId: string): Promise<Integration[]> {
    const response = await apiService.get<ApiResponse<Integration[]>>(
      `/workspaces/${workspaceId}/integrations`
    )
    return response.data.data
  }

  /**
   * Get a specific integration
   */
  static async getIntegration(workspaceId: string, integrationId: string): Promise<Integration> {
    const response = await apiService.get<ApiResponse<Integration>>(
      `/workspaces/${workspaceId}/integrations/${integrationId}`
    )
    return response.data.data
  }

  /**
   * Create a new integration
   */
  static async createIntegration(
    workspaceId: string,
    data: CreateIntegrationRequest
  ): Promise<Integration> {
    const response = await apiService.post<ApiResponse<Integration>>(
      `/workspaces/${workspaceId}/integrations`,
      data
    )
    return response.data.data
  }

  /**
   * Update an integration
   */
  static async updateIntegration(
    workspaceId: string,
    integrationId: string,
    data: UpdateIntegrationRequest
  ): Promise<Integration> {
    const response = await apiService.patch<ApiResponse<Integration>>(
      `/workspaces/${workspaceId}/integrations/${integrationId}`,
      data
    )
    return response.data.data
  }

  /**
   * Delete an integration
   */
  static async deleteIntegration(workspaceId: string, integrationId: string): Promise<void> {
    await apiService.delete(`/workspaces/${workspaceId}/integrations/${integrationId}`)
  }

  /**
   * Trigger manual sync for an integration
   */
  static async syncIntegration(workspaceId: string, integrationId: string): Promise<SyncResult> {
    const response = await apiService.post<ApiResponse<SyncResult>>(
      `/workspaces/${workspaceId}/integrations/${integrationId}/sync`
    )
    return response.data.data
  }

  /**
   * Get integration status and health information
   */
  static async getIntegrationStatus(
    workspaceId: string,
    integrationId: string
  ): Promise<IntegrationStatusInfo> {
    const response = await apiService.get<ApiResponse<IntegrationStatusInfo>>(
      `/workspaces/${workspaceId}/integrations/${integrationId}/status`
    )
    return response.data.data
  }

  /**
   * Get OAuth authorization URL for a provider
   */
  static async getOAuthUrl(
    workspaceId: string,
    provider: IntegrationProvider,
    redirectUri: string
  ): Promise<{ authUrl: string; state: string }> {
    const response = await apiService.post<ApiResponse<{ authUrl: string; state: string }>>(
      `/workspaces/${workspaceId}/integrations/oauth/authorize`,
      {
        provider,
        redirectUri
      }
    )
    return response.data.data
  }

  /**
   * Complete OAuth flow and create integration
   */
  static async completeOAuth(
    workspaceId: string,
    provider: IntegrationProvider,
    code: string,
    state: string,
    name: string,
    config: Partial<IntegrationConfig>
  ): Promise<Integration> {
    const response = await apiService.post<ApiResponse<Integration>>(
      `/workspaces/${workspaceId}/integrations/oauth/callback`,
      {
        provider,
        code,
        state,
        name,
        config
      }
    )
    return response.data.data
  }

  /**
   * Get available integration providers and their capabilities
   */
  static async getAvailableProviders(): Promise<Array<{
    provider: IntegrationProvider
    name: string
    description: string
    logoUrl: string
    capabilities: {
      supportsTwoWaySync: boolean
      supportsWebhooks: boolean
      supportsFieldMapping: boolean
    }
  }>> {
    const response = await apiService.get<ApiResponse<Array<{
      provider: IntegrationProvider
      name: string
      description: string
      logoUrl: string
      capabilities: {
        supportsTwoWaySync: boolean
        supportsWebhooks: boolean
        supportsFieldMapping: boolean
      }
    }>>>('/integrations/providers')
    return response.data.data
  }

  /**
   * Test integration connection
   */
  static async testConnection(workspaceId: string, integrationId: string): Promise<{
    success: boolean
    message: string
    details?: Record<string, any>
  }> {
    const response = await apiService.post<ApiResponse<{
      success: boolean
      message: string
      details?: Record<string, any>
    }>>(`/workspaces/${workspaceId}/integrations/${integrationId}/test`)
    return response.data.data
  }
}