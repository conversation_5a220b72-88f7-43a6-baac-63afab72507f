import { Test, TestingModule } from '@nestjs/testing';
import { IntegrationStatusService } from '../integration-status.service';
import { DatabaseService } from '../../../database/database.service';
import { IntegrationStatus, SyncError, SyncResult } from '../../types';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { beforeEach } from 'node:test';
import { describe } from 'node:test';

describe('IntegrationStatusService', () => {
  let service: IntegrationStatusService;
  let mockPrisma: any;

  const mockIntegrationId = 'integration-123';
  const mockWorkspaceId = 'workspace-123';

  beforeEach(async () => {
    mockPrisma = {
      integration: {
        update: jest.fn().mockResolvedValue({}),
        findUnique: jest.fn().mockResolvedValue({}),
        findMany: jest.fn().mockResolvedValue([]),
      },
      syncLog: {
        create: jest.fn().mockResolvedValue({}),
        findMany: jest.fn().mockResolvedValue([]),
        deleteMany: jest.fn().mockResolvedValue({ count: 0 }),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        IntegrationStatusService,
        {
          provide: DatabaseService,
          useValue: mockPrisma,
        },
      ],
    }).compile();

    service = module.get<IntegrationStatusService>(IntegrationStatusService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('updateStatus', () => {
    it('should update integration status successfully', async () => {
      await service.updateStatus(mockIntegrationId, IntegrationStatus.ACTIVE);

      expect(mockPrisma.integration.update).toHaveBeenCalledWith({
        where: { id: mockIntegrationId },
        data: {
          status: IntegrationStatus.ACTIVE,
          updatedAt: expect.any(Date),
        },
      });
    });

    it('should log error when provided', async () => {
      const mockError: SyncError = {
        type: 'AUTH_ERROR',
        message: 'Authentication failed',
        retryable: false,
        timestamp: new Date(),
      };

      await service.updateStatus(mockIntegrationId, IntegrationStatus.ERROR, mockError);

      expect(mockPrisma.syncLog.create).toHaveBeenCalledWith({
        data: {
          integrationId: mockIntegrationId,
          operation: 'ERROR',
          status: 'ERROR',
          tasksProcessed: 0,
          errors: [mockError],
          startedAt: mockError.timestamp,
          completedAt: mockError.timestamp,
        },
      });
    });

    it('should throw error when update fails', async () => {
      mockPrisma.integration.update.mockRejectedValueOnce(new Error('Database error'));

      await expect(service.updateStatus(mockIntegrationId, IntegrationStatus.ACTIVE))
        .rejects.toThrow('Failed to update integration status');
    });
  });

  describe('updateLastSync', () => {
    it('should update last sync timestamp', async () => {
      const timestamp = new Date();

      await service.updateLastSync(mockIntegrationId, timestamp);

      expect(mockPrisma.integration.update).toHaveBeenCalledWith({
        where: { id: mockIntegrationId },
        data: {
          lastSyncAt: timestamp,
          updatedAt: expect.any(Date),
        },
      });
    });

    it('should use current time when no timestamp provided', async () => {
      await service.updateLastSync(mockIntegrationId);

      expect(mockPrisma.integration.update).toHaveBeenCalledWith({
        where: { id: mockIntegrationId },
        data: {
          lastSyncAt: expect.any(Date),
          updatedAt: expect.any(Date),
        },
      });
    });

    it('should throw error when update fails', async () => {
      mockPrisma.integration.update.mockRejectedValueOnce(new Error('Database error'));

      await expect(service.updateLastSync(mockIntegrationId))
        .rejects.toThrow('Failed to update last sync timestamp');
    });
  });

  describe('logSyncOperation', () => {
    it('should log successful sync operation', async () => {
      const mockResult: SyncResult = {
        success: true,
        tasksProcessed: 10,
        tasksCreated: 5,
        tasksUpdated: 3,
        tasksDeleted: 2,
        errors: [],
        conflicts: [],
        duration: 5000,
      };

      await service.logSyncOperation(mockIntegrationId, 'SYNC', mockResult);

      expect(mockPrisma.syncLog.create).toHaveBeenCalledWith({
        data: {
          integrationId: mockIntegrationId,
          operation: 'SYNC',
          status: 'SUCCESS',
          tasksProcessed: 10,
          errors: [],
          startedAt: expect.any(Date),
          completedAt: expect.any(Date),
        },
      });

      expect(mockPrisma.integration.update).toHaveBeenCalledWith({
        where: { id: mockIntegrationId },
        data: {
          status: IntegrationStatus.ACTIVE,
          updatedAt: expect.any(Date),
        },
      });
    });

    it('should log failed sync operation with auth error', async () => {
      const mockError: SyncError = {
        type: 'AUTH_ERROR',
        message: 'Authentication failed',
        retryable: false,
        timestamp: new Date(),
      };

      const mockResult: SyncResult = {
        success: false,
        tasksProcessed: 0,
        tasksCreated: 0,
        tasksUpdated: 0,
        tasksDeleted: 0,
        errors: [mockError],
        conflicts: [],
        duration: 1000,
      };

      await service.logSyncOperation(mockIntegrationId, 'SYNC', mockResult);

      expect(mockPrisma.integration.update).toHaveBeenCalledWith({
        where: { id: mockIntegrationId },
        data: {
          status: IntegrationStatus.EXPIRED,
          updatedAt: expect.any(Date),
        },
      });
    });

    it('should not throw error when logging fails', async () => {
      const mockResult: SyncResult = {
        success: true,
        tasksProcessed: 5,
        tasksCreated: 5,
        tasksUpdated: 0,
        tasksDeleted: 0,
        errors: [],
        conflicts: [],
        duration: 2000,
      };

      mockPrisma.syncLog.create.mockRejectedValueOnce(new Error('Database error'));

      await expect(service.logSyncOperation(mockIntegrationId, 'SYNC', mockResult))
        .resolves.not.toThrow();
    });
  });

  describe('getStatus', () => {
    it('should return integration status with history', async () => {
      const mockIntegration = {
        status: IntegrationStatus.ACTIVE,
        lastSyncAt: new Date(),
      };

      const mockLogs = [
        {
          operation: 'SYNC',
          status: 'SUCCESS',
          tasksProcessed: 10,
          startedAt: new Date(),
          completedAt: new Date(),
          errors: [],
          createdAt: new Date(),
        },
      ];

      mockPrisma.integration.findUnique.mockResolvedValueOnce(mockIntegration);
      mockPrisma.syncLog.findMany.mockResolvedValueOnce(mockLogs);

      const result = await service.getStatus(mockIntegrationId);

      expect(result).toEqual({
        status: IntegrationStatus.ACTIVE,
        lastSyncAt: mockIntegration.lastSyncAt,
        recentErrors: [],
        syncHistory: [
          {
            operation: 'SYNC',
            status: 'SUCCESS',
            tasksProcessed: 10,
            startedAt: mockLogs[0].startedAt,
            completedAt: mockLogs[0].completedAt,
            errorCount: 0,
          },
        ],
      });
    });

    it('should throw error when integration not found', async () => {
      mockPrisma.integration.findUnique.mockResolvedValueOnce(null);

      await expect(service.getStatus(mockIntegrationId))
        .rejects.toThrow('Failed to get integration status');
    });

    it('should extract recent errors from logs', async () => {
      const mockError: SyncError = {
        type: 'API_ERROR',
        message: 'API request failed',
        retryable: true,
        timestamp: new Date(),
      };

      const mockIntegration = {
        status: IntegrationStatus.ERROR,
        lastSyncAt: new Date(),
      };

      const mockLogs = [
        {
          operation: 'SYNC',
          status: 'ERROR',
          tasksProcessed: 0,
          startedAt: new Date(),
          completedAt: new Date(),
          errors: [mockError],
          createdAt: new Date(),
        },
      ];

      mockPrisma.integration.findUnique.mockResolvedValueOnce(mockIntegration);
      mockPrisma.syncLog.findMany.mockResolvedValueOnce(mockLogs);

      const result = await service.getStatus(mockIntegrationId);

      expect(result.recentErrors).toEqual([mockError]);
    });
  });

  describe('needsAttention', () => {
    it('should return true for error status', async () => {
      mockPrisma.integration.findUnique.mockResolvedValueOnce({
        status: IntegrationStatus.ERROR,
      });

      const result = await service.needsAttention(mockIntegrationId);
      expect(result).toBe(true);
    });

    it('should return true for expired status', async () => {
      mockPrisma.integration.findUnique.mockResolvedValueOnce({
        status: IntegrationStatus.EXPIRED,
      });

      const result = await service.needsAttention(mockIntegrationId);
      expect(result).toBe(true);
    });

    it('should return false for active status', async () => {
      mockPrisma.integration.findUnique.mockResolvedValueOnce({
        status: IntegrationStatus.ACTIVE,
      });

      const result = await service.needsAttention(mockIntegrationId);
      expect(result).toBe(false);
    });

    it('should return true when check fails', async () => {
      mockPrisma.integration.findUnique.mockRejectedValueOnce(new Error('Database error'));

      const result = await service.needsAttention(mockIntegrationId);
      expect(result).toBe(true);
    });
  });

  describe('getIntegrationsNeedingAttention', () => {
    it('should return integrations with error or expired status', async () => {
      const mockIntegrations = [
        { id: 'integration-1' },
        { id: 'integration-2' },
      ];

      mockPrisma.integration.findMany.mockResolvedValueOnce(mockIntegrations);

      const result = await service.getIntegrationsNeedingAttention(mockWorkspaceId);

      expect(result).toEqual(['integration-1', 'integration-2']);
      expect(mockPrisma.integration.findMany).toHaveBeenCalledWith({
        where: {
          workspaceId: mockWorkspaceId,
          status: {
            in: [IntegrationStatus.ERROR, IntegrationStatus.EXPIRED],
          },
        },
        select: { id: true },
      });
    });

    it('should return empty array when query fails', async () => {
      mockPrisma.integration.findMany.mockRejectedValueOnce(new Error('Database error'));

      const result = await service.getIntegrationsNeedingAttention(mockWorkspaceId);
      expect(result).toEqual([]);
    });
  });

  describe('cleanupOldLogs', () => {
    it('should delete old sync logs', async () => {
      mockPrisma.syncLog.deleteMany.mockResolvedValueOnce({ count: 5 });

      await service.cleanupOldLogs(30);

      expect(mockPrisma.syncLog.deleteMany).toHaveBeenCalledWith({
        where: {
          createdAt: {
            lt: expect.any(Date),
          },
        },
      });
    });

    it('should use default 30 days when no parameter provided', async () => {
      mockPrisma.syncLog.deleteMany.mockResolvedValueOnce({ count: 3 });

      await service.cleanupOldLogs();

      expect(mockPrisma.syncLog.deleteMany).toHaveBeenCalled();
    });

    it('should not throw error when cleanup fails', async () => {
      mockPrisma.syncLog.deleteMany.mockRejectedValueOnce(new Error('Database error'));

      await expect(service.cleanupOldLogs(30)).resolves.not.toThrow();
    });
  });
});