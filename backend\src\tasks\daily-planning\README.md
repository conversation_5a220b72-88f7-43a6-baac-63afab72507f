# Daily Planning Functionality

This module implements the daily planning feature for TaskUnify, allowing users to create structured daily plans with task selection, time estimation, and progress tracking.

## Features Implemented

### 1. Daily Plan Creation and Management
- **Create Daily Plan**: Users can create a daily plan by selecting tasks and estimating time
- **Update Daily Plan**: Support for drag-and-drop reordering of tasks
- **Delete Daily Plan**: Remove daily plans when no longer needed
- **Get Daily Plan**: Retrieve daily plan for a specific date
- **Get Daily Plans Range**: Retrieve multiple daily plans for a date range

### 2. Task Time Estimation and Tracking
- **Estimated Duration**: Each task in a daily plan has an estimated duration in minutes
- **Actual Time Tracking**: Track actual time spent when completing tasks
- **Progress Calculation**: Calculate completion progress as percentage
- **Time Warnings**: Visual warning when daily plan exceeds 8 hours (480 minutes)

### 3. Task Completion Workflow
- **Mark Tasks Complete**: Complete tasks within daily plans
- **Status Sync**: Automatically update task status to 'done' and mark for sync
- **Progress Updates**: Recalculate total completed time and progress

### 4. Drag-and-Drop Support
- **Order Index**: Tasks have an order index for drag-and-drop functionality
- **Reordering**: Update task order through the API
- **Consistent Ordering**: Tasks are always returned in order index sequence

## Database Schema

### DailyPlan Table
```sql
CREATE TABLE "daily_plans" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "workspace_id" UUID NOT NULL REFERENCES "workspaces"("id"),
    "user_id" UUID NOT NULL REFERENCES "users"("id"),
    "plan_date" DATE NOT NULL,
    "total_estimated_minutes" INTEGER DEFAULT 0,
    "total_completed_minutes" INTEGER DEFAULT 0,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE("workspace_id", "user_id", "plan_date")
);
```

### DailyPlanTask Table
```sql
CREATE TABLE "daily_plan_tasks" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "daily_plan_id" UUID NOT NULL REFERENCES "daily_plans"("id"),
    "task_id" UUID NOT NULL REFERENCES "tasks"("id"),
    "estimated_minutes" INTEGER DEFAULT 0,
    "actual_minutes" INTEGER,
    "order_index" INTEGER DEFAULT 0,
    "completed_at" TIMESTAMP,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE("daily_plan_id", "task_id")
);
```

## API Endpoints

### POST /workspaces/:workspaceId/tasks/daily-plan
Create or update a daily plan for the current user.

**Request Body:**
```json
{
  "planDate": "2024-01-15",
  "tasks": [
    {
      "taskId": "task-uuid-1",
      "estimatedMinutes": 120,
      "orderIndex": 0
    },
    {
      "taskId": "task-uuid-2", 
      "estimatedMinutes": 60,
      "orderIndex": 1
    }
  ]
}
```

**Response:**
```json
{
  "id": "daily-plan-uuid",
  "workspaceId": "workspace-uuid",
  "userId": "user-uuid",
  "planDate": "2024-01-15T00:00:00.000Z",
  "totalEstimatedMinutes": 180,
  "totalCompletedMinutes": 0,
  "completionProgress": 0,
  "exceedsRecommendedTime": false,
  "tasks": [
    {
      "id": "daily-plan-task-uuid",
      "taskId": "task-uuid-1",
      "estimatedMinutes": 120,
      "actualMinutes": null,
      "orderIndex": 0,
      "completedAt": null,
      "task": {
        "id": "task-uuid-1",
        "title": "Task Title",
        "description": "Task Description",
        "status": "todo",
        "priority": "high",
        "priorityScore": 85.5,
        "dueDate": "2024-01-15T00:00:00.000Z",
        "tags": ["urgent"],
        "projectName": "Project Name",
        "sourceUrl": "https://example.com/task"
      }
    }
  ],
  "createdAt": "2024-01-15T10:00:00.000Z",
  "updatedAt": "2024-01-15T10:00:00.000Z"
}
```

### GET /workspaces/:workspaceId/tasks/daily-plan/:planDate
Get daily plan for a specific date.

**Response:** Same as POST response, or `null` if no plan exists.

### PATCH /workspaces/:workspaceId/tasks/daily-plan/:planDate
Update daily plan (for drag-and-drop reordering).

**Request Body:**
```json
{
  "tasks": [
    {
      "taskId": "task-uuid-2",
      "estimatedMinutes": 60,
      "orderIndex": 0
    },
    {
      "taskId": "task-uuid-1",
      "estimatedMinutes": 120,
      "orderIndex": 1
    }
  ]
}
```

### POST /workspaces/:workspaceId/tasks/daily-plan/:planDate/tasks/:taskId/complete
Mark a daily plan task as complete.

**Request Body:**
```json
{
  "actualMinutes": 90
}
```

**Response:** Updated daily plan with completion status.

### DELETE /workspaces/:workspaceId/tasks/daily-plan/:planDate
Delete a daily plan.

**Response:** 204 No Content

### GET /workspaces/:workspaceId/tasks/daily-plans?startDate=2024-01-15&endDate=2024-01-17
Get daily plans for a date range.

**Response:** Array of daily plan objects.

## Requirements Satisfied

This implementation satisfies all requirements from Requirement 4:

✅ **4.1**: Drag-and-drop interface to move tasks into "Today" view (via order index)
✅ **4.2**: Allow setting estimated duration in minutes
✅ **4.3**: Update task status and sync to source tool when marked complete
✅ **4.4**: Show total estimated time and completion progress
✅ **4.5**: Visual warning when daily plan exceeds 8 hours

## Usage Examples

### Creating a Daily Plan
```typescript
const dailyPlan = await dailyPlanningService.createOrUpdateDailyPlan(
  'workspace-id',
  'user-id',
  {
    planDate: '2024-01-15',
    tasks: [
      { taskId: 'task-1', estimatedMinutes: 120, orderIndex: 0 },
      { taskId: 'task-2', estimatedMinutes: 60, orderIndex: 1 }
    ]
  }
);
```

### Completing a Task
```typescript
const updatedPlan = await dailyPlanningService.completeDailyPlanTask(
  'workspace-id',
  'user-id', 
  '2024-01-15',
  'task-1',
  { actualMinutes: 90 }
);
```

### Reordering Tasks
```typescript
const reorderedPlan = await dailyPlanningService.updateDailyPlan(
  'workspace-id',
  'user-id',
  '2024-01-15',
  {
    tasks: [
      { taskId: 'task-2', estimatedMinutes: 60, orderIndex: 0 },
      { taskId: 'task-1', estimatedMinutes: 120, orderIndex: 1 }
    ]
  }
);
```

## Testing

The implementation includes comprehensive unit tests and integration tests:

- **Unit Tests**: `src/tasks/services/__tests__/daily-planning.service.spec.ts`
- **API Tests**: `src/tasks/__tests__/daily-planning.api.spec.ts`

Tests cover:
- Daily plan creation and updates
- Task completion workflows
- Drag-and-drop reordering
- Time estimation and progress tracking
- Error handling for edge cases
- Database constraints and relationships

## Future Enhancements

Potential improvements for future iterations:

1. **Recurring Daily Plans**: Template-based daily plans that repeat
2. **Time Blocking**: Visual time blocks for better scheduling
3. **Break Scheduling**: Automatic break suggestions for long work sessions
4. **Calendar Integration**: Sync with external calendar systems
5. **Team Daily Plans**: Collaborative daily planning for teams
6. **Analytics**: Daily planning effectiveness metrics