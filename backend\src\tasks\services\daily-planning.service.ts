import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { DatabaseService } from '../../database/database.service';
import { CreateDailyPlanDto } from '../dto/create-daily-plan.dto';
import { UpdateDailyPlanDto } from '../dto/update-daily-plan.dto';
import { CompleteDailyPlanTaskDto } from '../dto/complete-daily-plan-task.dto';
import { DailyPlanResponseDto, DailyPlanTaskResponseDto } from '../dto/daily-plan-response.dto';

@Injectable()
export class DailyPlanningService {
  constructor(private readonly databaseService: DatabaseService) {}

  /**
   * Create or update a daily plan for a user
   */
  async createOrUpdateDailyPlan(
    workspaceId: string,
    userId: string,
    createDailyPlanDto: CreateDailyPlanDto,
  ): Promise<DailyPlanResponseDto> {
    const planDate = new Date(createDailyPlanDto.planDate);
    
    // Verify all tasks belong to the workspace
    const taskIds = createDailyPlanDto.tasks.map(t => t.taskId);
    const tasks = await this.databaseService.task.findMany({
      where: {
        id: { in: taskIds },
        workspaceId,
      },
    });

    if (tasks.length !== taskIds.length) {
      throw new BadRequestException('Some tasks not found or do not belong to this workspace');
    }

    // Calculate total estimated time
    const totalEstimatedMinutes = createDailyPlanDto.tasks.reduce(
      (sum, task) => sum + task.estimatedMinutes,
      0,
    );

    // Create or update daily plan
    const dailyPlan = await this.databaseService.dailyPlan.upsert({
      where: {
        workspaceId_userId_planDate: {
          workspaceId,
          userId,
          planDate,
        },
      },
      create: {
        workspaceId,
        userId,
        planDate,
        totalEstimatedMinutes,
        totalCompletedMinutes: 0,
      },
      update: {
        totalEstimatedMinutes,
      },
    });

    // Remove existing daily plan tasks
    await this.databaseService.dailyPlanTask.deleteMany({
      where: { dailyPlanId: dailyPlan.id },
    });

    // Create new daily plan tasks
    const dailyPlanTasks = await Promise.all(
      createDailyPlanDto.tasks.map(taskDto =>
        this.databaseService.dailyPlanTask.create({
          data: {
            dailyPlanId: dailyPlan.id,
            taskId: taskDto.taskId,
            estimatedMinutes: taskDto.estimatedMinutes,
            orderIndex: taskDto.orderIndex,
          },
        }),
      ),
    );

    return this.getDailyPlan(workspaceId, userId, createDailyPlanDto.planDate);
  }

  /**
   * Get daily plan for a specific date
   */
  async getDailyPlan(
    workspaceId: string,
    userId: string,
    planDate: string,
  ): Promise<DailyPlanResponseDto | null> {
    const date = new Date(planDate);
    
    const dailyPlan = await this.databaseService.dailyPlan.findUnique({
      where: {
        workspaceId_userId_planDate: {
          workspaceId,
          userId,
          planDate: date,
        },
      },
      include: {
        dailyPlanTasks: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
                description: true,
                status: true,
                priority: true,
                priorityScore: true,
                dueDate: true,
                tags: true,
                projectName: true,
                sourceUrl: true,
              },
            },
          },
          orderBy: { orderIndex: 'asc' },
        },
      },
    });

    if (!dailyPlan) {
      return null;
    }

    return this.formatDailyPlanResponse(dailyPlan);
  }

  /**
   * Update daily plan tasks (for drag-and-drop reordering)
   */
  async updateDailyPlan(
    workspaceId: string,
    userId: string,
    planDate: string,
    updateDailyPlanDto: UpdateDailyPlanDto,
  ): Promise<DailyPlanResponseDto> {
    const date = new Date(planDate);
    
    const dailyPlan = await this.databaseService.dailyPlan.findUnique({
      where: {
        workspaceId_userId_planDate: {
          workspaceId,
          userId,
          planDate: date,
        },
      },
    });

    if (!dailyPlan) {
      throw new NotFoundException('Daily plan not found');
    }

    if (updateDailyPlanDto.tasks) {
      // Verify all tasks belong to the workspace
      const taskIds = updateDailyPlanDto.tasks.map(t => t.taskId);
      const tasks = await this.databaseService.task.findMany({
        where: {
          id: { in: taskIds },
          workspaceId,
        },
      });

      if (tasks.length !== taskIds.length) {
        throw new BadRequestException('Some tasks not found or do not belong to this workspace');
      }

      // Calculate new total estimated time
      const totalEstimatedMinutes = updateDailyPlanDto.tasks.reduce(
        (sum, task) => sum + task.estimatedMinutes,
        0,
      );

      // Update daily plan
      await this.databaseService.dailyPlan.update({
        where: { id: dailyPlan.id },
        data: { totalEstimatedMinutes },
      });

      // Remove existing daily plan tasks
      await this.databaseService.dailyPlanTask.deleteMany({
        where: { dailyPlanId: dailyPlan.id },
      });

      // Create new daily plan tasks
      await Promise.all(
        updateDailyPlanDto.tasks.map(taskDto =>
          this.databaseService.dailyPlanTask.create({
            data: {
              dailyPlanId: dailyPlan.id,
              taskId: taskDto.taskId,
              estimatedMinutes: taskDto.estimatedMinutes,
              orderIndex: taskDto.orderIndex,
            },
          }),
        ),
      );
    }

    return this.getDailyPlan(workspaceId, userId, planDate);
  }

  /**
   * Mark a daily plan task as complete
   */
  async completeDailyPlanTask(
    workspaceId: string,
    userId: string,
    planDate: string,
    taskId: string,
    completeDailyPlanTaskDto: CompleteDailyPlanTaskDto,
  ): Promise<DailyPlanResponseDto> {
    const date = new Date(planDate);
    
    const dailyPlan = await this.databaseService.dailyPlan.findUnique({
      where: {
        workspaceId_userId_planDate: {
          workspaceId,
          userId,
          planDate: date,
        },
      },
    });

    if (!dailyPlan) {
      throw new NotFoundException('Daily plan not found');
    }

    const dailyPlanTask = await this.databaseService.dailyPlanTask.findUnique({
      where: {
        dailyPlanId_taskId: {
          dailyPlanId: dailyPlan.id,
          taskId,
        },
      },
    });

    if (!dailyPlanTask) {
      throw new NotFoundException('Task not found in daily plan');
    }

    // Mark task as complete
    const actualMinutes = completeDailyPlanTaskDto.actualMinutes || dailyPlanTask.estimatedMinutes;
    
    await this.databaseService.dailyPlanTask.update({
      where: { id: dailyPlanTask.id },
      data: {
        completedAt: new Date(),
        actualMinutes,
      },
    });

    // Update the actual task status to completed
    await this.databaseService.task.update({
      where: { id: taskId },
      data: {
        status: 'done',
        syncStatus: 'pending', // Mark for sync to external tools
      },
    });

    // Recalculate total completed minutes for the daily plan
    const completedTasks = await this.databaseService.dailyPlanTask.findMany({
      where: {
        dailyPlanId: dailyPlan.id,
        completedAt: { not: null },
      },
    });

    const totalCompletedMinutes = completedTasks.reduce(
      (sum, task) => sum + (task.actualMinutes || task.estimatedMinutes),
      0,
    );

    await this.databaseService.dailyPlan.update({
      where: { id: dailyPlan.id },
      data: { totalCompletedMinutes },
    });

    return this.getDailyPlan(workspaceId, userId, planDate);
  }

  /**
   * Delete a daily plan
   */
  async deleteDailyPlan(
    workspaceId: string,
    userId: string,
    planDate: string,
  ): Promise<void> {
    const date = new Date(planDate);
    
    const dailyPlan = await this.databaseService.dailyPlan.findUnique({
      where: {
        workspaceId_userId_planDate: {
          workspaceId,
          userId,
          planDate: date,
        },
      },
    });

    if (!dailyPlan) {
      throw new NotFoundException('Daily plan not found');
    }

    await this.databaseService.dailyPlan.delete({
      where: { id: dailyPlan.id },
    });
  }

  /**
   * Get daily plans for a date range
   */
  async getDailyPlansForRange(
    workspaceId: string,
    userId: string,
    startDate: string,
    endDate: string,
  ): Promise<DailyPlanResponseDto[]> {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    const dailyPlans = await this.databaseService.dailyPlan.findMany({
      where: {
        workspaceId,
        userId,
        planDate: {
          gte: start,
          lte: end,
        },
      },
      include: {
        dailyPlanTasks: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
                description: true,
                status: true,
                priority: true,
                priorityScore: true,
                dueDate: true,
                tags: true,
                projectName: true,
                sourceUrl: true,
              },
            },
          },
          orderBy: { orderIndex: 'asc' },
        },
      },
      orderBy: { planDate: 'asc' },
    });

    return dailyPlans.map(plan => this.formatDailyPlanResponse(plan));
  }

  /**
   * Format daily plan response with calculated fields
   */
  private formatDailyPlanResponse(dailyPlan: any): DailyPlanResponseDto {
    const tasks: DailyPlanTaskResponseDto[] = dailyPlan.dailyPlanTasks.map(dpt => ({
      id: dpt.id,
      taskId: dpt.taskId,
      estimatedMinutes: dpt.estimatedMinutes,
      actualMinutes: dpt.actualMinutes,
      orderIndex: dpt.orderIndex,
      completedAt: dpt.completedAt,
      task: {
        id: dpt.task.id,
        title: dpt.task.title,
        description: dpt.task.description,
        status: dpt.task.status,
        priority: dpt.task.priority,
        priorityScore: Number(dpt.task.priorityScore),
        dueDate: dpt.task.dueDate,
        tags: dpt.task.tags,
        projectName: dpt.task.projectName,
        sourceUrl: dpt.task.sourceUrl,
      },
    }));

    const completedTasks = tasks.filter(t => t.completedAt !== null);
    const completionProgress = dailyPlan.totalEstimatedMinutes > 0 
      ? Math.round((completedTasks.length / tasks.length) * 100)
      : 0;

    const exceedsRecommendedTime = dailyPlan.totalEstimatedMinutes > 480; // 8 hours

    return {
      id: dailyPlan.id,
      workspaceId: dailyPlan.workspaceId,
      userId: dailyPlan.userId,
      planDate: dailyPlan.planDate,
      totalEstimatedMinutes: dailyPlan.totalEstimatedMinutes,
      totalCompletedMinutes: dailyPlan.totalCompletedMinutes,
      completionProgress,
      exceedsRecommendedTime,
      tasks,
      createdAt: dailyPlan.createdAt,
      updatedAt: dailyPlan.updatedAt,
    };
  }
}