import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { TasksService } from '../tasks.service';
import { PrioritizationService } from '../prioritization.service';
import { DatabaseService } from '../../../database/database.service';
import { PrioritizationSettings, DEFAULT_PRIORITIZATION_SETTINGS } from '../../interfaces/prioritization.interface';
import { GetTasksQueryDto, TaskSortField, SortOrder } from '../../dto/get-tasks-query.dto';
import { CreateTaskDto } from '../../dto/create-task.dto';
import { UpdateTaskDto } from '../../dto/update-task.dto';
import { BulkUpdateTasksDto } from '../../dto/bulk-update-tasks.dto';

describe('TasksService', () => {
  let service: TasksService;
  let databaseService: jest.Mocked<DatabaseService>;
  let prioritizationService: jest.Mocked<PrioritizationService>;

  const mockWorkspaceId = 'workspace-123';
  const mockTaskId = 'task-123';

  const mockTask = {
    id: mockTaskId,
    workspaceId: mockWorkspaceId,
    integrationId: 'integration-123',
    externalId: 'ext-123',
    title: 'Test Task',
    description: 'Test Description',
    status: 'todo',
    priority: 'high',
    priorityScore: 75.5,
    assigneeId: 'user-123',
    assigneeName: 'John Doe',
    dueDate: new Date('2024-12-31'),
    estimatedMinutes: 60,
    tags: ['urgent'],
    projectName: 'Test Project',
    sourceUrl: 'https://example.com/task/123',
    metadata: {},
    syncStatus: 'synced',
    createdAt: new Date(),
    updatedAt: new Date(),
    lastSyncAt: new Date(),
  };

  const mockWorkspace = {
    id: mockWorkspaceId,
    name: 'Test Workspace',
    slug: 'test-workspace',
    ownerId: 'owner-123',
    settings: {
      prioritization: {
        weights: {
          dueDateProximity: 0.5,
          effortEstimate: 0.2,
          businessImpact: 0.2,
          contextSwitching: 0.1,
        },
      },
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockDatabaseService = {
      task: {
        findMany: jest.fn(),
        findFirst: jest.fn(),
        findUnique: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        updateMany: jest.fn(),
        delete: jest.fn(),
        count: jest.fn(),
      },
      workspace: {
        findUnique: jest.fn(),
        update: jest.fn(),
      },
    };

    const mockPrioritizationService = {
      calculatePriorityScore: jest.fn(),
      getDefaultSettings: jest.fn(),
      validateWeights: jest.fn(),
      normalizeWeights: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TasksService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
        {
          provide: PrioritizationService,
          useValue: mockPrioritizationService,
        },
      ],
    }).compile();

    service = module.get<TasksService>(TasksService);
    databaseService = module.get(DatabaseService);
    prioritizationService = module.get(PrioritizationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('updateWorkspacePriorityScores', () => {
    it('should update priority scores for all tasks in workspace', async () => {
      const mockTasks = [
        {
          id: 'task-1',
          dueDate: new Date(),
          estimatedMinutes: 60,
          priority: 'high',
          tags: ['urgent'],
          projectName: 'Project A',
          assigneeId: 'user-1',
          status: 'todo',
        },
        {
          id: 'task-2',
          dueDate: new Date(),
          estimatedMinutes: 30,
          priority: 'medium',
          tags: [],
          projectName: 'Project B',
          assigneeId: 'user-2',
          status: 'in_progress',
        },
      ];

      databaseService.task.findMany.mockResolvedValue(mockTasks);
      prioritizationService.calculatePriorityScore.mockReturnValueOnce(85.5).mockReturnValueOnce(65.2);
      databaseService.task.update.mockResolvedValue(mockTask);

      await service.updateWorkspacePriorityScores(mockWorkspaceId);

      expect(databaseService.task.findMany).toHaveBeenCalledWith({
        where: { workspaceId: mockWorkspaceId },
        select: {
          id: true,
          dueDate: true,
          estimatedMinutes: true,
          priority: true,
          tags: true,
          projectName: true,
          assigneeId: true,
          status: true,
        },
      });

      expect(prioritizationService.calculatePriorityScore).toHaveBeenCalledTimes(2);
      expect(databaseService.task.update).toHaveBeenCalledTimes(2);
      expect(databaseService.task.update).toHaveBeenCalledWith({
        where: { id: 'task-1' },
        data: { priorityScore: 85.5 },
      });
      expect(databaseService.task.update).toHaveBeenCalledWith({
        where: { id: 'task-2' },
        data: { priorityScore: 65.2 },
      });
    });

    it('should use custom settings when provided', async () => {
      const customSettings: PrioritizationSettings = {
        ...DEFAULT_PRIORITIZATION_SETTINGS,
        weights: {
          dueDateProximity: 0.6,
          effortEstimate: 0.1,
          businessImpact: 0.2,
          contextSwitching: 0.1,
        },
      };

      databaseService.task.findMany.mockResolvedValue([]);

      await service.updateWorkspacePriorityScores(mockWorkspaceId, customSettings);

      expect(databaseService.task.findMany).toHaveBeenCalled();
    });
  });

  describe('updateTaskPriorityScore', () => {
    it('should update priority score for a single task', async () => {
      databaseService.task.findUnique.mockResolvedValue(mockTask);
      prioritizationService.calculatePriorityScore.mockReturnValue(90.0);
      databaseService.task.update.mockResolvedValue({ ...mockTask, priorityScore: 90.0 });

      const result = await service.updateTaskPriorityScore(mockTaskId);

      expect(databaseService.task.findUnique).toHaveBeenCalledWith({
        where: { id: mockTaskId },
      });
      expect(prioritizationService.calculatePriorityScore).toHaveBeenCalledWith(
        expect.objectContaining({
          id: mockTaskId,
          dueDate: mockTask.dueDate,
          estimatedMinutes: mockTask.estimatedMinutes,
          priority: mockTask.priority,
          tags: mockTask.tags,
          projectName: mockTask.projectName,
          assigneeId: mockTask.assigneeId,
          status: mockTask.status,
        }),
        undefined,
      );
      expect(databaseService.task.update).toHaveBeenCalledWith({
        where: { id: mockTaskId },
        data: { priorityScore: 90.0 },
      });
      expect(result.priorityScore).toBe(90.0);
    });

    it('should throw error when task not found', async () => {
      databaseService.task.findUnique.mockResolvedValue(null);

      await expect(service.updateTaskPriorityScore(mockTaskId)).rejects.toThrow(
        `Task with id ${mockTaskId} not found`,
      );
    });
  });

  describe('getTasksByPriority', () => {
    it('should return tasks ordered by priority score', async () => {
      const mockTasks = [mockTask];
      databaseService.task.findMany.mockResolvedValue(mockTasks);

      const result = await service.getTasksByPriority(mockWorkspaceId, 10, 0);

      expect(databaseService.task.findMany).toHaveBeenCalledWith({
        where: { workspaceId: mockWorkspaceId },
        orderBy: [
          { priorityScore: 'desc' },
          { dueDate: 'asc' },
          { createdAt: 'desc' },
        ],
        take: 10,
        skip: 0,
      });
      expect(result).toEqual(mockTasks);
    });

    it('should handle pagination parameters', async () => {
      databaseService.task.findMany.mockResolvedValue([]);

      await service.getTasksByPriority(mockWorkspaceId, 20, 40);

      expect(databaseService.task.findMany).toHaveBeenCalledWith({
        where: { workspaceId: mockWorkspaceId },
        orderBy: [
          { priorityScore: 'desc' },
          { dueDate: 'asc' },
          { createdAt: 'desc' },
        ],
        take: 20,
        skip: 40,
      });
    });

    it('should handle undefined pagination parameters', async () => {
      databaseService.task.findMany.mockResolvedValue([]);

      await service.getTasksByPriority(mockWorkspaceId);

      expect(databaseService.task.findMany).toHaveBeenCalledWith({
        where: { workspaceId: mockWorkspaceId },
        orderBy: [
          { priorityScore: 'desc' },
          { dueDate: 'asc' },
          { createdAt: 'desc' },
        ],
        take: undefined,
        skip: undefined,
      });
    });
  });

  describe('getWorkspacePrioritizationSettings', () => {
    it('should return custom prioritization settings when they exist', async () => {
      databaseService.workspace.findUnique.mockResolvedValue(mockWorkspace);
      prioritizationService.getDefaultSettings.mockReturnValue(DEFAULT_PRIORITIZATION_SETTINGS);

      const result = await service.getWorkspacePrioritizationSettings(mockWorkspaceId);

      expect(databaseService.workspace.findUnique).toHaveBeenCalledWith({
        where: { id: mockWorkspaceId },
        select: { settings: true },
      });
      expect(result.weights.dueDateProximity).toBe(0.5);
    });

    it('should return default settings when no custom settings exist', async () => {
      const workspaceWithoutSettings = {
        ...mockWorkspace,
        settings: {},
      };
      databaseService.workspace.findUnique.mockResolvedValue(workspaceWithoutSettings);
      prioritizationService.getDefaultSettings.mockReturnValue(DEFAULT_PRIORITIZATION_SETTINGS);

      const result = await service.getWorkspacePrioritizationSettings(mockWorkspaceId);

      expect(result).toEqual(DEFAULT_PRIORITIZATION_SETTINGS);
    });

    it('should throw error when workspace not found', async () => {
      databaseService.workspace.findUnique.mockResolvedValue(null);

      await expect(service.getWorkspacePrioritizationSettings(mockWorkspaceId)).rejects.toThrow(
        `Workspace with id ${mockWorkspaceId} not found`,
      );
    });
  });

  describe('updateWorkspacePrioritizationSettings', () => {
    it('should update workspace prioritization settings and recalculate scores', async () => {
      const newSettings = {
        weights: {
          dueDateProximity: 0.6,
          effortEstimate: 0.1,
          businessImpact: 0.2,
          contextSwitching: 0.1,
        },
      };

      databaseService.workspace.findUnique.mockResolvedValue(mockWorkspace);
      databaseService.workspace.update.mockResolvedValue(mockWorkspace);
      databaseService.task.findMany.mockResolvedValue([]);
      prioritizationService.getDefaultSettings.mockReturnValue(DEFAULT_PRIORITIZATION_SETTINGS);

      await service.updateWorkspacePrioritizationSettings(mockWorkspaceId, newSettings);

      expect(databaseService.workspace.findUnique).toHaveBeenCalledWith({
        where: { id: mockWorkspaceId },
        select: { settings: true },
      });
      expect(databaseService.workspace.update).toHaveBeenCalledWith({
        where: { id: mockWorkspaceId },
        data: {
          settings: expect.objectContaining({
            prioritization: expect.objectContaining({
              weights: newSettings.weights,
            }),
          }),
        },
      });
      expect(databaseService.task.findMany).toHaveBeenCalled(); // Called during recalculation
    });

    it('should throw error when workspace not found', async () => {
      databaseService.workspace.findUnique.mockResolvedValue(null);

      await expect(
        service.updateWorkspacePrioritizationSettings(mockWorkspaceId, {}),
      ).rejects.toThrow(`Workspace with id ${mockWorkspaceId} not found`);
    });

    it('should merge settings with existing workspace settings', async () => {
      const existingWorkspace = {
        ...mockWorkspace,
        settings: {
          someOtherSetting: 'value',
          prioritization: {
            maxScore: 50,
          },
        },
      };

      const newSettings = {
        weights: {
          dueDateProximity: 0.7,
          effortEstimate: 0.1,
          businessImpact: 0.1,
          contextSwitching: 0.1,
        },
      };

      databaseService.workspace.findUnique.mockResolvedValue(existingWorkspace);
      databaseService.workspace.update.mockResolvedValue(existingWorkspace);
      databaseService.task.findMany.mockResolvedValue([]);
      prioritizationService.getDefaultSettings.mockReturnValue(DEFAULT_PRIORITIZATION_SETTINGS);

      await service.updateWorkspacePrioritizationSettings(mockWorkspaceId, newSettings);

      expect(databaseService.workspace.update).toHaveBeenCalledWith({
        where: { id: mockWorkspaceId },
        data: {
          settings: {
            someOtherSetting: 'value',
            prioritization: expect.objectContaining({
              maxScore: 50, // Preserved from existing settings
              weights: newSettings.weights, // Updated with new settings
            }),
          },
        },
      });
    });
  });
});