import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from 'react-router-dom'
import { vi } from 'vitest'
import { WorkspacesPage } from '../WorkspacesPage'
import { useWorkspaceStore } from '@/store/workspace'
import { workspaceService } from '@/services/workspace'
import toast from 'react-hot-toast'

// Mock dependencies
vi.mock('@/store/workspace')
vi.mock('@/services/workspace')
vi.mock('react-hot-toast')

const mockUseWorkspaceStore = useWorkspaceStore as any
const mockWorkspaceService = workspaceService as any
const mockToast = toast as any

// Mock react-router-dom
const mockSearchParams = new URLSearchParams()
const mockSetSearchParams = vi.fn()

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useSearchParams: () => [mockSearchParams, mockSetSearchParams],
  }
})

const mockWorkspaces = [
  {
    id: '1',
    name: 'Personal Workspace',
    slug: 'personal',
    ownerId: 'user1',
    settings: {
      priorityWeights: {
        dueDateProximity: 0.3,
        effortEstimate: 0.2,
        businessImpact: 0.3,
        contextSwitching: 0.2
      },
      defaultSyncInterval: 15,
      enableTwoWaySync: true
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'Team Workspace',
    slug: 'team',
    ownerId: 'user1',
    settings: {
      priorityWeights: {
        dueDateProximity: 0.3,
        effortEstimate: 0.2,
        businessImpact: 0.3,
        contextSwitching: 0.2
      },
      defaultSyncInterval: 15,
      enableTwoWaySync: true
    },
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z'
  }
]

const mockStoreActions = {
  setWorkspaces: vi.fn(),
  addWorkspace: vi.fn(),
  updateWorkspace: vi.fn(),
  removeWorkspace: vi.fn(),
  switchWorkspace: vi.fn(),
  setCurrentWorkspace: vi.fn(),
  setMembers: vi.fn(),
  addMember: vi.fn(),
  updateMember: vi.fn(),
  removeMember: vi.fn(),
  setLoading: vi.fn(),
}

const renderWorkspacesPage = () => {
  return render(
    <BrowserRouter>
      <WorkspacesPage />
    </BrowserRouter>
  )
}

describe('WorkspacesPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockSearchParams.clear()
    
    mockUseWorkspaceStore.mockReturnValue({
      workspaces: mockWorkspaces,
      currentWorkspace: mockWorkspaces[0],
      members: [],
      isLoading: false,
      ...mockStoreActions
    })
    
    mockWorkspaceService.getWorkspaces.mockResolvedValue(mockWorkspaces)
    mockWorkspaceService.createWorkspace.mockResolvedValue(mockWorkspaces[0])
    mockWorkspaceService.updateWorkspace.mockResolvedValue(mockWorkspaces[0])
    mockWorkspaceService.deleteWorkspace.mockResolvedValue()
  })

  it('renders page title and description', () => {
    renderWorkspacesPage()
    
    expect(screen.getByText('Workspaces')).toBeInTheDocument()
    expect(screen.getByText('Manage your workspaces and team members')).toBeInTheDocument()
  })

  it('shows loading state initially', () => {
    mockUseWorkspaceStore.mockReturnValue({
      workspaces: [],
      currentWorkspace: null,
      members: [],
      isLoading: true,
      ...mockStoreActions
    })
    
    renderWorkspacesPage()
    
    expect(screen.getByText('Loading workspaces...')).toBeInTheDocument()
  })

  it('loads workspaces on mount', async () => {
    renderWorkspacesPage()
    
    await waitFor(() => {
      expect(mockWorkspaceService.getWorkspaces).toHaveBeenCalled()
      expect(mockStoreActions.setWorkspaces).toHaveBeenCalledWith(mockWorkspaces)
    })
  })

  it('shows empty state when no workspaces', () => {
    mockUseWorkspaceStore.mockReturnValue({
      workspaces: [],
      currentWorkspace: null,
      members: [],
      isLoading: false,
      ...mockStoreActions
    })
    
    renderWorkspacesPage()
    
    expect(screen.getByText('No workspaces found. Create your first workspace to get started.')).toBeInTheDocument()
  })

  it('displays workspace cards', () => {
    renderWorkspacesPage()
    
    expect(screen.getByText('Personal Workspace')).toBeInTheDocument()
    expect(screen.getByText('Team Workspace')).toBeInTheDocument()
    expect(screen.getByText('Current')).toBeInTheDocument() // Current workspace badge
  })

  it('opens create modal when create button is clicked', () => {
    renderWorkspacesPage()
    
    const createButton = screen.getByTestId('create-workspace-button')
    fireEvent.click(createButton)
    
    expect(screen.getByText('Create Workspace')).toBeInTheDocument()
  })

  it('opens create modal when URL has action=create', () => {
    mockSearchParams.set('action', 'create')
    
    renderWorkspacesPage()
    
    expect(screen.getByText('Create Workspace')).toBeInTheDocument()
    expect(mockSetSearchParams).toHaveBeenCalledWith({})
  })

  it('creates new workspace', async () => {
    const newWorkspace = {
      id: '3',
      name: 'New Workspace',
      slug: 'new-workspace',
      ownerId: 'user1',
      settings: {
        priorityWeights: {
          dueDateProximity: 0.3,
          effortEstimate: 0.2,
          businessImpact: 0.3,
          contextSwitching: 0.2
        },
        defaultSyncInterval: 15,
        enableTwoWaySync: true
      },
      createdAt: '2024-01-03T00:00:00Z',
      updatedAt: '2024-01-03T00:00:00Z'
    }
    
    mockWorkspaceService.createWorkspace.mockResolvedValue(newWorkspace)
    
    renderWorkspacesPage()
    
    const createButton = screen.getByTestId('create-workspace-button')
    fireEvent.click(createButton)
    
    const nameInput = screen.getByTestId('workspace-name-input')
    fireEvent.change(nameInput, { target: { value: 'New Workspace' } })
    
    const submitButton = screen.getByTestId('create-button')
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(mockWorkspaceService.createWorkspace).toHaveBeenCalledWith({
        name: 'New Workspace',
        slug: 'new-workspace'
      })
      expect(mockStoreActions.addWorkspace).toHaveBeenCalledWith(newWorkspace)
      expect(mockToast.success).toHaveBeenCalledWith('Workspace created successfully')
    })
  })

  it('opens settings modal when settings button is clicked', () => {
    renderWorkspacesPage()
    
    const settingsButton = screen.getByTestId('settings-button-1')
    fireEvent.click(settingsButton)
    
    expect(screen.getByText('Workspace Settings')).toBeInTheDocument()
  })

  it('opens members modal when members button is clicked', () => {
    renderWorkspacesPage()
    
    const membersButton = screen.getByTestId('members-button-1')
    fireEvent.click(membersButton)
    
    expect(screen.getByText('Manage Members - Personal Workspace')).toBeInTheDocument()
  })

  it('opens delete modal when delete button is clicked', () => {
    renderWorkspacesPage()
    
    const deleteButton = screen.getByTestId('delete-button-1')
    fireEvent.click(deleteButton)
    
    expect(screen.getByText('Delete Workspace')).toBeInTheDocument()
  })

  it('switches to workspace when switch button is clicked', () => {
    renderWorkspacesPage()
    
    const switchButton = screen.getByTestId('switch-button-2')
    fireEvent.click(switchButton)
    
    expect(mockStoreActions.switchWorkspace).toHaveBeenCalledWith('2')
  })

  it('does not show switch button for current workspace', () => {
    renderWorkspacesPage()
    
    expect(screen.queryByTestId('switch-button-1')).not.toBeInTheDocument()
    expect(screen.getByTestId('switch-button-2')).toBeInTheDocument()
  })

  it('updates workspace settings', async () => {
    const updatedWorkspace = { ...mockWorkspaces[0], name: 'Updated Workspace' }
    mockWorkspaceService.updateWorkspace.mockResolvedValue(updatedWorkspace)
    
    renderWorkspacesPage()
    
    const settingsButton = screen.getByTestId('settings-button-1')
    fireEvent.click(settingsButton)
    
    const nameInput = screen.getByTestId('workspace-name-input')
    fireEvent.change(nameInput, { target: { value: 'Updated Workspace' } })
    
    const saveButton = screen.getByTestId('save-button')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(mockWorkspaceService.updateWorkspace).toHaveBeenCalledWith('1', {
        name: 'Updated Workspace',
        settings: expect.any(Object)
      })
      expect(mockStoreActions.updateWorkspace).toHaveBeenCalledWith('1', updatedWorkspace)
      expect(mockToast.success).toHaveBeenCalledWith('Workspace updated successfully')
    })
  })

  it('deletes workspace', async () => {
    renderWorkspacesPage()
    
    const deleteButton = screen.getByTestId('delete-button-1')
    fireEvent.click(deleteButton)
    
    const confirmationInput = screen.getByTestId('confirmation-input')
    fireEvent.change(confirmationInput, { target: { value: 'Personal Workspace' } })
    
    const confirmButton = screen.getByTestId('delete-button')
    fireEvent.click(confirmButton)
    
    await waitFor(() => {
      expect(mockWorkspaceService.deleteWorkspace).toHaveBeenCalledWith('1')
      expect(mockStoreActions.removeWorkspace).toHaveBeenCalledWith('1')
      expect(mockToast.success).toHaveBeenCalledWith('Workspace deleted successfully')
    })
  })

  it('handles workspace loading error', async () => {
    mockWorkspaceService.getWorkspaces.mockRejectedValue(new Error('Failed to load'))
    
    renderWorkspacesPage()
    
    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith('Failed to load workspaces')
    })
  })

  it('handles workspace creation error', async () => {
    mockWorkspaceService.createWorkspace.mockRejectedValue(new Error('Failed to create'))
    
    renderWorkspacesPage()
    
    const createButton = screen.getByTestId('create-workspace-button')
    fireEvent.click(createButton)
    
    const nameInput = screen.getByTestId('workspace-name-input')
    fireEvent.change(nameInput, { target: { value: 'New Workspace' } })
    
    const submitButton = screen.getByTestId('create-button')
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith('Failed to create workspace')
    })
  })

  it('handles workspace update error', async () => {
    mockWorkspaceService.updateWorkspace.mockRejectedValue(new Error('Failed to update'))
    
    renderWorkspacesPage()
    
    const settingsButton = screen.getByTestId('settings-button-1')
    fireEvent.click(settingsButton)
    
    const saveButton = screen.getByTestId('save-button')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith('Failed to update workspace')
    })
  })

  it('handles workspace deletion error', async () => {
    mockWorkspaceService.deleteWorkspace.mockRejectedValue(new Error('Failed to delete'))
    
    renderWorkspacesPage()
    
    const deleteButton = screen.getByTestId('delete-button-1')
    fireEvent.click(deleteButton)
    
    const confirmationInput = screen.getByTestId('confirmation-input')
    fireEvent.change(confirmationInput, { target: { value: 'Personal Workspace' } })
    
    const confirmButton = screen.getByTestId('delete-button')
    fireEvent.click(confirmButton)
    
    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith('Failed to delete workspace')
    })
  })
})