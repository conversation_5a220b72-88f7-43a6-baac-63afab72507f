import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DatabaseService } from '../../database/database.service';
import { SyncEngineService } from './sync-engine.service';
import { SyncScheduleConfig } from '../interfaces/sync-job.interface';
import { DEFAULT_SYNC_INTERVALS, SyncJobPriority } from '../constants/sync.constants';
import { IntegrationStatus } from '../../integrations/types';

@Injectable()
export class SyncSchedulerService {
  private readonly logger = new Logger(SyncSchedulerService.name);
  private scheduleConfigs = new Map<string, SyncScheduleConfig>();

  constructor(
    private prisma: DatabaseService,
    private syncEngine: SyncEngineService,
  ) {}

  /**
   * Initialize scheduler with existing integration configurations
   */
  async onModuleInit(): Promise<void> {
    await this.loadScheduleConfigs();
    this.logger.log('Sync scheduler initialized');
  }

  /**
   * Load schedule configurations from database
   */
  private async loadScheduleConfigs(): Promise<void> {
    const integrations = await this.prisma.integration.findMany({
      where: {
        status: IntegrationStatus.ACTIVE,
      },
    });

    for (const integration of integrations) {
      const config = integration.config as any;
      const scheduleConfig: SyncScheduleConfig = {
        integrationId: integration.id,
        enabled: true,
        fullSyncInterval: config.fullSyncInterval || DEFAULT_SYNC_INTERVALS.FULL_SYNC,
        incrementalSyncInterval: config.incrementalSyncInterval || DEFAULT_SYNC_INTERVALS.INCREMENTAL_SYNC,
        timezone: config.timezone,
        activeHours: config.activeHours,
        excludeDays: config.excludeDays,
      };

      this.scheduleConfigs.set(integration.id, scheduleConfig);
    }

    this.logger.log(`Loaded ${this.scheduleConfigs.size} sync schedule configurations`);
  }

  /**
   * Run incremental sync every 15 minutes
   */
  @Cron('0 */15 * * * *')
  async runIncrementalSync(): Promise<void> {
    this.logger.log('Running scheduled incremental sync');

    for (const [integrationId, config] of this.scheduleConfigs) {
      if (!config.enabled || !this.shouldRunSync(config)) {
        continue;
      }

      try {
        const integration = await this.prisma.integration.findUnique({
          where: { id: integrationId },
        });

        if (!integration || integration.status !== IntegrationStatus.ACTIVE) {
          continue;
        }

        // Check if it's time for incremental sync
        const lastSync = integration.lastSyncAt;
        const now = new Date();
        const minutesSinceLastSync = lastSync 
          ? (now.getTime() - lastSync.getTime()) / (1000 * 60)
          : Infinity;

        if (minutesSinceLastSync >= config.incrementalSyncInterval) {
          await this.syncEngine.scheduleIncrementalSync(
            integrationId,
            integration.workspaceId,
            {
              priority: SyncJobPriority.NORMAL,
              lastSyncAt: lastSync,
            }
          );

          this.logger.log(`Scheduled incremental sync for integration ${integrationId}`);
        }
      } catch (error) {
        this.logger.error(`Failed to schedule incremental sync for integration ${integrationId}:`, error);
      }
    }
  }

  /**
   * Run full sync every hour
   */
  @Cron(CronExpression.EVERY_HOUR)
  async runFullSync(): Promise<void> {
    this.logger.log('Running scheduled full sync');

    for (const [integrationId, config] of this.scheduleConfigs) {
      if (!config.enabled || !this.shouldRunSync(config)) {
        continue;
      }

      try {
        const integration = await this.prisma.integration.findUnique({
          where: { id: integrationId },
        });

        if (!integration || integration.status !== IntegrationStatus.ACTIVE) {
          continue;
        }

        // Check if it's time for full sync
        const lastSync = integration.lastSyncAt;
        const now = new Date();
        const minutesSinceLastSync = lastSync 
          ? (now.getTime() - lastSync.getTime()) / (1000 * 60)
          : Infinity;

        if (minutesSinceLastSync >= config.fullSyncInterval) {
          await this.syncEngine.scheduleFullSync(
            integrationId,
            integration.workspaceId,
            {
              priority: SyncJobPriority.NORMAL,
              syncAllTasks: false,
            }
          );

          this.logger.log(`Scheduled full sync for integration ${integrationId}`);
        }
      } catch (error) {
        this.logger.error(`Failed to schedule full sync for integration ${integrationId}:`, error);
      }
    }
  }

  /**
   * Health check and cleanup every 5 minutes
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async runHealthCheck(): Promise<void> {
    try {
      // Check for stuck jobs
      await this.checkStuckJobs();

      // Update integration statuses
      await this.updateIntegrationStatuses();

      // Clean up old data periodically (every 6 hours)
      const now = new Date();
      if (now.getMinutes() % 360 === 0) { // Every 6 hours
        await this.syncEngine.cleanupOldData();
      }
    } catch (error) {
      this.logger.error('Health check failed:', error);
    }
  }

  /**
   * Check for stuck jobs and handle them
   */
  private async checkStuckJobs(): Promise<void> {
    const integrations = await this.prisma.integration.findMany({
      where: {
        status: IntegrationStatus.ACTIVE,
      },
    });

    for (const integration of integrations) {
      const activeJobs = await this.syncEngine.getActiveJobs(integration.id);
      const stuckJobs = activeJobs.filter(job => {
        const now = new Date();
        const jobAge = now.getTime() - job.createdAt.getTime();
        const maxJobAge = 30 * 60 * 1000; // 30 minutes

        return job.status === 'active' && jobAge > maxJobAge;
      });

      for (const stuckJob of stuckJobs) {
        this.logger.warn(`Found stuck job ${stuckJob.id} for integration ${integration.id}`);
        await this.syncEngine.cancelJob(stuckJob.id);
        
        // Reschedule if it was an important job
        if (stuckJob.jobType === 'full-sync' || stuckJob.jobType === 'incremental-sync') {
          await this.syncEngine.scheduleIncrementalSync(
            integration.id,
            integration.workspaceId,
            {
              priority: SyncJobPriority.HIGH,
              lastSyncAt: integration.lastSyncAt,
            }
          );
        }
      }
    }
  }

  /**
   * Update integration statuses based on sync results
   */
  private async updateIntegrationStatuses(): Promise<void> {
    const integrations = await this.prisma.integration.findMany();

    for (const integration of integrations) {
      const stats = await this.syncEngine.getSyncStats(integration.id, 1); // Last 24 hours
      
      // Mark integration as error if too many recent failures
      if (stats.totalJobs > 0 && stats.failedJobs / stats.totalJobs > 0.8) {
        if (integration.status !== IntegrationStatus.ERROR) {
          await this.prisma.integration.update({
            where: { id: integration.id },
            data: { status: IntegrationStatus.ERROR },
          });
          
          this.logger.warn(`Marked integration ${integration.id} as ERROR due to high failure rate`);
        }
      }
      // Reactivate if it was in error but now working
      else if (integration.status === IntegrationStatus.ERROR && stats.successfulJobs > 0) {
        await this.prisma.integration.update({
          where: { id: integration.id },
          data: { status: IntegrationStatus.ACTIVE },
        });
        
        this.logger.log(`Reactivated integration ${integration.id} after successful sync`);
      }
    }
  }

  /**
   * Check if sync should run based on schedule configuration
   */
  private shouldRunSync(config: SyncScheduleConfig): boolean {
    const now = new Date();

    // Check excluded days
    if (config.excludeDays && config.excludeDays.includes(now.getDay())) {
      return false;
    }

    // Check active hours
    if (config.activeHours) {
      const currentTime = now.toTimeString().slice(0, 5); // HH:mm format
      if (currentTime < config.activeHours.start || currentTime > config.activeHours.end) {
        return false;
      }
    }

    return true;
  }

  /**
   * Update schedule configuration for an integration
   */
  async updateScheduleConfig(
    integrationId: string,
    config: Partial<SyncScheduleConfig>
  ): Promise<void> {
    const existingConfig = this.scheduleConfigs.get(integrationId);
    const updatedConfig = { ...existingConfig, ...config } as SyncScheduleConfig;
    
    this.scheduleConfigs.set(integrationId, updatedConfig);

    // Update integration config in database
    const integration = await this.prisma.integration.findUnique({
      where: { id: integrationId },
    });

    if (integration) {
      const integrationConfig = integration.config as any;
      const newConfig = {
        ...integrationConfig,
        fullSyncInterval: updatedConfig.fullSyncInterval,
        incrementalSyncInterval: updatedConfig.incrementalSyncInterval,
        timezone: updatedConfig.timezone,
        activeHours: updatedConfig.activeHours,
        excludeDays: updatedConfig.excludeDays,
      };

      await this.prisma.integration.update({
        where: { id: integrationId },
        data: { config: newConfig },
      });
    }

    this.logger.log(`Updated schedule config for integration ${integrationId}`);
  }

  /**
   * Enable/disable sync for an integration
   */
  async toggleSync(integrationId: string, enabled: boolean): Promise<void> {
    const config = this.scheduleConfigs.get(integrationId);
    if (config) {
      config.enabled = enabled;
      this.scheduleConfigs.set(integrationId, config);
    }

    if (enabled) {
      await this.syncEngine.resumeIntegrationSync(integrationId);
    } else {
      await this.syncEngine.pauseIntegrationSync(integrationId);
    }

    this.logger.log(`${enabled ? 'Enabled' : 'Disabled'} sync for integration ${integrationId}`);
  }

  /**
   * Get schedule configuration for an integration
   */
  getScheduleConfig(integrationId: string): SyncScheduleConfig | undefined {
    return this.scheduleConfigs.get(integrationId);
  }

  /**
   * Add new integration to scheduler
   */
  async addIntegration(integrationId: string): Promise<void> {
    const integration = await this.prisma.integration.findUnique({
      where: { id: integrationId },
    });

    if (integration) {
      const config = integration.config as any;
      const scheduleConfig: SyncScheduleConfig = {
        integrationId,
        enabled: true,
        fullSyncInterval: config.fullSyncInterval || DEFAULT_SYNC_INTERVALS.FULL_SYNC,
        incrementalSyncInterval: config.incrementalSyncInterval || DEFAULT_SYNC_INTERVALS.INCREMENTAL_SYNC,
        timezone: config.timezone,
        activeHours: config.activeHours,
        excludeDays: config.excludeDays,
      };

      this.scheduleConfigs.set(integrationId, scheduleConfig);
      this.logger.log(`Added integration ${integrationId} to scheduler`);
    }
  }

  /**
   * Remove integration from scheduler
   */
  async removeIntegration(integrationId: string): Promise<void> {
    this.scheduleConfigs.delete(integrationId);
    await this.syncEngine.pauseIntegrationSync(integrationId);
    this.logger.log(`Removed integration ${integrationId} from scheduler`);
  }
}