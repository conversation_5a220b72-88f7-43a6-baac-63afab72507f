import { Injectable, Logger } from '@nestjs/common';
import { IntegrationAdapter } from '../interfaces/integration-adapter.interface';
import {
  IntegrationConfig,
  OAuthCredentials,
  ExternalTask,
  TaskUpdate,
  CreateTaskRequest,
  WebhookConfig,
  AuthResult,
  <PERSON>ync<PERSON>rror,
  IntegrationProvider,
} from '../types';

/**
 * Abstract base class for integration adapters
 * Provides common functionality and enforces interface implementation
 */
@Injectable()
export abstract class BaseIntegrationAdapter implements IntegrationAdapter {
  protected readonly logger = new Logger(this.constructor.name);
  protected readonly provider: IntegrationProvider;
  protected readonly baseUrl: string;
  protected readonly apiVersion?: string;

  constructor(provider: IntegrationProvider, baseUrl: string, apiVersion?: string) {
    this.provider = provider;
    this.baseUrl = baseUrl;
    this.apiVersion = apiVersion;
  }

  /**
   * Get the provider name for this adapter
   */
  getProvider(): string {
    return this.provider;
  }

  /**
   * Default implementation - most adapters support two-way sync
   */
  supportsTwoWaySync(): boolean {
    return true;
  }

  /**
   * Default implementation - webhooks are optional
   */
  supportsWebhooks(): boolean {
    return false;
  }

  /**
   * Abstract methods that must be implemented by concrete adapters
   */
  abstract authenticate(credentials: OAuthCredentials): Promise<AuthResult>;
  abstract fetchTasks(config: IntegrationConfig): Promise<ExternalTask[]>;
  abstract updateTask(taskId: string, updates: TaskUpdate): Promise<ExternalTask>;
  abstract createTask(task: CreateTaskRequest): Promise<ExternalTask>;
  abstract validateCredentials(credentials: any): Promise<boolean>;

  /**
   * Optional webhook setup - override if supported
   */
  async setupWebhook?(webhookUrl: string): Promise<WebhookConfig> {
    throw new Error(`Webhooks not supported for ${this.provider}`);
  }

  /**
   * Common error handling for API requests
   */
  protected handleApiError(error: any, operation: string): SyncError {
    this.logger.error(`${operation} failed for ${this.provider}:`, error);

    if (error.response?.status === 401 || error.response?.status === 403) {
      return {
        type: 'AUTH_ERROR',
        message: 'Authentication failed. Please reconnect your account.',
        retryable: false,
        details: { status: error.response?.status },
        timestamp: new Date(),
      };
    }

    if (error.response?.status === 429) {
      const retryAfter = error.response?.headers['retry-after'] 
        ? parseInt(error.response.headers['retry-after']) * 1000 
        : 60000; // Default 1 minute

      return {
        type: 'RATE_LIMIT_ERROR',
        message: 'Rate limit exceeded. Please try again later.',
        retryable: true,
        retryAfter,
        details: { status: error.response?.status },
        timestamp: new Date(),
      };
    }

    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return {
        type: 'NETWORK_ERROR',
        message: 'Network connection failed. Please check your internet connection.',
        retryable: true,
        retryAfter: 30000, // 30 seconds
        details: { code: error.code },
        timestamp: new Date(),
      };
    }

    if (error.response?.status >= 400 && error.response?.status < 500) {
      return {
        type: 'VALIDATION_ERROR',
        message: error.response?.data?.message || 'Invalid request data.',
        retryable: false,
        details: { 
          status: error.response?.status,
          data: error.response?.data 
        },
        timestamp: new Date(),
      };
    }

    return {
      type: 'API_ERROR',
      message: error.message || 'An unexpected error occurred.',
      retryable: true,
      retryAfter: 60000, // 1 minute
      details: { 
        status: error.response?.status,
        message: error.message 
      },
      timestamp: new Date(),
    };
  }

  /**
   * Common method to build API headers with authentication
   */
  protected buildHeaders(credentials: OAuthCredentials): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'TaskUnify/1.0',
    };

    if (credentials.accessToken) {
      headers.Authorization = `${credentials.tokenType || 'Bearer'} ${credentials.accessToken}`;
    }

    return headers;
  }

  /**
   * Common method to check if credentials are expired
   */
  protected isCredentialsExpired(credentials: OAuthCredentials): boolean {
    if (!credentials.expiresAt) {
      return false; // No expiration set
    }

    // Add 5 minute buffer before actual expiration
    const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds
    return new Date().getTime() > (credentials.expiresAt.getTime() - bufferTime);
  }

  /**
   * Common method to normalize task status from external systems
   */
  protected normalizeTaskStatus(externalStatus: string): string {
    const statusMap: Record<string, string> = {
      // Common variations
      'open': 'todo',
      'new': 'todo',
      'to do': 'todo',
      'todo': 'todo',
      'pending': 'todo',
      'backlog': 'todo',
      
      'in progress': 'in_progress',
      'in-progress': 'in_progress',
      'active': 'in_progress',
      'working': 'in_progress',
      'started': 'in_progress',
      
      'done': 'done',
      'completed': 'done',
      'finished': 'done',
      'resolved': 'done',
      'closed': 'done',
      
      'cancelled': 'cancelled',
      'canceled': 'cancelled',
      'rejected': 'cancelled',
      'abandoned': 'cancelled',
    };

    const normalized = statusMap[externalStatus.toLowerCase()];
    return normalized || 'todo'; // Default to todo if unknown
  }

  /**
   * Common method to normalize task priority from external systems
   */
  protected normalizeTaskPriority(externalPriority: string | number): string {
    if (typeof externalPriority === 'number') {
      if (externalPriority >= 4) return 'urgent';
      if (externalPriority >= 3) return 'high';
      if (externalPriority >= 2) return 'medium';
      return 'low';
    }

    const priorityMap: Record<string, string> = {
      'urgent': 'urgent',
      'critical': 'urgent',
      'highest': 'urgent',
      '4': 'urgent',
      
      'high': 'high',
      'important': 'high',
      '3': 'high',
      
      'medium': 'medium',
      'normal': 'medium',
      '2': 'medium',
      
      'low': 'low',
      'minor': 'low',
      'lowest': 'low',
      '1': 'low',
      '0': 'low',
    };

    const normalized = priorityMap[externalPriority.toString().toLowerCase()];
    return normalized || 'medium'; // Default to medium if unknown
  }

  /**
   * Common method to validate required task fields
   */
  protected validateTaskData(task: Partial<ExternalTask>): void {
    if (!task.id) {
      throw new Error('Task ID is required');
    }
    if (!task.title || task.title.trim().length === 0) {
      throw new Error('Task title is required');
    }
    if (!task.sourceUrl) {
      throw new Error('Task source URL is required');
    }
  }

  /**
   * Common method to sanitize task data
   */
  protected sanitizeTaskData(task: Partial<ExternalTask>): Partial<ExternalTask> {
    return {
      ...task,
      title: task.title?.trim(),
      description: task.description?.trim() || undefined,
      tags: task.tags?.filter(tag => tag && tag.trim().length > 0) || [],
      assigneeName: task.assigneeName?.trim() || undefined,
      projectName: task.projectName?.trim() || undefined,
    };
  }
}