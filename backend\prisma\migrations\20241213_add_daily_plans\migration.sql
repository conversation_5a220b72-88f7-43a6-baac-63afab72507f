-- CreateTable
CREATE TABLE "daily_plans" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "workspace_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "plan_date" DATE NOT NULL,
    "total_estimated_minutes" INTEGER NOT NULL DEFAULT 0,
    "total_completed_minutes" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "daily_plans_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "daily_plan_tasks" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "daily_plan_id" UUID NOT NULL,
    "task_id" UUID NOT NULL,
    "estimated_minutes" INTEGER NOT NULL DEFAULT 0,
    "actual_minutes" INTEGER,
    "order_index" INTEGER NOT NULL DEFAULT 0,
    "completed_at" TIMESTAMP(6),
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "daily_plan_tasks_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "daily_plans_workspace_id_user_id_plan_date_key" ON "daily_plans"("workspace_id", "user_id", "plan_date");

-- CreateIndex
CREATE UNIQUE INDEX "daily_plan_tasks_daily_plan_id_task_id_key" ON "daily_plan_tasks"("daily_plan_id", "task_id");

-- CreateIndex
CREATE INDEX "idx_daily_plans_workspace_user_date" ON "daily_plans"("workspace_id", "user_id", "plan_date");

-- CreateIndex
CREATE INDEX "idx_daily_plan_tasks_plan_order" ON "daily_plan_tasks"("daily_plan_id", "order_index");

-- AddForeignKey
ALTER TABLE "daily_plans" ADD CONSTRAINT "daily_plans_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "daily_plans" ADD CONSTRAINT "daily_plans_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "daily_plan_tasks" ADD CONSTRAINT "daily_plan_tasks_daily_plan_id_fkey" FOREIGN KEY ("daily_plan_id") REFERENCES "daily_plans"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "daily_plan_tasks" ADD CONSTRAINT "daily_plan_tasks_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE CASCADE;