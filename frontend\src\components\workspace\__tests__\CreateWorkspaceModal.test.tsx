import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { CreateWorkspaceModal } from '../CreateWorkspaceModal'

const mockOnClose = vi.fn()
const mockOnSubmit = vi.fn()

const renderModal = (isOpen = true) => {
  return render(
    <CreateWorkspaceModal
      isOpen={isOpen}
      onClose={mockOnClose}
      onSubmit={mockOnSubmit}
    />
  )
}

describe('CreateWorkspaceModal', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockOnSubmit.mockResolvedValue(undefined)
  })

  it('does not render when closed', () => {
    renderModal(false)
    expect(screen.queryByText('Create Workspace')).not.toBeInTheDocument()
  })

  it('renders when open', () => {
    renderModal()
    expect(screen.getByRole('heading', { name: 'Create Workspace' })).toBeInTheDocument()
    expect(screen.getByTestId('workspace-name-input')).toBeInTheDocument()
    expect(screen.getByTestId('workspace-slug-input')).toBeInTheDocument()
  })

  it('auto-generates slug from name', () => {
    renderModal()
    
    const nameInput = screen.getByTestId('workspace-name-input')
    const slugInput = screen.getByTestId('workspace-slug-input')
    
    fireEvent.change(nameInput, { target: { value: 'My Test Workspace' } })
    
    expect(slugInput).toHaveValue('my-test-workspace')
  })

  it('allows manual slug editing', () => {
    renderModal()
    
    const nameInput = screen.getByTestId('workspace-name-input')
    const slugInput = screen.getByTestId('workspace-slug-input')
    
    fireEvent.change(nameInput, { target: { value: 'My Workspace' } })
    fireEvent.change(slugInput, { target: { value: 'custom-slug' } })
    
    expect(slugInput).toHaveValue('custom-slug')
    
    // Changing name again should not override manual slug
    fireEvent.change(nameInput, { target: { value: 'My Updated Workspace' } })
    expect(slugInput).toHaveValue('custom-slug')
  })

  it('validates required name field', async () => {
    renderModal()
    
    const submitButton = screen.getByTestId('create-button')
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('name-error')).toHaveTextContent('Workspace name is required')
    })
    
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('validates minimum name length', async () => {
    renderModal()
    
    const nameInput = screen.getByTestId('workspace-name-input')
    fireEvent.change(nameInput, { target: { value: 'A' } })
    
    const submitButton = screen.getByTestId('create-button')
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('name-error')).toHaveTextContent('Workspace name must be at least 2 characters')
    })
    
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('validates minimum slug length when provided', async () => {
    renderModal()
    
    const nameInput = screen.getByTestId('workspace-name-input')
    const slugInput = screen.getByTestId('workspace-slug-input')
    
    fireEvent.change(nameInput, { target: { value: 'Valid Name' } })
    fireEvent.change(slugInput, { target: { value: 'a' } })
    
    const submitButton = screen.getByTestId('create-button')
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('slug-error')).toHaveTextContent('Slug must be at least 2 characters')
    })
    
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('cleans slug input to remove invalid characters', () => {
    renderModal()
    
    const slugInput = screen.getByTestId('workspace-slug-input')
    fireEvent.change(slugInput, { target: { value: 'Test Slug!' } })
    
    expect(slugInput).toHaveValue('test-slug')
  })

  it('submits form with valid data', async () => {
    renderModal()
    
    const nameInput = screen.getByTestId('workspace-name-input')
    const slugInput = screen.getByTestId('workspace-slug-input')
    
    fireEvent.change(nameInput, { target: { value: 'My Workspace' } })
    fireEvent.change(slugInput, { target: { value: 'my-workspace' } })
    
    const submitButton = screen.getByTestId('create-button')
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: 'My Workspace',
        slug: 'my-workspace'
      })
    })
  })

  it('submits form without slug when empty', async () => {
    renderModal()
    
    const nameInput = screen.getByTestId('workspace-name-input')
    const slugInput = screen.getByTestId('workspace-slug-input')
    
    fireEvent.change(nameInput, { target: { value: 'My Workspace' } })
    fireEvent.change(slugInput, { target: { value: '' } })
    
    const submitButton = screen.getByTestId('create-button')
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: 'My Workspace',
        slug: undefined
      })
    })
  })

  it('shows loading state during submission', async () => {
    mockOnSubmit.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))
    
    renderModal()
    
    const nameInput = screen.getByTestId('workspace-name-input')
    fireEvent.change(nameInput, { target: { value: 'My Workspace' } })
    
    const submitButton = screen.getByTestId('create-button')
    fireEvent.click(submitButton)
    
    expect(submitButton).toHaveTextContent('Creating...')
    expect(submitButton).toBeDisabled()
    
    await waitFor(() => {
      expect(submitButton).toHaveTextContent('Create Workspace')
    })
  })

  it('resets form after successful submission', async () => {
    renderModal()
    
    const nameInput = screen.getByTestId('workspace-name-input')
    const slugInput = screen.getByTestId('workspace-slug-input')
    
    fireEvent.change(nameInput, { target: { value: 'My Workspace' } })
    fireEvent.change(slugInput, { target: { value: 'my-workspace' } })
    
    const submitButton = screen.getByTestId('create-button')
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(nameInput).toHaveValue('')
      expect(slugInput).toHaveValue('')
    })
  })

  it('closes modal when cancel button is clicked', () => {
    renderModal()
    
    const cancelButton = screen.getByTestId('cancel-button')
    fireEvent.click(cancelButton)
    
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('closes modal when close button is clicked', () => {
    renderModal()
    
    const closeButton = screen.getByTestId('close-modal-button')
    fireEvent.click(closeButton)
    
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('closes modal when clicking backdrop', () => {
    renderModal()
    
    const backdrop = document.querySelector('.fixed.inset-0.bg-black')
    fireEvent.click(backdrop!)
    
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('prevents closing during submission', () => {
    mockOnSubmit.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))
    
    renderModal()
    
    const nameInput = screen.getByTestId('workspace-name-input')
    fireEvent.change(nameInput, { target: { value: 'My Workspace' } })
    
    const submitButton = screen.getByTestId('create-button')
    fireEvent.click(submitButton)
    
    const closeButton = screen.getByTestId('close-modal-button')
    fireEvent.click(closeButton)
    
    expect(mockOnClose).not.toHaveBeenCalled()
  })

  it('clears errors when user starts typing', async () => {
    renderModal()
    
    // Trigger validation error
    const submitButton = screen.getByTestId('create-button')
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('name-error')).toBeInTheDocument()
    })
    
    // Start typing to clear error
    const nameInput = screen.getByTestId('workspace-name-input')
    fireEvent.change(nameInput, { target: { value: 'A' } })
    
    expect(screen.queryByTestId('name-error')).not.toBeInTheDocument()
  })
})