# TaskUnify Backend - Quick Start

Get your TaskUnify backend running with Neon database in minutes!

## 🚀 One-Command Setup

```bash
cd backend
npm run neon:setup
```

This interactive script will:
- ✅ Guide you through Neon database setup
- ✅ Configure your environment variables
- ✅ Install dependencies
- ✅ Run database migrations
- ✅ Seed with test data
- ✅ Verify everything works

## 📋 Manual Setup (Alternative)

If you prefer manual setup:

### 1. Get Neon Database
1. Visit [Neon Console](https://console.neon.tech)
2. Create account and new project
3. Copy your connection string

### 2. Configure Environment
```bash
cp .env.example .env
# Edit .env and add your Neon connection string
```

### 3. Install & Setup
```bash
npm install
npm run prisma:generate
npm run prisma:migrate
npm run prisma:seed
```

### 4. Verify Setup
```bash
npm run db:verify
```

## 🏃‍♂️ Start Development

```bash
npm run dev
```

Your API will be available at:
- **API**: http://localhost:3001/api
- **API Docs**: http://localhost:3001/api (Swagger UI)
- **Health Check**: http://localhost:3001/api/health
- **Database Health**: http://localhost:3001/api/health/database

## 🔍 Explore Your Data

```bash
npm run prisma:studio
```

Opens Prisma Studio to view and edit your database data.

## 📊 What's Included

After setup, you'll have:

### Test Users
- `<EMAIL>` - Personal workspace owner
- `<EMAIL>` - Agency workspace owner  
- `<EMAIL>` - Agency team member

### Sample Data
- 2 workspaces with different settings
- 3 integrations (Asana, Trello, Google Sheets)
- 5 sample tasks with various priorities
- Sync logs showing integration history

### API Endpoints
- User management and authentication
- Workspace and team management
- Integration configuration
- Task aggregation and prioritization
- Sync status and logging

## 🛠️ Development Commands

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server |
| `npm run build` | Build for production |
| `npm run test` | Run tests |
| `npm run lint` | Lint code |
| `npm run prisma:studio` | Open database GUI |
| `npm run prisma:migrate` | Run new migrations |
| `npm run db:reset` | Reset database |
| `npm run db:verify` | Check database health |

## 🔧 Troubleshooting

### Database Connection Issues
```bash
npm run db:verify
```

### Reset Everything
```bash
npm run db:reset
npm run prisma:seed
```

### Update Schema
1. Edit `prisma/schema.prisma`
2. Run `npm run prisma:migrate`

## 📚 Next Steps

1. **Frontend**: Set up the React frontend
2. **Integrations**: Configure API keys for external services
3. **Authentication**: Set up OAuth providers
4. **Deployment**: Deploy to your preferred platform

## 🆘 Need Help?

- **Database Issues**: Check `NEON_SETUP.md`
- **API Documentation**: Visit `/api` endpoint when server is running
- **Schema Reference**: See `prisma/schema.prisma`

Happy coding! 🎉