import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsArray, ValidateNested, IsString, IsNumber, Min } from 'class-validator';
import { Type, Transform } from 'class-transformer';

export class AgeRangeDto {
  @ApiPropertyOptional({
    description: 'Label for the age range',
    example: '0-7 days',
  })
  @IsString()
  label: string;

  @ApiPropertyOptional({
    description: 'Minimum number of days for this range',
    example: 0,
  })
  @IsNumber()
  @Min(0)
  minDays: number;

  @ApiPropertyOptional({
    description: 'Maximum number of days for this range (null for open-ended)',
    example: 7,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxDays: number | null;
}

export class TaskAgingQueryDto {
  @ApiPropertyOptional({
    description: 'Custom age ranges for grouping tasks',
    type: [AgeRangeDto],
    example: [
      { label: '0-7 days', minDays: 0, maxDays: 7 },
      { label: '8-30 days', minDays: 8, maxDays: 30 },
      { label: '30+ days', minDays: 31, maxDays: null },
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AgeRangeDto)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return undefined;
      }
    }
    return value;
  })
  ageRanges?: AgeRangeDto[];
}