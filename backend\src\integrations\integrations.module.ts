import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from '../database/database.module';

// Services
import { IntegrationService } from './services/integration.service';
import { IntegrationAdapterRegistry } from './services/integration-adapter.registry';
import { CredentialEncryptionService } from './services/credential-encryption.service';
import { IntegrationStatusService } from './services/integration-status.service';

// Adapters
import { GoogleSheetsAdapter } from './adapters/google-sheets.adapter';
import { AsanaAdapter } from './adapters/asana.adapter';

// Types
import { IntegrationProvider } from './types';

// Controllers (to be added later)
// import { IntegrationsController } from './integrations.controller';

/**
 * Integration module that provides all integration-related functionality
 * including adapters, credential management, and sync operations
 */
@Module({
  imports: [
    ConfigModule,
    DatabaseModule,
  ],
  providers: [
    IntegrationService,
    IntegrationAdapterRegistry,
    CredentialEncryptionService,
    IntegrationStatusService,
    GoogleSheetsAdapter,
    AsanaAdapter,
  ],
  exports: [
    IntegrationService,
    IntegrationAdapterRegistry,
    CredentialEncryptionService,
    IntegrationStatusService,
  ],
})
export class IntegrationsModule {
  constructor(
    private readonly registry: IntegrationAdapterRegistry,
    private readonly googleSheetsAdapter: GoogleSheetsAdapter,
    private readonly asanaAdapter: AsanaAdapter,
  ) {
    this.registerAdapters();
  }

  private registerAdapters(): void {
    // Register all available adapters
    this.registry.register(IntegrationProvider.GOOGLE_SHEETS, this.googleSheetsAdapter);
    this.registry.register(IntegrationProvider.ASANA, this.asanaAdapter);
  }
}