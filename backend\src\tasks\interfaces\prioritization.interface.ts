export interface PriorityWeights {
  dueDateProximity: number;    // 0.0 - 1.0
  effortEstimate: number;      // 0.0 - 1.0
  businessImpact: number;      // 0.0 - 1.0
  contextSwitching: number;    // 0.0 - 1.0
}

export interface TaskPriorityData {
  id: string;
  dueDate?: Date;
  estimatedMinutes?: number;
  priority?: string;
  tags: string[];
  projectName?: string;
  assigneeId?: string;
  status: string;
}

export interface PrioritizationSettings {
  weights: PriorityWeights;
  maxScore: number;
  dueDateUrgencyDays: number;
  effortThresholds: {
    quick: number;    // minutes
    medium: number;   // minutes
    large: number;    // minutes
  };
  businessImpactTags: string[];
  contextSwitchingPenalty: number;
}

export const DEFAULT_PRIORITIZATION_SETTINGS: PrioritizationSettings = {
  weights: {
    dueDateProximity: 0.4,
    effortEstimate: 0.2,
    businessImpact: 0.3,
    contextSwitching: 0.1,
  },
  maxScore: 100,
  dueDateUrgencyDays: 7,
  effortThresholds: {
    quick: 30,
    medium: 120,
    large: 480,
  },
  businessImpactTags: ['urgent', 'critical', 'high-priority', 'client-facing'],
  contextSwitchingPenalty: 0.1,
};