import { PrismaClient } from '@prisma/client';
import * as crypto from 'crypto';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Clean existing data
  await prisma.syncLog.deleteMany();
  await prisma.task.deleteMany();
  await prisma.integration.deleteMany();
  await prisma.workspaceMember.deleteMany();
  await prisma.workspace.deleteMany();
  await prisma.user.deleteMany();

  // Create test users
  const user1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: '<PERSON>',
      avatarUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=john',
    },
  });

  const user2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: '<PERSON>',
      avatarUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=jane',
    },
  });

  const user3 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: '<PERSON>',
      avatarUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=mike',
    },
  });

  // Create test workspaces
  const personalWorkspace = await prisma.workspace.create({
    data: {
      name: 'Personal Tasks',
      slug: 'personal-tasks',
      ownerId: user1.id,
      settings: {
        priorityWeights: {
          dueDateProximity: 0.4,
          effortEstimate: 0.2,
          businessImpact: 0.3,
          contextSwitching: 0.1,
        },
        defaultSyncInterval: 15,
        enableTwoWaySync: true,
      },
    },
  });

  const agencyWorkspace = await prisma.workspace.create({
    data: {
      name: 'Digital Agency',
      slug: 'digital-agency',
      ownerId: user2.id,
      settings: {
        priorityWeights: {
          dueDateProximity: 0.3,
          effortEstimate: 0.2,
          businessImpact: 0.4,
          contextSwitching: 0.1,
        },
        defaultSyncInterval: 10,
        enableTwoWaySync: true,
      },
    },
  });

  // Create workspace memberships
  await prisma.workspaceMember.create({
    data: {
      userId: user1.id,
      workspaceId: personalWorkspace.id,
      role: 'OWNER',
      permissions: ['manage_workspace', 'manage_integrations', 'manage_members'],
    },
  });

  await prisma.workspaceMember.create({
    data: {
      userId: user2.id,
      workspaceId: agencyWorkspace.id,
      role: 'OWNER',
      permissions: ['manage_workspace', 'manage_integrations', 'manage_members'],
    },
  });

  await prisma.workspaceMember.create({
    data: {
      userId: user1.id,
      workspaceId: agencyWorkspace.id,
      role: 'ADMIN',
      permissions: ['manage_integrations', 'manage_tasks'],
    },
  });

  await prisma.workspaceMember.create({
    data: {
      userId: user3.id,
      workspaceId: agencyWorkspace.id,
      role: 'MEMBER',
      permissions: ['view_tasks', 'edit_tasks'],
    },
  });

  // Create test integrations
  const asanaIntegration = await prisma.integration.create({
    data: {
      workspaceId: personalWorkspace.id,
      provider: 'asana',
      name: 'Personal Asana',
      config: {
        syncInterval: 15,
        enableTwoWaySync: true,
        fieldMappings: [
          { source: 'name', target: 'title' },
          { source: 'notes', target: 'description' },
          { source: 'completed', target: 'status' },
          { source: 'due_on', target: 'dueDate' },
        ],
        filters: [
          { field: 'assignee', operator: 'equals', value: 'me' },
        ],
      },
      encryptedCredentials: crypto.randomBytes(32).toString('hex'), // Mock encrypted credentials
      status: 'ACTIVE',
      lastSyncAt: new Date(),
    },
  });

  const trelloIntegration = await prisma.integration.create({
    data: {
      workspaceId: agencyWorkspace.id,
      provider: 'trello',
      name: 'Agency Trello Board',
      config: {
        syncInterval: 10,
        enableTwoWaySync: true,
        fieldMappings: [
          { source: 'name', target: 'title' },
          { source: 'desc', target: 'description' },
          { source: 'closed', target: 'status' },
          { source: 'due', target: 'dueDate' },
        ],
        filters: [
          { field: 'board', operator: 'equals', value: 'client-projects' },
        ],
      },
      encryptedCredentials: crypto.randomBytes(32).toString('hex'),
      status: 'ACTIVE',
      lastSyncAt: new Date(),
    },
  });

  const googleSheetsIntegration = await prisma.integration.create({
    data: {
      workspaceId: personalWorkspace.id,
      provider: 'google_sheets',
      name: 'Personal Task Sheet',
      config: {
        syncInterval: 30,
        enableTwoWaySync: true,
        sheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
        range: 'Tasks!A:F',
        fieldMappings: [
          { column: 'A', target: 'title' },
          { column: 'B', target: 'description' },
          { column: 'C', target: 'status' },
          { column: 'D', target: 'priority' },
          { column: 'E', target: 'dueDate' },
          { column: 'F', target: 'estimatedMinutes' },
        ],
      },
      encryptedCredentials: crypto.randomBytes(32).toString('hex'),
      status: 'ACTIVE',
      lastSyncAt: new Date(),
    },
  });

  // Create test tasks
  const tasks = [
    {
      workspaceId: personalWorkspace.id,
      integrationId: asanaIntegration.id,
      externalId: 'asana_task_1',
      title: 'Complete project proposal',
      description: 'Finish the Q1 project proposal for the new client',
      status: 'in_progress',
      priority: 'high',
      priorityScore: 85.5,
      assigneeId: 'user_123',
      assigneeName: 'John Doe',
      dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
      estimatedMinutes: 120,
      tags: ['proposal', 'client', 'urgent'],
      projectName: 'Q1 Client Onboarding',
      sourceUrl: 'https://app.asana.com/0/123456789/987654321',
      metadata: {
        asanaGid: '123456789',
        parentTask: null,
        subtasks: [],
      },
    },
    {
      workspaceId: personalWorkspace.id,
      integrationId: googleSheetsIntegration.id,
      externalId: 'sheets_row_2',
      title: 'Review marketing materials',
      description: 'Review and approve new marketing brochures',
      status: 'todo',
      priority: 'medium',
      priorityScore: 65.0,
      assigneeId: 'user_123',
      assigneeName: 'John Doe',
      dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
      estimatedMinutes: 60,
      tags: ['marketing', 'review'],
      projectName: 'Marketing Campaign',
      sourceUrl: 'https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
      metadata: {
        sheetRow: 2,
        lastModified: new Date().toISOString(),
      },
    },
    {
      workspaceId: agencyWorkspace.id,
      integrationId: trelloIntegration.id,
      externalId: 'trello_card_abc123',
      title: 'Design homepage mockup',
      description: 'Create initial homepage design mockup for client review',
      status: 'todo',
      priority: 'high',
      priorityScore: 92.0,
      assigneeId: 'user_456',
      assigneeName: 'Jane Smith',
      dueDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1 day from now
      estimatedMinutes: 240,
      tags: ['design', 'homepage', 'client'],
      projectName: 'Website Redesign',
      sourceUrl: 'https://trello.com/c/abc123/design-homepage-mockup',
      metadata: {
        trelloId: 'abc123',
        boardId: 'board_456',
        listId: 'list_789',
      },
    },
    {
      workspaceId: agencyWorkspace.id,
      integrationId: trelloIntegration.id,
      externalId: 'trello_card_def456',
      title: 'Set up development environment',
      description: 'Configure local development environment for new project',
      status: 'done',
      priority: 'medium',
      priorityScore: 45.0,
      assigneeId: 'user_789',
      assigneeName: 'Mike Wilson',
      dueDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      estimatedMinutes: 180,
      tags: ['development', 'setup'],
      projectName: 'Website Redesign',
      sourceUrl: 'https://trello.com/c/def456/set-up-development-environment',
      metadata: {
        trelloId: 'def456',
        boardId: 'board_456',
        listId: 'list_completed',
      },
    },
    {
      workspaceId: personalWorkspace.id,
      integrationId: asanaIntegration.id,
      externalId: 'asana_task_2',
      title: 'Update portfolio website',
      description: 'Add recent projects to portfolio and update bio',
      status: 'todo',
      priority: 'low',
      priorityScore: 25.0,
      assigneeId: 'user_123',
      assigneeName: 'John Doe',
      dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 2 weeks from now
      estimatedMinutes: 90,
      tags: ['portfolio', 'personal'],
      projectName: 'Personal Branding',
      sourceUrl: 'https://app.asana.com/0/123456789/987654322',
      metadata: {
        asanaGid: '987654322',
        parentTask: null,
        subtasks: [],
      },
    },
  ];

  for (const taskData of tasks) {
    await prisma.task.create({ data: taskData });
  }

  // Create test sync logs
  const syncLogs = [
    {
      integrationId: asanaIntegration.id,
      operation: 'FULL_SYNC',
      status: 'SUCCESS',
      tasksProcessed: 15,
      errors: [],
      startedAt: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
      completedAt: new Date(Date.now() - 58 * 60 * 1000), // 58 minutes ago
    },
    {
      integrationId: trelloIntegration.id,
      operation: 'INCREMENTAL_SYNC',
      status: 'SUCCESS',
      tasksProcessed: 8,
      errors: [],
      startedAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      completedAt: new Date(Date.now() - 29 * 60 * 1000), // 29 minutes ago
    },
    {
      integrationId: googleSheetsIntegration.id,
      operation: 'FULL_SYNC',
      status: 'PARTIAL_SUCCESS',
      tasksProcessed: 12,
      errors: [
        {
          row: 5,
          error: 'Invalid date format in due date column',
          severity: 'WARNING',
        },
      ],
      startedAt: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
      completedAt: new Date(Date.now() - 14 * 60 * 1000), // 14 minutes ago
    },
  ];

  for (const logData of syncLogs) {
    await prisma.syncLog.create({ data: logData });
  }

  console.log('✅ Database seeded successfully!');
  console.log(`Created:`);
  console.log(`  - ${await prisma.user.count()} users`);
  console.log(`  - ${await prisma.workspace.count()} workspaces`);
  console.log(`  - ${await prisma.workspaceMember.count()} workspace memberships`);
  console.log(`  - ${await prisma.integration.count()} integrations`);
  console.log(`  - ${await prisma.task.count()} tasks`);
  console.log(`  - ${await prisma.syncLog.count()} sync logs`);
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });