import { Badge } from '@/components/ui/badge'
import { TaskStatus } from '@/types/task'
import { cn } from '@/lib/utils'

interface TaskStatusBadgeProps {
  status: TaskStatus
  className?: string
}

const statusConfig = {
  [TaskStatus.TODO]: {
    label: 'To Do',
    variant: 'secondary' as const,
    className: 'bg-gray-100 text-gray-800 hover:bg-gray-200'
  },
  [TaskStatus.IN_PROGRESS]: {
    label: 'In Progress',
    variant: 'default' as const,
    className: 'bg-blue-100 text-blue-800 hover:bg-blue-200'
  },
  [TaskStatus.DONE]: {
    label: 'Done',
    variant: 'default' as const,
    className: 'bg-green-100 text-green-800 hover:bg-green-200'
  },
  [TaskStatus.CANCELLED]: {
    label: 'Cancelled',
    variant: 'destructive' as const,
    className: 'bg-red-100 text-red-800 hover:bg-red-200'
  }
}

export function TaskStatusBadge({ status, className }: TaskStatusBadgeProps) {
  const config = statusConfig[status]
  
  return (
    <Badge 
      variant={config.variant}
      className={cn(config.className, className)}
    >
      {config.label}
    </Badge>
  )
}