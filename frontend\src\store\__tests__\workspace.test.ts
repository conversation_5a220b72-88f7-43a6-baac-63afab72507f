import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useWorkspaceStore } from '../workspace'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

describe('Workspace Store', () => {
  const mockWorkspace = {
    id: '1',
    name: 'Test Workspace',
    slug: 'test-workspace',
    ownerId: 'user-1',
    settings: {
      priorityWeights: {
        dueDateProximity: 0.3,
        effortEstimate: 0.2,
        businessImpact: 0.3,
        contextSwitching: 0.2,
      },
      defaultSyncInterval: 15,
      enableTwoWaySync: true,
    },
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  }

  const mockMember = {
    id: '1',
    userId: 'user-1',
    workspaceId: '1',
    role: 'OWNER' as const,
    permissions: ['read', 'write', 'admin'],
    joinedAt: '2023-01-01T00:00:00Z',
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Reset store state
    useWorkspaceStore.setState({
      workspaces: [],
      currentWorkspace: null,
      members: [],
      isLoading: false,
    })
  })

  it('initializes with default state', () => {
    const state = useWorkspaceStore.getState()
    
    expect(state.workspaces).toEqual([])
    expect(state.currentWorkspace).toBeNull()
    expect(state.members).toEqual([])
    expect(state.isLoading).toBe(false)
  })

  it('sets workspaces correctly', () => {
    const workspaces = [mockWorkspace]

    useWorkspaceStore.getState().setWorkspaces(workspaces)
    const state = useWorkspaceStore.getState()

    expect(state.workspaces).toEqual(workspaces)
    expect(state.currentWorkspace).toEqual(mockWorkspace) // Should set first workspace as current
  })

  it('sets current workspace correctly', () => {
    useWorkspaceStore.getState().setCurrentWorkspace(mockWorkspace)
    const state = useWorkspaceStore.getState()

    expect(state.currentWorkspace).toEqual(mockWorkspace)
  })

  it('adds workspace correctly', () => {
    useWorkspaceStore.getState().addWorkspace(mockWorkspace)
    const state = useWorkspaceStore.getState()

    expect(state.workspaces).toContain(mockWorkspace)
  })

  it('updates workspace correctly', () => {
    useWorkspaceStore.getState().setWorkspaces([mockWorkspace])
    useWorkspaceStore.getState().setCurrentWorkspace(mockWorkspace)

    const updates = { name: 'Updated Workspace' }
    useWorkspaceStore.getState().updateWorkspace('1', updates)
    const state = useWorkspaceStore.getState()

    expect(state.workspaces[0].name).toBe('Updated Workspace')
    expect(state.currentWorkspace?.name).toBe('Updated Workspace')
  })

  it('removes workspace correctly', () => {
    const workspace2 = { ...mockWorkspace, id: '2', name: 'Workspace 2' }
    useWorkspaceStore.getState().setWorkspaces([mockWorkspace, workspace2])
    useWorkspaceStore.getState().setCurrentWorkspace(mockWorkspace)

    useWorkspaceStore.getState().removeWorkspace('1')
    const state = useWorkspaceStore.getState()

    expect(state.workspaces).not.toContain(mockWorkspace)
    expect(state.currentWorkspace).toEqual(workspace2) // Should switch to remaining workspace
  })

  it('switches workspace correctly', () => {
    const workspace2 = { ...mockWorkspace, id: '2', name: 'Workspace 2' }
    useWorkspaceStore.getState().setWorkspaces([mockWorkspace, workspace2])

    useWorkspaceStore.getState().switchWorkspace('2')
    const state = useWorkspaceStore.getState()

    expect(state.currentWorkspace).toEqual(workspace2)
  })

  it('manages members correctly', () => {
    useWorkspaceStore.getState().setMembers([mockMember])
    let state = useWorkspaceStore.getState()
    expect(state.members).toContain(mockMember)

    const newMember = { ...mockMember, id: '2', userId: 'user-2' }
    useWorkspaceStore.getState().addMember(newMember)
    state = useWorkspaceStore.getState()
    expect(state.members).toContain(newMember)

    useWorkspaceStore.getState().updateMember('2', { role: 'ADMIN' })
    state = useWorkspaceStore.getState()
    expect(state.members.find(m => m.id === '2')?.role).toBe('ADMIN')

    useWorkspaceStore.getState().removeMember('2')
    state = useWorkspaceStore.getState()
    expect(state.members.find(m => m.id === '2')).toBeUndefined()
  })

  it('sets loading state correctly', () => {
    useWorkspaceStore.getState().setLoading(true)
    expect(useWorkspaceStore.getState().isLoading).toBe(true)

    useWorkspaceStore.getState().setLoading(false)
    expect(useWorkspaceStore.getState().isLoading).toBe(false)
  })
})