import { describe, it, expect, vi, beforeEach } from 'vitest'
import { IntegrationService } from '../integration'
import { apiClient } from '../api'
import { IntegrationProvider, IntegrationStatus } from '@/types/api'

// Mock the API service
vi.mock('../api', () => ({
  apiService: {
    get: vi.fn(),
    post: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn()
  }
}))

const mockApiService = vi.mocked(await import('../api')).apiService as any

describe('IntegrationService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getWorkspaceIntegrations', () => {
    it('should fetch workspace integrations', async () => {
      const mockIntegrations = [
        {
          id: 'integration-1',
          workspaceId: 'workspace-1',
          provider: IntegrationProvider.ASANA,
          name: 'My Asana Integration',
          config: {
            syncInterval: 30,
            enableTwoWaySync: true,
            fieldMappings: [],
            filters: []
          },
          status: IntegrationStatus.ACTIVE,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      ]

      mockApiService.get.mockResolvedValue({
        data: { data: mockIntegrations }
      })

      const result = await IntegrationService.getWorkspaceIntegrations('workspace-1')

      expect(mockApiService.get).toHaveBeenCalledWith('/workspaces/workspace-1/integrations')
      expect(result).toEqual(mockIntegrations)
    })
  })

  describe('createIntegration', () => {
    it('should create a new integration', async () => {
      const mockIntegration = {
        id: 'integration-1',
        workspaceId: 'workspace-1',
        provider: IntegrationProvider.ASANA,
        name: 'My Asana Integration',
        config: {
          syncInterval: 30,
          enableTwoWaySync: true,
          fieldMappings: [],
          filters: []
        },
        status: IntegrationStatus.ACTIVE,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }

      const createRequest = {
        provider: IntegrationProvider.ASANA,
        name: 'My Asana Integration',
        config: {
          syncInterval: 30,
          enableTwoWaySync: true,
          fieldMappings: [],
          filters: []
        }
      }

      mockApiService.post.mockResolvedValue({
        data: { data: mockIntegration }
      })

      const result = await IntegrationService.createIntegration('workspace-1', createRequest)

      expect(mockApiService.post).toHaveBeenCalledWith(
        '/workspaces/workspace-1/integrations',
        createRequest
      )
      expect(result).toEqual(mockIntegration)
    })
  })

  describe('updateIntegration', () => {
    it('should update an integration', async () => {
      const mockIntegration = {
        id: 'integration-1',
        workspaceId: 'workspace-1',
        provider: IntegrationProvider.ASANA,
        name: 'Updated Asana Integration',
        config: {
          syncInterval: 60,
          enableTwoWaySync: false,
          fieldMappings: [],
          filters: []
        },
        status: IntegrationStatus.ACTIVE,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T01:00:00Z'
      }

      const updateRequest = {
        name: 'Updated Asana Integration',
        config: {
          syncInterval: 60,
          enableTwoWaySync: false
        }
      }

      mockApiService.patch.mockResolvedValue({
        data: { data: mockIntegration }
      })

      const result = await IntegrationService.updateIntegration(
        'workspace-1',
        'integration-1',
        updateRequest
      )

      expect(mockApiService.patch).toHaveBeenCalledWith(
        '/workspaces/workspace-1/integrations/integration-1',
        updateRequest
      )
      expect(result).toEqual(mockIntegration)
    })
  })

  describe('deleteIntegration', () => {
    it('should delete an integration', async () => {
      mockApiService.delete.mockResolvedValue({})

      await IntegrationService.deleteIntegration('workspace-1', 'integration-1')

      expect(mockApiService.delete).toHaveBeenCalledWith(
        '/workspaces/workspace-1/integrations/integration-1'
      )
    })
  })

  describe('syncIntegration', () => {
    it('should trigger manual sync', async () => {
      const mockSyncResult = {
        success: true,
        tasksProcessed: 10,
        tasksCreated: 5,
        tasksUpdated: 3,
        tasksDeleted: 2,
        errors: [],
        conflicts: [],
        duration: 1500
      }

      mockApiService.post.mockResolvedValue({
        data: { data: mockSyncResult }
      })

      const result = await IntegrationService.syncIntegration('workspace-1', 'integration-1')

      expect(mockApiService.post).toHaveBeenCalledWith(
        '/workspaces/workspace-1/integrations/integration-1/sync'
      )
      expect(result).toEqual(mockSyncResult)
    })
  })

  describe('getOAuthUrl', () => {
    it('should get OAuth authorization URL', async () => {
      const mockOAuthResponse = {
        authUrl: 'https://app.asana.com/oauth/authorize?client_id=123&redirect_uri=http://localhost:3000/callback',
        state: 'random-state-string'
      }

      mockApiService.post.mockResolvedValue({
        data: { data: mockOAuthResponse }
      })

      const result = await IntegrationService.getOAuthUrl(
        'workspace-1',
        IntegrationProvider.ASANA,
        'http://localhost:3000/callback'
      )

      expect(mockApiService.post).toHaveBeenCalledWith(
        '/workspaces/workspace-1/integrations/oauth/authorize',
        {
          provider: IntegrationProvider.ASANA,
          redirectUri: 'http://localhost:3000/callback'
        }
      )
      expect(result).toEqual(mockOAuthResponse)
    })
  })

  describe('completeOAuth', () => {
    it('should complete OAuth flow and create integration', async () => {
      const mockIntegration = {
        id: 'integration-1',
        workspaceId: 'workspace-1',
        provider: IntegrationProvider.ASANA,
        name: 'My Asana Integration',
        config: {
          syncInterval: 30,
          enableTwoWaySync: true,
          fieldMappings: [],
          filters: []
        },
        status: IntegrationStatus.ACTIVE,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }

      mockApiService.post.mockResolvedValue({
        data: { data: mockIntegration }
      })

      const result = await IntegrationService.completeOAuth(
        'workspace-1',
        IntegrationProvider.ASANA,
        'oauth-code',
        'oauth-state',
        'My Asana Integration',
        { syncInterval: 30, enableTwoWaySync: true }
      )

      expect(mockApiService.post).toHaveBeenCalledWith(
        '/workspaces/workspace-1/integrations/oauth/callback',
        {
          provider: IntegrationProvider.ASANA,
          code: 'oauth-code',
          state: 'oauth-state',
          name: 'My Asana Integration',
          config: { syncInterval: 30, enableTwoWaySync: true }
        }
      )
      expect(result).toEqual(mockIntegration)
    })
  })

  describe('getAvailableProviders', () => {
    it('should fetch available integration providers', async () => {
      const mockProviders = [
        {
          provider: IntegrationProvider.ASANA,
          name: 'Asana',
          description: 'Connect your Asana projects and tasks',
          logoUrl: '/logos/asana.png',
          capabilities: {
            supportsTwoWaySync: true,
            supportsWebhooks: true,
            supportsFieldMapping: true
          }
        },
        {
          provider: IntegrationProvider.TRELLO,
          name: 'Trello',
          description: 'Connect your Trello boards and cards',
          logoUrl: '/logos/trello.png',
          capabilities: {
            supportsTwoWaySync: true,
            supportsWebhooks: false,
            supportsFieldMapping: false
          }
        }
      ]

      mockApiService.get.mockResolvedValue({
        data: { data: mockProviders }
      })

      const result = await IntegrationService.getAvailableProviders()

      expect(mockApiService.get).toHaveBeenCalledWith('/integrations/providers')
      expect(result).toEqual(mockProviders)
    })
  })

  describe('testConnection', () => {
    it('should test integration connection', async () => {
      const mockTestResult = {
        success: true,
        message: 'Connection successful',
        details: { userId: '123', userName: 'John Doe' }
      }

      mockApiService.post.mockResolvedValue({
        data: { data: mockTestResult }
      })

      const result = await IntegrationService.testConnection('workspace-1', 'integration-1')

      expect(mockApiService.post).toHaveBeenCalledWith(
        '/workspaces/workspace-1/integrations/integration-1/test'
      )
      expect(result).toEqual(mockTestResult)
    })

    it('should handle connection test failure', async () => {
      const mockTestResult = {
        success: false,
        message: 'Invalid credentials',
        details: { error: 'Unauthorized' }
      }

      mockApiService.post.mockResolvedValue({
        data: { data: mockTestResult }
      })

      const result = await IntegrationService.testConnection('workspace-1', 'integration-1')

      expect(result).toEqual(mockTestResult)
    })
  })
})