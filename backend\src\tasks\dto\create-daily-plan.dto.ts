import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsArray, ValidateNested, IsUUID, IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class DailyPlanTaskDto {
  @ApiProperty({ description: 'Task ID to add to daily plan' })
  @IsUUID()
  taskId: string;

  @ApiProperty({ description: 'Estimated duration in minutes', minimum: 0 })
  @IsInt()
  @Min(0)
  estimatedMinutes: number;

  @ApiProperty({ description: 'Order index for drag-and-drop ordering', minimum: 0 })
  @IsInt()
  @Min(0)
  orderIndex: number;
}

export class CreateDailyPlanDto {
  @ApiProperty({ description: 'Date for the daily plan (YYYY-MM-DD)' })
  @IsDateString()
  planDate: string;

  @ApiProperty({ 
    description: 'Tasks to include in the daily plan',
    type: [DailyPlanTaskDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DailyPlanTaskDto)
  tasks: DailyPlanTaskDto[];
}