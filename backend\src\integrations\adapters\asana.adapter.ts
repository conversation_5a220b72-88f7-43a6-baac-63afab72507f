import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { BaseIntegrationAdapter } from './base-integration.adapter';
import {
  IntegrationProvider,
  IntegrationConfig,
  OAuthCredentials,
  ExternalTask,
  TaskUpdate,
  CreateTaskRequest,
  AuthResult,
  TaskStatus,
  TaskPriority,
  WebhookConfig,
} from '../types';

/**
 * Asana API response interfaces
 */
interface AsanaUser {
  gid: string;
  name: string;
  email: string;
  photo?: {
    image_128x128?: string;
  };
}

interface AsanaTask {
  gid: string;
  name: string;
  notes?: string;
  completed: boolean;
  completed_at?: string;
  due_date?: string;
  due_time?: string;
  assignee?: {
    gid: string;
    name: string;
  };
  projects?: Array<{
    gid: string;
    name: string;
  }>;
  tags?: Array<{
    gid: string;
    name: string;
  }>;
  permalink_url: string;
  created_at: string;
  modified_at: string;
  custom_fields?: Array<{
    gid: string;
    name: string;
    type: string;
    enum_value?: {
      gid: string;
      name: string;
    };
    number_value?: number;
    text_value?: string;
  }>;
}

interface AsanaProject {
  gid: string;
  name: string;
  archived: boolean;
}

interface AsanaWorkspace {
  gid: string;
  name: string;
}

interface AsanaApiResponse<T> {
  data: T;
}

interface AsanaListResponse<T> {
  data: T[];
  next_page?: {
    offset: string;
    path: string;
    uri: string;
  };
}

/**
 * Asana integration adapter
 * Provides task synchronization with Asana projects and workspaces
 */
@Injectable()
export class AsanaAdapter extends BaseIntegrationAdapter {
  private readonly apiClient: AxiosInstance;

  constructor() {
    super(IntegrationProvider.ASANA, 'https://app.asana.com/api/1.0');
    
    this.apiClient = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TaskUnify/1.0',
      },
    });

    // Add response interceptor for error handling
    this.apiClient.interceptors.response.use(
      (response) => response,
      (error) => {
        this.logger.error('Asana API error:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          url: error.config?.url,
        });
        throw error;
      }
    );
  }

  /**
   * Authenticate with Asana using OAuth credentials
   */
  async authenticate(credentials: OAuthCredentials): Promise<AuthResult> {
    try {
      // Set up authentication headers
      this.apiClient.defaults.headers.common['Authorization'] = `Bearer ${credentials.accessToken}`;

      // Test authentication by getting current user info
      const response: AxiosResponse<AsanaApiResponse<AsanaUser>> = await this.apiClient.get('/users/me');
      const user = response.data.data;

      return {
        success: true,
        user: {
          id: user.gid,
          email: user.email,
          name: user.name,
          avatar: user.photo?.image_128x128,
        },
        credentials,
      };
    } catch (error) {
      this.logger.error('Asana authentication failed:', error);
      const syncError = this.handleApiError(error, 'Authentication');
      
      return {
        success: false,
        error: syncError.message,
      };
    }
  }

  /**
   * Fetch tasks from Asana with project and workspace filtering
   */
  async fetchTasks(config: IntegrationConfig): Promise<ExternalTask[]> {
    try {
      // Set up authentication
      const credentials = config.customSettings?.credentials as OAuthCredentials;
      if (!credentials?.accessToken) {
        throw new Error('Access token is required');
      }

      this.apiClient.defaults.headers.common['Authorization'] = `Bearer ${credentials.accessToken}`;

      const tasks: ExternalTask[] = [];
      const workspaceIds = config.customSettings?.workspaceIds as string[] || [];
      const projectIds = config.customSettings?.projectIds as string[] || [];

      // If no specific workspaces/projects are configured, fetch from all accessible workspaces
      if (workspaceIds.length === 0 && projectIds.length === 0) {
        const workspaces = await this.getWorkspaces();
        for (const workspace of workspaces) {
          const workspaceTasks = await this.fetchTasksFromWorkspace(workspace.gid, config);
          tasks.push(...workspaceTasks);
        }
      } else {
        // Fetch from specific projects if configured
        if (projectIds.length > 0) {
          for (const projectId of projectIds) {
            const projectTasks = await this.fetchTasksFromProject(projectId, config);
            tasks.push(...projectTasks);
          }
        }

        // Fetch from specific workspaces if configured
        if (workspaceIds.length > 0) {
          for (const workspaceId of workspaceIds) {
            const workspaceTasks = await this.fetchTasksFromWorkspace(workspaceId, config);
            tasks.push(...workspaceTasks);
          }
        }
      }

      // Apply filters
      return this.applyFilters(tasks, config.filters);
    } catch (error) {
      this.logger.error('Failed to fetch Asana tasks:', error);
      throw this.handleApiError(error, 'Fetch tasks');
    }
  }

  /**
   * Update a task in Asana
   */
  async updateTask(taskId: string, updates: TaskUpdate): Promise<ExternalTask> {
    try {
      const updateData: any = {};

      // Map updates to Asana API format
      if (updates.title !== undefined) {
        updateData.name = updates.title;
      }

      if (updates.description !== undefined) {
        updateData.notes = updates.description;
      }

      if (updates.status !== undefined) {
        updateData.completed = updates.status === TaskStatus.DONE;
      }

      if (updates.dueDate !== undefined) {
        if (updates.dueDate) {
          updateData.due_date = updates.dueDate.toISOString().split('T')[0]; // YYYY-MM-DD format
        } else {
          updateData.due_date = null;
        }
      }

      if (updates.assigneeId !== undefined) {
        updateData.assignee = updates.assigneeId || null;
      }

      // Update the task
      const response: AxiosResponse<AsanaApiResponse<AsanaTask>> = await this.apiClient.put(
        `/tasks/${taskId}`,
        { data: updateData }
      );

      return this.mapAsanaTaskToExternal(response.data.data);
    } catch (error) {
      this.logger.error(`Failed to update Asana task ${taskId}:`, error);
      throw this.handleApiError(error, 'Update task');
    }
  }

  /**
   * Create a new task in Asana
   */
  async createTask(task: CreateTaskRequest): Promise<ExternalTask> {
    try {
      const createData: any = {
        name: task.title,
        notes: task.description || '',
      };

      // Set due date if provided
      if (task.dueDate) {
        createData.due_date = task.dueDate.toISOString().split('T')[0];
      }

      // Set assignee if provided
      if (task.assigneeId) {
        createData.assignee = task.assigneeId;
      }

      // Set project if provided
      if (task.projectId) {
        createData.projects = [task.projectId];
      }

      // Create the task
      const response: AxiosResponse<AsanaApiResponse<AsanaTask>> = await this.apiClient.post(
        '/tasks',
        { data: createData }
      );

      return this.mapAsanaTaskToExternal(response.data.data);
    } catch (error) {
      this.logger.error('Failed to create Asana task:', error);
      throw this.handleApiError(error, 'Create task');
    }
  }

  /**
   * Validate that the provided credentials are still valid
   */
  async validateCredentials(credentials: OAuthCredentials): Promise<boolean> {
    try {
      this.apiClient.defaults.headers.common['Authorization'] = `Bearer ${credentials.accessToken}`;
      await this.apiClient.get('/users/me');
      return true;
    } catch (error) {
      this.logger.warn('Asana credentials validation failed:', error);
      return false;
    }
  }

  /**
   * Set up webhook for real-time updates
   */
  async setupWebhook(webhookUrl: string): Promise<WebhookConfig> {
    try {
      // Get the first workspace to create webhook for
      const workspaces = await this.getWorkspaces();
      if (workspaces.length === 0) {
        throw new Error('No workspaces available for webhook setup');
      }

      const webhookData = {
        resource: workspaces[0].gid,
        target: webhookUrl,
        filters: [
          { resource_type: 'task', action: 'added' },
          { resource_type: 'task', action: 'changed' },
          { resource_type: 'task', action: 'deleted' },
        ],
      };

      const response = await this.apiClient.post('/webhooks', { data: webhookData });
      const webhook = response.data.data;

      return {
        id: webhook.gid,
        url: webhookUrl,
        events: ['task.added', 'task.changed', 'task.deleted'],
        active: webhook.active,
      };
    } catch (error) {
      this.logger.error('Failed to setup Asana webhook:', error);
      throw this.handleApiError(error, 'Setup webhook');
    }
  }

  /**
   * Check if the adapter supports webhooks
   */
  supportsWebhooks(): boolean {
    return true;
  }

  /**
   * Get available workspaces for the authenticated user
   */
  private async getWorkspaces(): Promise<AsanaWorkspace[]> {
    const response: AxiosResponse<AsanaApiResponse<AsanaWorkspace[]>> = await this.apiClient.get('/workspaces');
    return response.data.data;
  }

  /**
   * Get projects in a workspace
   */
  private async getProjectsInWorkspace(workspaceId: string): Promise<AsanaProject[]> {
    const response: AxiosResponse<AsanaApiResponse<AsanaProject[]>> = await this.apiClient.get(
      `/workspaces/${workspaceId}/projects?archived=false`
    );
    return response.data.data;
  }

  /**
   * Fetch tasks from a specific workspace
   */
  private async fetchTasksFromWorkspace(workspaceId: string, config: IntegrationConfig): Promise<ExternalTask[]> {
    const tasks: ExternalTask[] = [];
    
    // Get all projects in the workspace
    const projects = await this.getProjectsInWorkspace(workspaceId);
    
    // Fetch tasks from each project
    for (const project of projects) {
      const projectTasks = await this.fetchTasksFromProject(project.gid, config);
      tasks.push(...projectTasks);
    }

    return tasks;
  }

  /**
   * Fetch tasks from a specific project
   */
  private async fetchTasksFromProject(projectId: string, config: IntegrationConfig): Promise<ExternalTask[]> {
    const tasks: ExternalTask[] = [];
    let offset: string | undefined;

    do {
      const params: any = {
        opt_fields: [
          'gid',
          'name',
          'notes',
          'completed',
          'completed_at',
          'due_date',
          'due_time',
          'assignee.gid',
          'assignee.name',
          'projects.gid',
          'projects.name',
          'tags.gid',
          'tags.name',
          'permalink_url',
          'created_at',
          'modified_at',
          'custom_fields.gid',
          'custom_fields.name',
          'custom_fields.type',
          'custom_fields.enum_value.name',
          'custom_fields.number_value',
          'custom_fields.text_value',
        ].join(','),
        limit: 100,
      };

      if (offset) {
        params.offset = offset;
      }

      const response: AxiosResponse<AsanaListResponse<AsanaTask>> = await this.apiClient.get(
        `/projects/${projectId}/tasks`,
        { params }
      );

      const asanaTasks = response.data.data;
      for (const asanaTask of asanaTasks) {
        try {
          const externalTask = this.mapAsanaTaskToExternal(asanaTask);
          tasks.push(externalTask);
        } catch (error) {
          this.logger.warn(`Failed to map Asana task ${asanaTask.gid}:`, error);
        }
      }

      offset = response.data.next_page?.offset;
    } while (offset);

    return tasks;
  }

  /**
   * Map Asana task to external task format
   */
  private mapAsanaTaskToExternal(asanaTask: AsanaTask): ExternalTask {
    // Determine task status
    let status = TaskStatus.TODO;
    if (asanaTask.completed) {
      status = TaskStatus.DONE;
    }

    // Extract priority from custom fields or tags
    let priority = TaskPriority.MEDIUM;
    const priorityField = asanaTask.custom_fields?.find(field => 
      field.name.toLowerCase().includes('priority')
    );
    if (priorityField?.enum_value?.name) {
      priority = this.normalizeTaskPriority(priorityField.enum_value.name) as TaskPriority;
    }

    // Parse due date
    let dueDate: Date | undefined;
    if (asanaTask.due_date) {
      dueDate = new Date(asanaTask.due_date);
      if (asanaTask.due_time) {
        const timeMatch = asanaTask.due_time.match(/(\d{2}):(\d{2})/);
        if (timeMatch) {
          dueDate.setHours(parseInt(timeMatch[1]), parseInt(timeMatch[2]));
        }
      }
    }

    // Extract tags
    const tags = asanaTask.tags?.map(tag => tag.name) || [];

    // Get project name
    const projectName = asanaTask.projects?.[0]?.name;

    // Extract estimated minutes from custom fields
    let estimatedMinutes: number | undefined;
    const estimateField = asanaTask.custom_fields?.find(field => 
      field.name.toLowerCase().includes('estimate') || 
      field.name.toLowerCase().includes('time') ||
      field.name.toLowerCase().includes('hours')
    );
    if (estimateField?.number_value) {
      // Assume the field is in hours, convert to minutes
      estimatedMinutes = Math.round(estimateField.number_value * 60);
    }

    const externalTask: ExternalTask = {
      id: asanaTask.gid,
      title: asanaTask.name,
      description: asanaTask.notes || undefined,
      status,
      priority,
      assigneeId: asanaTask.assignee?.gid,
      assigneeName: asanaTask.assignee?.name,
      dueDate,
      estimatedMinutes,
      tags,
      projectName,
      sourceUrl: asanaTask.permalink_url,
      metadata: {
        asanaProjectIds: asanaTask.projects?.map(p => p.gid) || [],
        customFields: asanaTask.custom_fields || [],
        completedAt: asanaTask.completed_at,
      },
      createdAt: new Date(asanaTask.created_at),
      updatedAt: new Date(asanaTask.modified_at),
    };

    this.validateTaskData(externalTask);
    return this.sanitizeTaskData(externalTask) as ExternalTask;
  }

  /**
   * Apply filters to tasks based on configuration
   */
  private applyFilters(tasks: ExternalTask[], filters: any[]): ExternalTask[] {
    if (!filters || filters.length === 0) {
      return tasks;
    }

    return tasks.filter(task => {
      return filters.every(filter => {
        const fieldValue = (task as any)[filter.field];
        
        switch (filter.operator) {
          case 'equals':
            return fieldValue === filter.value;
          case 'contains':
            return fieldValue && fieldValue.toString().toLowerCase().includes(filter.value.toLowerCase());
          case 'startsWith':
            return fieldValue && fieldValue.toString().toLowerCase().startsWith(filter.value.toLowerCase());
          case 'endsWith':
            return fieldValue && fieldValue.toString().toLowerCase().endsWith(filter.value.toLowerCase());
          case 'in':
            return Array.isArray(filter.value) && filter.value.includes(fieldValue);
          case 'notIn':
            return Array.isArray(filter.value) && !filter.value.includes(fieldValue);
          default:
            return true;
        }
      });
    });
  }
}