import { Test, TestingModule } from '@nestjs/testing';
import { GoogleSheetsAdapter } from '../google-sheets.adapter';
import { IntegrationProvider } from '../../types';

describe('GoogleSheetsAdapter Integration', () => {
  let adapter: GoogleSheetsAdapter;
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [GoogleSheetsAdapter],
    }).compile();

    adapter = module.get<GoogleSheetsAdapter>(GoogleSheetsAdapter);
  });

  afterEach(async () => {
    await module.close();
  });

  describe('basic functionality', () => {
    it('should be defined', () => {
      expect(adapter).toBeDefined();
    });

    it('should have correct provider', () => {
      expect(adapter.getProvider()).toBe(IntegrationProvider.GOOGLE_SHEETS);
    });

    it('should support two-way sync', () => {
      expect(adapter.supportsTwoWaySync()).toBe(true);
    });

    it('should not support webhooks', () => {
      expect(adapter.supportsWebhooks()).toBe(false);
    });
  });

  describe('credential validation', () => {
    it('should handle invalid credentials gracefully', async () => {
      const invalidCredentials = {
        accessToken: 'invalid-token',
        refreshToken: 'invalid-refresh',
      };

      const result = await adapter.validateCredentials(invalidCredentials);
      expect(result).toBe(false);
    });
  });

  describe('error handling', () => {
    it('should throw error for missing spreadsheet ID', async () => {
      const config = {
        syncInterval: 15,
        enableTwoWaySync: true,
        fieldMappings: [],
        filters: [],
        customSettings: {}, // Missing spreadsheetId
      };

      await expect(adapter.fetchTasks(config)).rejects.toThrow(
        'Spreadsheet ID is required in configuration'
      );
    });
  });
});