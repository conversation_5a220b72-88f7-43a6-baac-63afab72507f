import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import { render } from '@/test/test-utils'
import { OAuthCallbackPage } from '../OAuthCallbackPage'
import { useAuthStore } from '@/store/auth'

// Mock the auth store
vi.mock('@/store/auth', () => ({
  useAuthStore: vi.fn(),
}))

// Mock react-router-dom
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useParams: () => ({ provider: 'google' }),
    useSearchParams: () => [new URLSearchParams('code=test_code&state=test_state')],
    useNavigate: () => mockNavigate,
  }
})

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

describe('OAuthCallbackPage', () => {
  const mockLogin = vi.fn()
  const mockSetLoading = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useAuthStore as any).mockReturnValue({
      login: mockLogin,
      setLoading: mockSetLoading,
    })
  })

  it('renders loading state correctly', () => {
    render(<OAuthCallbackPage />)
    
    expect(screen.getByText('Completing sign in...')).toBeInTheDocument()
    expect(screen.getByText(/please wait while we complete your google authentication/i)).toBeInTheDocument()
  })

  it('processes OAuth callback successfully', async () => {
    render(<OAuthCallbackPage />)
    
    await waitFor(() => {
      expect(mockSetLoading).toHaveBeenCalledWith(true)
    })

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith(
        expect.objectContaining({
          email: '<EMAIL>',
          name: 'John Doe',
        }),
        'mock-jwt-token'
      )
    })

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard')
    })
  })
})