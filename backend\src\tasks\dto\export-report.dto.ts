import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString } from 'class-validator';

export enum ExportFormat {
  CSV = 'csv',
  PDF = 'pdf',
}

export enum ReportType {
  COMPLETION = 'completion',
  AGING = 'aging',
  VELOCITY = 'velocity',
}

export class ExportReportDto {
  @ApiProperty({
    description: 'Export format',
    enum: ExportFormat,
    example: ExportFormat.CSV,
  })
  @IsEnum(ExportFormat)
  format: ExportFormat;

  @ApiProperty({
    description: 'Type of report to export',
    enum: ReportType,
    example: ReportType.COMPLETION,
  })
  @IsEnum(ReportType)
  reportType: ReportType;

  @ApiProperty({
    description: 'Serialized filters as JSON string',
    example: '{"startDate":"2024-01-01","endDate":"2024-12-31"}',
  })
  @IsString()
  filters: string;
}