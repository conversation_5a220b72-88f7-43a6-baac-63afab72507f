import { Badge } from '@/components/ui/badge'
import { TaskPriority } from '@/types/task'
import { cn } from '@/lib/utils'
import { AlertTriangle, ArrowUp, ArrowDown, Minus } from 'lucide-react'

interface TaskPriorityBadgeProps {
  priority?: TaskPriority
  priorityScore?: number
  className?: string
  showIcon?: boolean
}

const priorityConfig = {
  [TaskPriority.LOW]: {
    label: 'Low',
    variant: 'secondary' as const,
    className: 'bg-gray-100 text-gray-600 hover:bg-gray-200',
    icon: ArrowDown
  },
  [TaskPriority.MEDIUM]: {
    label: 'Medium',
    variant: 'default' as const,
    className: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
    icon: Minus
  },
  [TaskPriority.HIGH]: {
    label: 'High',
    variant: 'default' as const,
    className: 'bg-orange-100 text-orange-800 hover:bg-orange-200',
    icon: ArrowUp
  },
  [TaskPriority.URGENT]: {
    label: 'Urgent',
    variant: 'destructive' as const,
    className: 'bg-red-100 text-red-800 hover:bg-red-200',
    icon: AlertTriangle
  }
}

export function TaskPriorityBadge({ 
  priority = TaskPriority.MEDIUM, 
  priorityScore,
  className,
  showIcon = false 
}: TaskPriorityBadgeProps) {
  const config = priorityConfig[priority]
  const Icon = config.icon
  
  return (
    <Badge 
      variant={config.variant}
      className={cn(config.className, 'flex items-center gap-1', className)}
      title={priorityScore ? `Priority Score: ${priorityScore}` : undefined}
    >
      {showIcon && <Icon className="h-3 w-3" />}
      {config.label}
      {priorityScore && (
        <span className="ml-1 text-xs opacity-75">
          ({Math.round(priorityScore)})
        </span>
      )}
    </Badge>
  )
}