import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { DatabaseService } from '../../database/database.service';
import { IntegrationService } from '../../integrations/services/integration.service';
import { 
  SyncJobData, 
  SyncJobResult, 
  SyncJobStatus,
  FullSyncJobData,
  IncrementalSyncJobData,
  WebhookSyncJobData,
  ConflictResolutionJobData,
  RetryFailedJobData
} from '../interfaces/sync-job.interface';
import { 
  SYNC_QUEUE, 
  SyncJobType, 
  SyncJobPriority,
  ConflictResolutionStrategy,
  SyncOperationType
} from '../constants/sync.constants';
import { SyncError, SyncConflict, IntegrationStatus } from '../../integrations/types';

@Injectable()
export class SyncEngineService {
  private readonly logger = new Logger(SyncEngineService.name);

  constructor(
    @InjectQueue(SYNC_QUEUE) private syncQueue: Queue,
    private prisma: DatabaseService,
    private integrationsService: IntegrationService,
  ) {}

  /**
   * Schedule a full sync for an integration
   */
  async scheduleFullSync(
    integrationId: string,
    workspaceId: string,
    options: {
      priority?: SyncJobPriority;
      forceSync?: boolean;
      syncAllTasks?: boolean;
      delay?: number;
    } = {}
  ): Promise<string> {
    const jobData: FullSyncJobData = {
      integrationId,
      workspaceId,
      jobType: SyncJobType.FULL_SYNC,
      priority: options.priority || SyncJobPriority.NORMAL,
      forceSync: options.forceSync || false,
      syncAllTasks: options.syncAllTasks || false,
    };

    const job = await this.syncQueue.add(
      SyncJobType.FULL_SYNC,
      jobData,
      {
        priority: jobData.priority,
        delay: options.delay || 0,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      }
    );

    this.logger.log(`Scheduled full sync job ${job.id} for integration ${integrationId}`);
    return job.id.toString();
  }

  /**
   * Schedule an incremental sync for an integration
   */
  async scheduleIncrementalSync(
    integrationId: string,
    workspaceId: string,
    options: {
      priority?: SyncJobPriority;
      lastSyncAt?: Date;
      modifiedSince?: Date;
      delay?: number;
    } = {}
  ): Promise<string> {
    const jobData: IncrementalSyncJobData = {
      integrationId,
      workspaceId,
      jobType: SyncJobType.INCREMENTAL_SYNC,
      priority: options.priority || SyncJobPriority.NORMAL,
      lastSyncAt: options.lastSyncAt,
      modifiedSince: options.modifiedSince,
    };

    const job = await this.syncQueue.add(
      SyncJobType.INCREMENTAL_SYNC,
      jobData,
      {
        priority: jobData.priority,
        delay: options.delay || 0,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      }
    );

    this.logger.log(`Scheduled incremental sync job ${job.id} for integration ${integrationId}`);
    return job.id.toString();
  }

  /**
   * Schedule a webhook sync for an integration
   */
  async scheduleWebhookSync(
    integrationId: string,
    workspaceId: string,
    webhookPayload: any,
    eventType: string,
    externalTaskId?: string
  ): Promise<string> {
    const jobData: WebhookSyncJobData = {
      integrationId,
      workspaceId,
      jobType: SyncJobType.WEBHOOK_SYNC,
      priority: SyncJobPriority.HIGH,
      webhookPayload,
      eventType,
      externalTaskId,
    };

    const job = await this.syncQueue.add(
      SyncJobType.WEBHOOK_SYNC,
      jobData,
      {
        priority: jobData.priority,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
      }
    );

    this.logger.log(`Scheduled webhook sync job ${job.id} for integration ${integrationId}`);
    return job.id.toString();
  }

  /**
   * Schedule conflict resolution for specific conflicts
   */
  async scheduleConflictResolution(
    integrationId: string,
    workspaceId: string,
    conflicts: SyncConflict[],
    strategy: ConflictResolutionStrategy,
    resolvedBy?: string
  ): Promise<string> {
    const jobData: ConflictResolutionJobData = {
      integrationId,
      workspaceId,
      jobType: SyncJobType.CONFLICT_RESOLUTION,
      priority: SyncJobPriority.HIGH,
      conflicts,
      strategy,
      resolvedBy,
    };

    const job = await this.syncQueue.add(
      SyncJobType.CONFLICT_RESOLUTION,
      jobData,
      {
        priority: jobData.priority,
        attempts: 2,
        backoff: {
          type: 'fixed',
          delay: 5000,
        },
      }
    );

    this.logger.log(`Scheduled conflict resolution job ${job.id} for integration ${integrationId}`);
    return job.id.toString();
  }

  /**
   * Retry a failed sync job
   */
  async retryFailedJob(
    originalJobId: string,
    integrationId: string,
    workspaceId: string,
    originalError: SyncError,
    maxRetries: number = 3
  ): Promise<string> {
    const jobData: RetryFailedJobData = {
      integrationId,
      workspaceId,
      jobType: SyncJobType.RETRY_FAILED,
      priority: SyncJobPriority.NORMAL,
      originalJobId,
      originalError,
      maxRetries,
    };

    const job = await this.syncQueue.add(
      SyncJobType.RETRY_FAILED,
      jobData,
      {
        priority: jobData.priority,
        attempts: maxRetries,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
      }
    );

    this.logger.log(`Scheduled retry job ${job.id} for failed job ${originalJobId}`);
    return job.id.toString();
  }

  /**
   * Get sync job status
   */
  async getJobStatus(jobId: string): Promise<SyncJobStatus | null> {
    const job = await this.syncQueue.getJob(jobId);
    if (!job) {
      return null;
    }

    const status: SyncJobStatus = {
      id: job.id.toString(),
      integrationId: job.data.integrationId,
      jobType: job.data.jobType,
      status: await job.getState() as 'waiting' | 'active' | 'completed' | 'failed' | 'delayed' | 'paused',
      progress: job.progress(),
      createdAt: new Date(job.timestamp),
      processedAt: job.processedOn ? new Date(job.processedOn) : undefined,
      completedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
      failedAt: job.failedReason ? new Date(job.failedReason) : undefined,
      error: job.failedReason,
      result: job.returnvalue,
    };

    return status;
  }

  /**
   * Get active sync jobs for an integration
   */
  async getActiveJobs(integrationId: string): Promise<SyncJobStatus[]> {
    const jobs = await this.syncQueue.getJobs(['waiting', 'active', 'delayed']);
    const integrationJobs = jobs.filter(job => job.data.integrationId === integrationId);

    const statuses: SyncJobStatus[] = [];
    for (const job of integrationJobs) {
      const status = await this.getJobStatus(job.id.toString());
      if (status) {
        statuses.push(status);
      }
    }

    return statuses;
  }

  /**
   * Cancel a sync job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    try {
      const job = await this.syncQueue.getJob(jobId);
      if (!job) {
        return false;
      }

      await job.remove();
      this.logger.log(`Cancelled sync job ${jobId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to cancel job ${jobId}:`, error);
      return false;
    }
  }

  /**
   * Pause sync jobs for an integration
   */
  async pauseIntegrationSync(integrationId: string): Promise<void> {
    const jobs = await this.getActiveJobs(integrationId);
    
    for (const jobStatus of jobs) {
      if (jobStatus.status === 'waiting' || jobStatus.status === 'delayed') {
        const job = await this.syncQueue.getJob(jobStatus.id);
        if (job) {
          await job.remove();
          this.logger.log(`Removed pending job ${jobStatus.id} for paused integration ${integrationId}`);
        }
      }
    }

    // Update integration status
    await this.prisma.integration.update({
      where: { id: integrationId },
      data: { status: IntegrationStatus.DISABLED },
    });

    this.logger.log(`Paused sync for integration ${integrationId}`);
  }

  /**
   * Resume sync jobs for an integration
   */
  async resumeIntegrationSync(integrationId: string): Promise<void> {
    // Update integration status
    await this.prisma.integration.update({
      where: { id: integrationId },
      data: { status: IntegrationStatus.ACTIVE },
    });

    // Schedule an incremental sync to catch up
    const integration = await this.prisma.integration.findUnique({
      where: { id: integrationId },
    });

    if (integration) {
      await this.scheduleIncrementalSync(
        integrationId,
        integration.workspaceId,
        {
          priority: SyncJobPriority.HIGH,
          lastSyncAt: integration.lastSyncAt,
        }
      );
    }

    this.logger.log(`Resumed sync for integration ${integrationId}`);
  }

  /**
   * Get sync statistics for an integration
   */
  async getSyncStats(integrationId: string, days: number = 7): Promise<{
    totalJobs: number;
    successfulJobs: number;
    failedJobs: number;
    averageDuration: number;
    lastSyncAt?: Date;
    tasksProcessed: number;
    conflictsDetected: number;
  }> {
    const since = new Date();
    since.setDate(since.getDate() - days);

    const syncLogs = await this.prisma.syncLog.findMany({
      where: {
        integrationId,
        createdAt: {
          gte: since,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    const totalJobs = syncLogs.length;
    const successfulJobs = syncLogs.filter(log => log.status === 'completed').length;
    const failedJobs = syncLogs.filter(log => log.status === 'failed').length;
    
    const completedJobs = syncLogs.filter(log => log.completedAt);
    const averageDuration = completedJobs.length > 0
      ? completedJobs.reduce((sum, log) => {
          const duration = log.completedAt!.getTime() - log.startedAt.getTime();
          return sum + duration;
        }, 0) / completedJobs.length
      : 0;

    const tasksProcessed = syncLogs.reduce((sum, log) => sum + log.tasksProcessed, 0);
    
    // Count conflicts from error logs
    const conflictsDetected = syncLogs.reduce((sum, log) => {
      const errors = Array.isArray(log.errors) ? log.errors : [];
      return sum + errors.filter((error: any) => error.type === 'CONFLICT').length;
    }, 0);

    const lastSyncLog = syncLogs.find(log => log.status === 'completed');

    return {
      totalJobs,
      successfulJobs,
      failedJobs,
      averageDuration,
      lastSyncAt: lastSyncLog?.completedAt,
      tasksProcessed,
      conflictsDetected,
    };
  }

  /**
   * Clean up old sync logs and completed jobs
   */
  async cleanupOldData(olderThanDays: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    // Clean up old sync logs
    const deletedLogs = await this.prisma.syncLog.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate,
        },
        status: {
          in: ['completed', 'failed'],
        },
      },
    });

    // Clean up completed jobs from Bull queue
    await this.syncQueue.clean(olderThanDays * 24 * 60 * 60 * 1000, 'completed');
    await this.syncQueue.clean(olderThanDays * 24 * 60 * 60 * 1000, 'failed');

    this.logger.log(`Cleaned up ${deletedLogs.count} old sync logs and completed jobs`);
  }
}