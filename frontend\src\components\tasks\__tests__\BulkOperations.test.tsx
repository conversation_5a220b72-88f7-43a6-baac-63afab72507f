import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { BulkOperations } from '../BulkOperations'
import { TaskStatus, TaskPriority } from '@/types/task'

describe('BulkOperations', () => {
  const mockOnBulkUpdate = vi.fn()
  const mockOnBulkDelete = vi.fn()
  const mockOnClearSelection = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should not render when no tasks are selected', () => {
    const { container } = render(
      <BulkOperations
        selectedTaskIds={[]}
        onBulkUpdate={mockOnBulkUpdate}
        onBulkDelete={mockOnBulkDelete}
        onClearSelection={mockOnClearSelection}
      />
    )

    expect(container.firstChild).toBeNull()
  })

  it('should render when tasks are selected', () => {
    render(
      <BulkOperations
        selectedTaskIds={['task-1', 'task-2']}
        onBulkUpdate={mockOnBulkUpdate}
        onBulkDelete={mockOnBulkDelete}
        onClearSelection={mockOnClearSelection}
      />
    )

    expect(screen.getByText('2 selected')).toBeInTheDocument()
    expect(screen.getByText('In Progress')).toBeInTheDocument()
    expect(screen.getByText('Done')).toBeInTheDocument()
    expect(screen.getByText('Edit')).toBeInTheDocument()
    expect(screen.getByText('Delete')).toBeInTheDocument()
    expect(screen.getByText('Clear')).toBeInTheDocument()
  })

  it('should handle quick status update to In Progress', async () => {
    render(
      <BulkOperations
        selectedTaskIds={['task-1', 'task-2']}
        onBulkUpdate={mockOnBulkUpdate}
        onBulkDelete={mockOnBulkDelete}
        onClearSelection={mockOnClearSelection}
      />
    )

    const inProgressButton = screen.getByText('In Progress')
    fireEvent.click(inProgressButton)

    expect(mockOnBulkUpdate).toHaveBeenCalledWith({
      taskIds: ['task-1', 'task-2'],
      updates: { status: TaskStatus.IN_PROGRESS }
    })
    expect(mockOnClearSelection).toHaveBeenCalled()
  })

  it('should handle quick status update to Done', async () => {
    render(
      <BulkOperations
        selectedTaskIds={['task-1', 'task-2']}
        onBulkUpdate={mockOnBulkUpdate}
        onBulkDelete={mockOnBulkDelete}
        onClearSelection={mockOnClearSelection}
      />
    )

    const doneButton = screen.getByText('Done')
    fireEvent.click(doneButton)

    expect(mockOnBulkUpdate).toHaveBeenCalledWith({
      taskIds: ['task-1', 'task-2'],
      updates: { status: TaskStatus.DONE }
    })
    expect(mockOnClearSelection).toHaveBeenCalled()
  })

  it('should open edit dialog', async () => {
    const user = userEvent.setup()
    
    render(
      <BulkOperations
        selectedTaskIds={['task-1', 'task-2']}
        onBulkUpdate={mockOnBulkUpdate}
        onBulkDelete={mockOnBulkDelete}
        onClearSelection={mockOnClearSelection}
      />
    )

    const editButton = screen.getByText('Edit')
    await user.click(editButton)

    expect(screen.getByText('Update 2 Tasks')).toBeInTheDocument()
    expect(screen.getByLabelText('Status')).toBeInTheDocument()
    expect(screen.getByLabelText('Priority')).toBeInTheDocument()
    expect(screen.getByLabelText('Assignee Name')).toBeInTheDocument()
    expect(screen.getByLabelText('Due Date')).toBeInTheDocument()
    expect(screen.getByLabelText('Tags (comma-separated)')).toBeInTheDocument()
  })

  it('should handle bulk update through edit dialog', async () => {
    const user = userEvent.setup()
    
    render(
      <BulkOperations
        selectedTaskIds={['task-1', 'task-2']}
        onBulkUpdate={mockOnBulkUpdate}
        onBulkDelete={mockOnBulkDelete}
        onClearSelection={mockOnClearSelection}
      />
    )

    // Open edit dialog
    await user.click(screen.getByText('Edit'))

    // Change status
    const statusSelect = screen.getByDisplayValue('Keep current status')
    await user.click(statusSelect)
    await user.click(screen.getByText('In Progress'))

    // Change priority
    const prioritySelect = screen.getByDisplayValue('Keep current priority')
    await user.click(prioritySelect)
    await user.click(screen.getByText('High'))

    // Set assignee
    const assigneeInput = screen.getByLabelText('Assignee Name')
    await user.type(assigneeInput, 'John Doe')

    // Set tags
    const tagsInput = screen.getByLabelText('Tags (comma-separated)')
    await user.type(tagsInput, 'urgent, bug')

    // Submit
    const updateButton = screen.getByText('Update Tasks')
    await user.click(updateButton)

    expect(mockOnBulkUpdate).toHaveBeenCalledWith({
      taskIds: ['task-1', 'task-2'],
      updates: {
        status: TaskStatus.IN_PROGRESS,
        priority: TaskPriority.HIGH,
        assigneeName: 'John Doe',
        tags: ['urgent', 'bug']
      }
    })
    expect(mockOnClearSelection).toHaveBeenCalled()
  })

  it('should not submit empty updates', async () => {
    const user = userEvent.setup()
    
    render(
      <BulkOperations
        selectedTaskIds={['task-1', 'task-2']}
        onBulkUpdate={mockOnBulkUpdate}
        onBulkDelete={mockOnBulkDelete}
        onClearSelection={mockOnClearSelection}
      />
    )

    // Open edit dialog
    await user.click(screen.getByText('Edit'))

    // Submit without making changes
    const updateButton = screen.getByText('Update Tasks')
    await user.click(updateButton)

    expect(mockOnBulkUpdate).not.toHaveBeenCalled()
  })

  it('should open delete dialog', async () => {
    const user = userEvent.setup()
    
    render(
      <BulkOperations
        selectedTaskIds={['task-1', 'task-2']}
        onBulkUpdate={mockOnBulkUpdate}
        onBulkDelete={mockOnBulkDelete}
        onClearSelection={mockOnClearSelection}
      />
    )

    const deleteButton = screen.getByText('Delete')
    await user.click(deleteButton)

    expect(screen.getByText('Delete 2 Tasks')).toBeInTheDocument()
    expect(screen.getByText(/Are you sure you want to delete 2 selected tasks/)).toBeInTheDocument()
  })

  it('should handle bulk delete', async () => {
    const user = userEvent.setup()
    
    render(
      <BulkOperations
        selectedTaskIds={['task-1', 'task-2']}
        onBulkUpdate={mockOnBulkUpdate}
        onBulkDelete={mockOnBulkDelete}
        onClearSelection={mockOnClearSelection}
      />
    )

    // Open delete dialog
    await user.click(screen.getByText('Delete'))

    // Confirm delete
    const confirmDeleteButton = screen.getByRole('button', { name: 'Delete Tasks' })
    await user.click(confirmDeleteButton)

    expect(mockOnBulkDelete).toHaveBeenCalledWith(['task-1', 'task-2'])
    expect(mockOnClearSelection).toHaveBeenCalled()
  })

  it('should handle clear selection', async () => {
    const user = userEvent.setup()
    
    render(
      <BulkOperations
        selectedTaskIds={['task-1', 'task-2']}
        onBulkUpdate={mockOnBulkUpdate}
        onBulkDelete={mockOnBulkDelete}
        onClearSelection={mockOnClearSelection}
      />
    )

    const clearButton = screen.getByText('Clear')
    await user.click(clearButton)

    expect(mockOnClearSelection).toHaveBeenCalled()
  })

  it('should disable buttons when loading', () => {
    render(
      <BulkOperations
        selectedTaskIds={['task-1', 'task-2']}
        onBulkUpdate={mockOnBulkUpdate}
        onBulkDelete={mockOnBulkDelete}
        onClearSelection={mockOnClearSelection}
        isLoading={true}
      />
    )

    expect(screen.getByText('In Progress')).toBeDisabled()
    expect(screen.getByText('Done')).toBeDisabled()
    expect(screen.getByText('Edit')).toBeDisabled()
    expect(screen.getByText('Delete')).toBeDisabled()
  })

  it('should handle due date input', async () => {
    const user = userEvent.setup()
    
    render(
      <BulkOperations
        selectedTaskIds={['task-1']}
        onBulkUpdate={mockOnBulkUpdate}
        onBulkDelete={mockOnBulkDelete}
        onClearSelection={mockOnClearSelection}
      />
    )

    // Open edit dialog
    await user.click(screen.getByText('Edit'))

    // Set due date
    const dueDateInput = screen.getByLabelText('Due Date')
    await user.type(dueDateInput, '2024-12-25')

    // Submit
    await user.click(screen.getByText('Update Tasks'))

    expect(mockOnBulkUpdate).toHaveBeenCalledWith({
      taskIds: ['task-1'],
      updates: {
        dueDate: '2024-12-25T23:59:59Z'
      }
    })
  })
})