import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { User } from '../users/entities/user.entity';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: jest.Mocked<AuthService>;

  const mockUser: User = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    email: '<EMAIL>',
    name: 'Test User',
    avatarUrl: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockAuthResponse = {
    user: mockUser,
    accessToken: 'access-token',
    refreshToken: 'refresh-token',
  };

  beforeEach(async () => {
    const mockAuthService = {
      register: jest.fn(),
      login: jest.fn(),
      googleLogin: jest.fn(),
      refreshToken: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        { provide: AuthService, useValue: mockAuthService },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get(AuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    it('should register a new user', async () => {
      // Arrange
      const registerDto: RegisterDto = {
        email: '<EMAIL>',
        name: 'Test User',
        password: 'password123',
      };
      authService.register.mockResolvedValue(mockAuthResponse);

      // Act
      const result = await controller.register(registerDto);

      // Assert
      expect(authService.register).toHaveBeenCalledWith(registerDto);
      expect(result).toBe(mockAuthResponse);
    });
  });

  describe('login', () => {
    it('should login a user', async () => {
      // Arrange
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };
      authService.login.mockResolvedValue(mockAuthResponse);

      // Act
      const result = await controller.login(loginDto);

      // Assert
      expect(authService.login).toHaveBeenCalledWith(loginDto);
      expect(result).toBe(mockAuthResponse);
    });
  });

  describe('googleAuthCallback', () => {
    it('should handle Google OAuth callback and redirect', async () => {
      // Arrange
      const mockRequest = {
        user: {
          id: 'google-123',
          email: '<EMAIL>',
          displayName: 'Test User',
          picture: 'https://example.com/avatar.jpg',
        },
      } as any;

      const mockResponse = {
        redirect: jest.fn(),
      } as any;

      authService.googleLogin.mockResolvedValue(mockAuthResponse);
      process.env.FRONTEND_URL = 'http://localhost:3000';

      // Act
      await controller.googleAuthCallback(mockRequest, mockResponse);

      // Assert
      expect(authService.googleLogin).toHaveBeenCalledWith(mockRequest.user);
      expect(mockResponse.redirect).toHaveBeenCalledWith(
        `http://localhost:3000/auth/callback?token=${mockAuthResponse.accessToken}&refresh=${mockAuthResponse.refreshToken}`,
      );
    });

    it('should use default frontend URL if not set', async () => {
      // Arrange
      const mockRequest = {
        user: {
          id: 'google-123',
          email: '<EMAIL>',
          displayName: 'Test User',
        },
      } as any;

      const mockResponse = {
        redirect: jest.fn(),
      } as any;

      authService.googleLogin.mockResolvedValue(mockAuthResponse);
      delete process.env.FRONTEND_URL;

      // Act
      await controller.googleAuthCallback(mockRequest, mockResponse);

      // Assert
      expect(mockResponse.redirect).toHaveBeenCalledWith(
        `http://localhost:3000/auth/callback?token=${mockAuthResponse.accessToken}&refresh=${mockAuthResponse.refreshToken}`,
      );
    });
  });

  describe('refreshToken', () => {
    it('should refresh access token', async () => {
      // Arrange
      const refreshTokenDto: RefreshTokenDto = {
        refreshToken: 'valid-refresh-token',
      };
      const mockRefreshResponse = { accessToken: 'new-access-token' };
      authService.refreshToken.mockResolvedValue(mockRefreshResponse);

      // Act
      const result = await controller.refreshToken(refreshTokenDto);

      // Assert
      expect(authService.refreshToken).toHaveBeenCalledWith(refreshTokenDto.refreshToken);
      expect(result).toBe(mockRefreshResponse);
    });
  });

  describe('getProfile', () => {
    it('should return current user profile', async () => {
      // Act
      const result = await controller.getProfile(mockUser);

      // Assert
      expect(result).toBe(mockUser);
    });
  });

  describe('logout', () => {
    it('should return success message', async () => {
      // Act
      const result = await controller.logout();

      // Assert
      expect(result).toEqual({ message: 'Successfully logged out' });
    });
  });
});