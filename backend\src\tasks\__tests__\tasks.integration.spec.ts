import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { TasksModule } from '../tasks.module';
import { DatabaseService } from '../../database/database.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CreateTaskDto } from '../dto/create-task.dto';
import { UpdateTaskDto } from '../dto/update-task.dto';
import { BulkUpdateTasksDto } from '../dto/bulk-update-tasks.dto';
import { TaskSortField, SortOrder } from '../dto/get-tasks-query.dto';

// Mock the JWT Auth Guard
const mockJwtAuthGuard = {
  canActivate: jest.fn(() => true),
};

describe('TasksController (Integration)', () => {
  let app: INestApplication;
  let databaseService: DatabaseService;
  let workspaceId: string;
  let integrationId: string;
  let taskId: string;

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TasksModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ transform: true }));
    
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);
    
    await app.init();

    // Setup test data
    await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    await app.close();
  });

  async function setupTestData() {
    // Create test user
    const user = await databaseService.user.create({
      data: mockUser,
    });

    // Create test workspace
    const workspace = await databaseService.workspace.create({
      data: {
        name: 'Test Workspace',
        slug: 'test-workspace',
        ownerId: user.id,
        settings: {},
      },
    });
    workspaceId = workspace.id;

    // Create test integration
    const integration = await databaseService.integration.create({
      data: {
        workspaceId: workspace.id,
        provider: 'test',
        name: 'Test Integration',
        config: {},
        encryptedCredentials: 'encrypted-creds',
        status: 'ACTIVE',
      },
    });
    integrationId = integration.id;

    // Create test task
    const task = await databaseService.task.create({
      data: {
        workspaceId: workspace.id,
        integrationId: integration.id,
        externalId: 'test-task-1',
        title: 'Test Task',
        description: 'Test task description',
        status: 'todo',
        priority: 'medium',
        priorityScore: 50.0,
        sourceUrl: 'https://example.com/task/1',
        tags: ['test', 'api'],
        projectName: 'Test Project',
      },
    });
    taskId = task.id;
  }

  async function cleanupTestData() {
    if (databaseService) {
      await databaseService.task.deleteMany({});
      await databaseService.integration.deleteMany({});
      await databaseService.workspace.deleteMany({});
      await databaseService.user.deleteMany({});
    }
  }

  describe('GET /workspaces/:workspaceId/tasks', () => {
    it('should return paginated tasks with default sorting', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .expect(200);

      expect(response.body).toHaveProperty('tasks');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('count');
      expect(response.body).toHaveProperty('offset');
      expect(response.body).toHaveProperty('limit');
      expect(response.body).toHaveProperty('hasMore');
      expect(response.body.tasks).toBeInstanceOf(Array);
      expect(response.body.total).toBeGreaterThanOrEqual(1);
      
      // Verify default sorting by priority score (descending)
      if (response.body.tasks.length > 1) {
        for (let i = 0; i < response.body.tasks.length - 1; i++) {
          expect(Number(response.body.tasks[i].priorityScore))
            .toBeGreaterThanOrEqual(Number(response.body.tasks[i + 1].priorityScore));
        }
      }
    });

    it('should filter tasks by status', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ status: 'todo' })
        .expect(200);

      expect(response.body.tasks.every((task: any) => task.status === 'todo')).toBe(true);
    });

    it('should filter tasks by assignee ID', async () => {
      // Create a task with specific assignee
      const taskWithAssignee = await databaseService.task.create({
        data: {
          workspaceId,
          integrationId,
          externalId: 'assignee-test-task',
          title: 'Assignee Test Task',
          assigneeId: 'test-assignee-123',
          assigneeName: 'Test Assignee',
          sourceUrl: 'https://example.com/assignee-task',
        },
      });

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ assigneeId: 'test-assignee-123' })
        .expect(200);

      expect(response.body.tasks.every((task: any) => task.assigneeId === 'test-assignee-123')).toBe(true);
      expect(response.body.tasks.length).toBeGreaterThanOrEqual(1);

      // Cleanup
      await databaseService.task.delete({ where: { id: taskWithAssignee.id } });
    });

    it('should filter tasks by project name', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ projectName: 'Test Project' })
        .expect(200);

      expect(response.body.tasks.every((task: any) => 
        task.projectName && task.projectName.toLowerCase().includes('test project')
      )).toBe(true);
    });

    it('should filter tasks by integration ID', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ integrationId })
        .expect(200);

      expect(response.body.tasks.every((task: any) => task.integrationId === integrationId)).toBe(true);
    });

    it('should filter tasks by priority', async () => {
      // Create a high priority task
      const highPriorityTask = await databaseService.task.create({
        data: {
          workspaceId,
          integrationId,
          externalId: 'high-priority-task',
          title: 'High Priority Task',
          priority: 'high',
          sourceUrl: 'https://example.com/high-priority',
        },
      });

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ priority: 'high' })
        .expect(200);

      expect(response.body.tasks.every((task: any) => task.priority === 'high')).toBe(true);
      expect(response.body.tasks.length).toBeGreaterThanOrEqual(1);

      // Cleanup
      await databaseService.task.delete({ where: { id: highPriorityTask.id } });
    });

    it('should filter tasks by due date range', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 7);
      
      // Create a task with due date
      const taskWithDueDate = await databaseService.task.create({
        data: {
          workspaceId,
          integrationId,
          externalId: 'due-date-task',
          title: 'Task with Due Date',
          dueDate: futureDate,
          sourceUrl: 'https://example.com/due-date-task',
        },
      });

      const dueDateFrom = new Date().toISOString();
      const dueDateTo = new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(); // 14 days from now

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ 
          dueDateFrom,
          dueDateTo
        })
        .expect(200);

      expect(response.body.tasks.every((task: any) => {
        if (!task.dueDate) return false;
        const taskDueDate = new Date(task.dueDate);
        return taskDueDate >= new Date(dueDateFrom) && taskDueDate <= new Date(dueDateTo);
      })).toBe(true);

      // Cleanup
      await databaseService.task.delete({ where: { id: taskWithDueDate.id } });
    });

    it('should sort tasks by different fields', async () => {
      // Test sorting by created date
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ 
          sortBy: TaskSortField.CREATED_AT,
          sortOrder: SortOrder.DESC
        })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      
      // Verify sorting order
      if (response.body.tasks.length > 1) {
        for (let i = 0; i < response.body.tasks.length - 1; i++) {
          expect(new Date(response.body.tasks[i].createdAt).getTime())
            .toBeGreaterThanOrEqual(new Date(response.body.tasks[i + 1].createdAt).getTime());
        }
      }
    });

    it('should sort tasks by title alphabetically', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ 
          sortBy: TaskSortField.TITLE,
          sortOrder: SortOrder.ASC
        })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      
      // Verify alphabetical sorting
      if (response.body.tasks.length > 1) {
        for (let i = 0; i < response.body.tasks.length - 1; i++) {
          expect(response.body.tasks[i].title.toLowerCase())
            .toBeLessThanOrEqual(response.body.tasks[i + 1].title.toLowerCase());
        }
      }
    });

    it('should paginate results correctly', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ limit: 1, offset: 0 })
        .expect(200);

      expect(response.body.limit).toBe(1);
      expect(response.body.offset).toBe(0);
      expect(response.body.tasks.length).toBeLessThanOrEqual(1);
      expect(response.body.hasMore).toBe(response.body.total > 1);
    });

    it('should handle large offset values', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ limit: 10, offset: 1000 })
        .expect(200);

      expect(response.body.offset).toBe(1000);
      expect(response.body.tasks).toBeInstanceOf(Array);
      expect(response.body.hasMore).toBe(false);
    });

    it('should filter by tags', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ tags: 'test,api' })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      // Verify that returned tasks have at least one of the specified tags
      response.body.tasks.forEach((task: any) => {
        const hasMatchingTag = task.tags.some((tag: string) => 
          ['test', 'api'].includes(tag)
        );
        expect(hasMatchingTag).toBe(true);
      });
    });

    it('should combine multiple filters', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ 
          status: 'todo',
          priority: 'medium',
          tags: 'test',
          limit: 5
        })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      response.body.tasks.forEach((task: any) => {
        expect(task.status).toBe('todo');
        expect(task.priority).toBe('medium');
        expect(task.tags).toContain('test');
      });
    });

    it('should validate query parameters', async () => {
      await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ limit: -1 })
        .expect(400);
    });

    it('should validate sort parameters', async () => {
      await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ sortBy: 'invalid_field' })
        .expect(400);
    });

    it('should validate date parameters', async () => {
      await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ dueDateFrom: 'invalid-date' })
        .expect(400);
    });

    it('should respect maximum limit', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ limit: 200 }) // Assuming max limit is 100
        .expect(400);
    });

    it('should include integration information in response', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .expect(200);

      if (response.body.tasks.length > 0) {
        const task = response.body.tasks[0];
        expect(task).toHaveProperty('integration');
        expect(task.integration).toHaveProperty('id');
        expect(task.integration).toHaveProperty('name');
        expect(task.integration).toHaveProperty('provider');
      }
    });
  });

  describe('GET /workspaces/:workspaceId/tasks/search', () => {
    let searchTestTasks: any[] = [];

    beforeEach(async () => {
      // Create specific tasks for search testing
      searchTestTasks = await Promise.all([
        databaseService.task.create({
          data: {
            workspaceId,
            integrationId,
            externalId: 'search-task-1',
            title: 'Bug fix authentication system',
            description: 'Fix OAuth login issues with Google provider',
            status: 'todo',
            priority: 'high',
            tags: ['bug', 'authentication'],
            sourceUrl: 'https://example.com/search1',
          },
        }),
        databaseService.task.create({
          data: {
            workspaceId,
            integrationId,
            externalId: 'search-task-2',
            title: 'Implement user dashboard',
            description: 'Create responsive dashboard for user analytics',
            status: 'in_progress',
            priority: 'medium',
            tags: ['feature', 'ui'],
            sourceUrl: 'https://example.com/search2',
          },
        }),
        databaseService.task.create({
          data: {
            workspaceId,
            integrationId,
            externalId: 'search-task-3',
            title: 'Database optimization',
            description: 'Optimize queries for better performance',
            status: 'todo',
            priority: 'low',
            tags: ['performance', 'database'],
            sourceUrl: 'https://example.com/search3',
          },
        }),
      ]);
    });

    afterEach(async () => {
      // Clean up search test tasks
      await databaseService.task.deleteMany({
        where: { id: { in: searchTestTasks.map(task => task.id) } },
      });
      searchTestTasks = [];
    });

    it('should search tasks by title', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ q: 'authentication' })
        .expect(200);

      expect(response.body).toHaveProperty('tasks');
      expect(response.body.tasks).toBeInstanceOf(Array);
      expect(response.body.tasks.length).toBeGreaterThanOrEqual(1);
      
      // Verify that results contain the search term in title or description
      response.body.tasks.forEach((task: any) => {
        const titleMatch = task.title.toLowerCase().includes('authentication');
        const descriptionMatch = task.description?.toLowerCase().includes('authentication');
        expect(titleMatch || descriptionMatch).toBe(true);
      });
    });

    it('should search tasks by description', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ q: 'OAuth' })
        .expect(200);

      expect(response.body.tasks.length).toBeGreaterThanOrEqual(1);
      
      // Verify that results contain the search term
      const foundTask = response.body.tasks.find((task: any) => 
        task.description?.includes('OAuth')
      );
      expect(foundTask).toBeDefined();
    });

    it('should perform case-insensitive search', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ q: 'DASHBOARD' })
        .expect(200);

      expect(response.body.tasks.length).toBeGreaterThanOrEqual(1);
      
      const foundTask = response.body.tasks.find((task: any) => 
        task.title.toLowerCase().includes('dashboard')
      );
      expect(foundTask).toBeDefined();
    });

    it('should search with multiple terms', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ q: 'user dashboard' })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      
      // Should find tasks containing either "user" or "dashboard"
      if (response.body.tasks.length > 0) {
        const hasRelevantResults = response.body.tasks.some((task: any) => {
          const text = `${task.title} ${task.description || ''}`.toLowerCase();
          return text.includes('user') || text.includes('dashboard');
        });
        expect(hasRelevantResults).toBe(true);
      }
    });

    it('should handle partial word matches', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ q: 'auth' })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      
      // Should find tasks with "authentication" when searching for "auth"
      if (response.body.tasks.length > 0) {
        const hasPartialMatch = response.body.tasks.some((task: any) => {
          const text = `${task.title} ${task.description || ''}`.toLowerCase();
          return text.includes('auth');
        });
        expect(hasPartialMatch).toBe(true);
      }
    });

    it('should return empty results for non-matching search', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ q: 'nonexistentterm12345' })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      expect(response.body.tasks.length).toBe(0);
      expect(response.body.total).toBe(0);
    });

    it('should return all tasks when search query is empty', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ q: '' })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      // Should behave like regular getTasks when no search query
      expect(response.body.total).toBeGreaterThanOrEqual(searchTestTasks.length);
    });

    it('should return all tasks when search query is whitespace only', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ q: '   ' })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      // Should behave like regular getTasks when no meaningful search query
      expect(response.body.total).toBeGreaterThanOrEqual(searchTestTasks.length);
    });

    it('should combine search with status filter', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ 
          q: 'task',
          status: 'todo'
        })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      
      // All returned tasks should have status 'todo'
      response.body.tasks.forEach((task: any) => {
        expect(task.status).toBe('todo');
      });
    });

    it('should combine search with priority filter', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ 
          q: 'fix',
          priority: 'high'
        })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      
      // All returned tasks should have priority 'high'
      response.body.tasks.forEach((task: any) => {
        expect(task.priority).toBe('high');
      });
    });

    it('should combine search with assignee filter', async () => {
      // Update one of our test tasks to have an assignee
      await databaseService.task.update({
        where: { id: searchTestTasks[0].id },
        data: { assigneeId: 'search-test-assignee' },
      });

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ 
          q: 'authentication',
          assigneeId: 'search-test-assignee'
        })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      
      // All returned tasks should have the specified assignee
      response.body.tasks.forEach((task: any) => {
        expect(task.assigneeId).toBe('search-test-assignee');
      });
    });

    it('should combine search with tags filter', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ 
          q: 'bug',
          tags: 'authentication'
        })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      
      // All returned tasks should have the specified tag
      response.body.tasks.forEach((task: any) => {
        expect(task.tags).toContain('authentication');
      });
    });

    it('should combine search with pagination', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ 
          q: 'task',
          limit: 2,
          offset: 0
        })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      expect(response.body.limit).toBe(2);
      expect(response.body.offset).toBe(0);
      expect(response.body.tasks.length).toBeLessThanOrEqual(2);
    });

    it('should combine search with sorting', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ 
          q: 'task',
          sortBy: TaskSortField.TITLE,
          sortOrder: SortOrder.ASC
        })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      
      // Verify sorting order
      if (response.body.tasks.length > 1) {
        for (let i = 0; i < response.body.tasks.length - 1; i++) {
          expect(response.body.tasks[i].title.toLowerCase())
            .toBeLessThanOrEqual(response.body.tasks[i + 1].title.toLowerCase());
        }
      }
    });

    it('should handle special characters in search query', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ q: 'fix & optimize' })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      // Should not throw an error and return valid response structure
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('count');
    });

    it('should maintain search relevance in results', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ q: 'authentication system' })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
      
      // Tasks with both terms should appear before tasks with only one term
      if (response.body.tasks.length > 1) {
        const firstTask = response.body.tasks[0];
        const titleAndDesc = `${firstTask.title} ${firstTask.description || ''}`.toLowerCase();
        const hasBothTerms = titleAndDesc.includes('authentication') && titleAndDesc.includes('system');
        
        // First result should be highly relevant (this is a basic check)
        expect(titleAndDesc.includes('authentication') || titleAndDesc.includes('system')).toBe(true);
      }
    });
  });

  describe('POST /workspaces/:workspaceId/tasks', () => {
    it('should create a new task', async () => {
      const createTaskDto: CreateTaskDto = {
        title: 'New Integration Test Task',
        description: 'Created via integration test',
        status: 'todo',
        priority: 'high',
        sourceUrl: 'https://example.com/task/new',
        tags: ['integration', 'test'],
        projectName: 'Integration Test Project',
      };

      const response = await request(app.getHttpServer())
        .post(`/workspaces/${workspaceId}/tasks`)
        .send(createTaskDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.title).toBe(createTaskDto.title);
      expect(response.body.description).toBe(createTaskDto.description);
      expect(response.body.workspaceId).toBe(workspaceId);
    });

    it('should validate required fields', async () => {
      const invalidTask = {
        description: 'Missing title and sourceUrl',
      };

      await request(app.getHttpServer())
        .post(`/workspaces/${workspaceId}/tasks`)
        .send(invalidTask)
        .expect(400);
    });

    it('should set default values for optional fields', async () => {
      const minimalTask: CreateTaskDto = {
        title: 'Minimal Task',
        sourceUrl: 'https://example.com/minimal',
      };

      const response = await request(app.getHttpServer())
        .post(`/workspaces/${workspaceId}/tasks`)
        .send(minimalTask)
        .expect(201);

      expect(response.body.status).toBe('todo');
      expect(response.body.priority).toBe('medium');
      expect(response.body.tags).toEqual([]);
    });
  });

  describe('GET /workspaces/:workspaceId/tasks/:taskId', () => {
    it('should return a specific task', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/${taskId}`)
        .expect(200);

      expect(response.body.id).toBe(taskId);
      expect(response.body.workspaceId).toBe(workspaceId);
    });

    it('should return 404 for non-existent task', async () => {
      const nonExistentId = '00000000-0000-0000-0000-000000000000';
      
      await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/${nonExistentId}`)
        .expect(404);
    });
  });

  describe('PATCH /workspaces/:workspaceId/tasks/:taskId', () => {
    it('should update a task', async () => {
      const updateTaskDto: UpdateTaskDto = {
        title: 'Updated Task Title',
        status: 'in_progress',
        priority: 'high',
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/${taskId}`)
        .send(updateTaskDto)
        .expect(200);

      expect(response.body.title).toBe(updateTaskDto.title);
      expect(response.body.status).toBe(updateTaskDto.status);
      expect(response.body.priority).toBe(updateTaskDto.priority);
    });

    it('should handle partial updates', async () => {
      const partialUpdate: UpdateTaskDto = {
        description: 'Updated description only',
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/${taskId}`)
        .send(partialUpdate)
        .expect(200);

      expect(response.body.description).toBe(partialUpdate.description);
    });

    it('should return 404 for non-existent task', async () => {
      const nonExistentId = '00000000-0000-0000-0000-000000000000';
      
      await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/${nonExistentId}`)
        .send({ title: 'Updated' })
        .expect(404);
    });
  });

  describe('DELETE /workspaces/:workspaceId/tasks/:taskId', () => {
    it('should delete a task', async () => {
      // Create a task to delete
      const taskToDelete = await databaseService.task.create({
        data: {
          workspaceId,
          integrationId,
          externalId: 'task-to-delete',
          title: 'Task to Delete',
          sourceUrl: 'https://example.com/delete',
        },
      });

      await request(app.getHttpServer())
        .delete(`/workspaces/${workspaceId}/tasks/${taskToDelete.id}`)
        .expect(204);

      // Verify task is deleted
      const deletedTask = await databaseService.task.findUnique({
        where: { id: taskToDelete.id },
      });
      expect(deletedTask).toBeNull();
    });

    it('should return 404 for non-existent task', async () => {
      const nonExistentId = '00000000-0000-0000-0000-000000000000';
      
      await request(app.getHttpServer())
        .delete(`/workspaces/${workspaceId}/tasks/${nonExistentId}`)
        .expect(404);
    });
  });

  describe('PATCH /workspaces/:workspaceId/tasks/bulk', () => {
    let bulkTaskIds: string[];

    beforeEach(async () => {
      // Create multiple tasks for bulk operations
      const tasks = await Promise.all([
        databaseService.task.create({
          data: {
            workspaceId,
            integrationId,
            externalId: 'bulk-task-1',
            title: 'Bulk Task 1',
            status: 'todo',
            priority: 'medium',
            assigneeId: 'original-assignee',
            sourceUrl: 'https://example.com/bulk1',
            tags: ['bulk', 'test'],
          },
        }),
        databaseService.task.create({
          data: {
            workspaceId,
            integrationId,
            externalId: 'bulk-task-2',
            title: 'Bulk Task 2',
            status: 'todo',
            priority: 'low',
            sourceUrl: 'https://example.com/bulk2',
            tags: ['bulk', 'test'],
          },
        }),
        databaseService.task.create({
          data: {
            workspaceId,
            integrationId,
            externalId: 'bulk-task-3',
            title: 'Bulk Task 3',
            status: 'in_progress',
            priority: 'high',
            sourceUrl: 'https://example.com/bulk3',
            tags: ['bulk', 'test'],
          },
        }),
      ]);
      bulkTaskIds = tasks.map(task => task.id);
    });

    afterEach(async () => {
      // Clean up bulk test tasks
      await databaseService.task.deleteMany({
        where: { id: { in: bulkTaskIds } },
      });
    });

    it('should bulk update multiple tasks with all fields', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 7);

      const bulkUpdateDto: BulkUpdateTasksDto = {
        taskIds: bulkTaskIds,
        updates: {
          status: 'in_progress',
          assigneeId: 'user-456',
          assigneeName: 'John Doe',
          priority: 'high',
          dueDate: futureDate.toISOString(),
          tags: ['updated', 'bulk-operation'],
        },
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(bulkUpdateDto)
        .expect(200);

      expect(response.body.updated).toBe(bulkTaskIds.length);
      expect(response.body.tasks).toHaveLength(bulkTaskIds.length);
      
      response.body.tasks.forEach((task: any) => {
        expect(task.status).toBe('in_progress');
        expect(task.assigneeId).toBe('user-456');
        expect(task.assigneeName).toBe('John Doe');
        expect(task.priority).toBe('high');
        expect(new Date(task.dueDate).toDateString()).toBe(futureDate.toDateString());
        expect(task.tags).toEqual(['updated', 'bulk-operation']);
        expect(task.syncStatus).toBe('pending'); // Should be marked for sync
      });
    });

    it('should handle partial bulk updates', async () => {
      const bulkUpdateDto: BulkUpdateTasksDto = {
        taskIds: bulkTaskIds,
        updates: {
          status: 'done',
        },
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(bulkUpdateDto)
        .expect(200);

      expect(response.body.updated).toBe(bulkTaskIds.length);
      response.body.tasks.forEach((task: any) => {
        expect(task.status).toBe('done');
        // Other fields should remain unchanged
        expect(task.tags).toEqual(['bulk', 'test']);
      });
    });

    it('should bulk update assignee information', async () => {
      const bulkUpdateDto: BulkUpdateTasksDto = {
        taskIds: bulkTaskIds.slice(0, 2), // Update only first 2 tasks
        updates: {
          assigneeId: 'new-assignee-123',
          assigneeName: 'Jane Smith',
        },
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(bulkUpdateDto)
        .expect(200);

      expect(response.body.updated).toBe(2);
      expect(response.body.tasks).toHaveLength(2);
      
      response.body.tasks.forEach((task: any) => {
        expect(task.assigneeId).toBe('new-assignee-123');
        expect(task.assigneeName).toBe('Jane Smith');
      });
    });

    it('should bulk clear assignee information', async () => {
      const bulkUpdateDto: BulkUpdateTasksDto = {
        taskIds: bulkTaskIds,
        updates: {
          assigneeId: null,
          assigneeName: null,
        },
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(bulkUpdateDto)
        .expect(200);

      expect(response.body.updated).toBe(bulkTaskIds.length);
      response.body.tasks.forEach((task: any) => {
        expect(task.assigneeId).toBeNull();
        expect(task.assigneeName).toBeNull();
      });
    });

    it('should bulk update due dates', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 14);

      const bulkUpdateDto: BulkUpdateTasksDto = {
        taskIds: bulkTaskIds,
        updates: {
          dueDate: futureDate.toISOString(),
        },
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(bulkUpdateDto)
        .expect(200);

      expect(response.body.updated).toBe(bulkTaskIds.length);
      response.body.tasks.forEach((task: any) => {
        expect(new Date(task.dueDate).toDateString()).toBe(futureDate.toDateString());
      });
    });

    it('should bulk clear due dates', async () => {
      const bulkUpdateDto: BulkUpdateTasksDto = {
        taskIds: bulkTaskIds,
        updates: {
          dueDate: null,
        },
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(bulkUpdateDto)
        .expect(200);

      expect(response.body.updated).toBe(bulkTaskIds.length);
      response.body.tasks.forEach((task: any) => {
        expect(task.dueDate).toBeNull();
      });
    });

    it('should bulk update priority levels', async () => {
      const bulkUpdateDto: BulkUpdateTasksDto = {
        taskIds: bulkTaskIds,
        updates: {
          priority: 'urgent',
        },
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(bulkUpdateDto)
        .expect(200);

      expect(response.body.updated).toBe(bulkTaskIds.length);
      response.body.tasks.forEach((task: any) => {
        expect(task.priority).toBe('urgent');
      });
    });

    it('should bulk update tags', async () => {
      const newTags = ['urgent', 'review-needed', 'backend'];
      
      const bulkUpdateDto: BulkUpdateTasksDto = {
        taskIds: bulkTaskIds,
        updates: {
          tags: newTags,
        },
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(bulkUpdateDto)
        .expect(200);

      expect(response.body.updated).toBe(bulkTaskIds.length);
      response.body.tasks.forEach((task: any) => {
        expect(task.tags).toEqual(newTags);
      });
    });

    it('should handle large bulk operations', async () => {
      // Create additional tasks for large bulk test
      const largeBulkTasks = await Promise.all(
        Array.from({ length: 10 }, (_, i) => 
          databaseService.task.create({
            data: {
              workspaceId,
              integrationId,
              externalId: `large-bulk-task-${i}`,
              title: `Large Bulk Task ${i}`,
              status: 'todo',
              sourceUrl: `https://example.com/large-bulk-${i}`,
            },
          })
        )
      );

      const largeBulkTaskIds = largeBulkTasks.map(task => task.id);

      const bulkUpdateDto: BulkUpdateTasksDto = {
        taskIds: largeBulkTaskIds,
        updates: {
          status: 'in_progress',
          priority: 'medium',
        },
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(bulkUpdateDto)
        .expect(200);

      expect(response.body.updated).toBe(largeBulkTaskIds.length);
      expect(response.body.tasks).toHaveLength(largeBulkTaskIds.length);

      // Cleanup
      await databaseService.task.deleteMany({
        where: { id: { in: largeBulkTaskIds } },
      });
    });

    it('should validate task IDs belong to workspace', async () => {
      const invalidBulkUpdate: BulkUpdateTasksDto = {
        taskIds: ['00000000-0000-0000-0000-000000000000'],
        updates: {
          status: 'done',
        },
      };

      await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(invalidBulkUpdate)
        .expect(400);
    });

    it('should handle mixed valid and invalid task IDs', async () => {
      const mixedBulkUpdate: BulkUpdateTasksDto = {
        taskIds: [bulkTaskIds[0], '00000000-0000-0000-0000-000000000000'],
        updates: {
          status: 'done',
        },
      };

      await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(mixedBulkUpdate)
        .expect(400);
    });

    it('should require at least one task ID', async () => {
      const emptyBulkUpdate = {
        taskIds: [],
        updates: {
          status: 'done',
        },
      };

      await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(emptyBulkUpdate)
        .expect(400);
    });

    it('should require updates object', async () => {
      const noUpdatesDto = {
        taskIds: bulkTaskIds,
      };

      await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(noUpdatesDto)
        .expect(400);
    });

    it('should validate update field types', async () => {
      const invalidUpdatesDto = {
        taskIds: bulkTaskIds,
        updates: {
          status: 123, // Should be string
          dueDate: 'invalid-date',
        },
      };

      await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(invalidUpdatesDto)
        .expect(400);
    });

    it('should include integration information in bulk update response', async () => {
      const bulkUpdateDto: BulkUpdateTasksDto = {
        taskIds: bulkTaskIds,
        updates: {
          status: 'done',
        },
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(bulkUpdateDto)
        .expect(200);

      response.body.tasks.forEach((task: any) => {
        expect(task).toHaveProperty('integration');
        expect(task.integration).toHaveProperty('id');
        expect(task.integration).toHaveProperty('name');
        expect(task.integration).toHaveProperty('provider');
      });
    });

    it('should update priority scores when priority changes', async () => {
      const bulkUpdateDto: BulkUpdateTasksDto = {
        taskIds: bulkTaskIds,
        updates: {
          priority: 'urgent',
        },
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send(bulkUpdateDto)
        .expect(200);

      // Priority scores should be recalculated (this is handled by the service)
      response.body.tasks.forEach((task: any) => {
        expect(task.priority).toBe('urgent');
        expect(typeof task.priorityScore).toBe('string'); // Decimal is returned as string
      });
    });
  });

  describe('Advanced API Features', () => {
    it('should handle concurrent requests without conflicts', async () => {
      // Create multiple concurrent requests to test race conditions
      const concurrentRequests = Array.from({ length: 5 }, () =>
        request(app.getHttpServer())
          .get(`/workspaces/${workspaceId}/tasks`)
          .query({ limit: 10 })
      );

      const responses = await Promise.all(concurrentRequests);

      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('tasks');
        expect(response.body.tasks).toBeInstanceOf(Array);
      });
    });

    it('should maintain consistent pagination across requests', async () => {
      // Get first page
      const firstPage = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ limit: 2, offset: 0, sortBy: TaskSortField.CREATED_AT, sortOrder: SortOrder.DESC })
        .expect(200);

      // Get second page
      const secondPage = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ limit: 2, offset: 2, sortBy: TaskSortField.CREATED_AT, sortOrder: SortOrder.DESC })
        .expect(200);

      // Verify no overlap between pages
      const firstPageIds = firstPage.body.tasks.map((task: any) => task.id);
      const secondPageIds = secondPage.body.tasks.map((task: any) => task.id);
      
      const overlap = firstPageIds.filter((id: string) => secondPageIds.includes(id));
      expect(overlap).toHaveLength(0);
    });

    it('should handle complex filter combinations', async () => {
      // Create a task that matches multiple filters
      const complexTask = await databaseService.task.create({
        data: {
          workspaceId,
          integrationId,
          externalId: 'complex-filter-task',
          title: 'Complex Filter Test Task',
          description: 'This task tests complex filtering',
          status: 'in_progress',
          priority: 'high',
          assigneeId: 'complex-test-user',
          assigneeName: 'Complex Test User',
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
          tags: ['complex', 'filter', 'test'],
          projectName: 'Complex Project',
          sourceUrl: 'https://example.com/complex',
        },
      });

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({
          status: 'in_progress',
          priority: 'high',
          assigneeId: 'complex-test-user',
          tags: 'complex,filter',
          projectName: 'Complex',
          dueDateFrom: new Date().toISOString(),
          dueDateTo: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
        })
        .expect(200);

      expect(response.body.tasks.length).toBeGreaterThanOrEqual(1);
      
      const foundTask = response.body.tasks.find((task: any) => task.id === complexTask.id);
      expect(foundTask).toBeDefined();
      expect(foundTask.status).toBe('in_progress');
      expect(foundTask.priority).toBe('high');
      expect(foundTask.assigneeId).toBe('complex-test-user');

      // Cleanup
      await databaseService.task.delete({ where: { id: complexTask.id } });
    });

    it('should handle empty result sets gracefully', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ 
          status: 'nonexistent-status',
          assigneeId: 'nonexistent-user'
        })
        .expect(200);

      expect(response.body.tasks).toEqual([]);
      expect(response.body.total).toBe(0);
      expect(response.body.count).toBe(0);
      expect(response.body.hasMore).toBe(false);
    });

    it('should handle very large limit values', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ limit: 1000 })
        .expect(400); // Should be rejected due to max limit validation
    });

    it('should handle negative offset values', async () => {
      await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ offset: -1 })
        .expect(400);
    });

    it('should handle malformed UUID parameters', async () => {
      await request(app.getHttpServer())
        .get(`/workspaces/invalid-uuid/tasks`)
        .expect(400);
    });

    it('should handle SQL injection attempts in filters', async () => {
      const maliciousQuery = "'; DROP TABLE tasks; --";
      
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ projectName: maliciousQuery })
        .expect(200);

      // Should return empty results, not cause an error
      expect(response.body.tasks).toBeInstanceOf(Array);
      expect(response.body.total).toBe(0);
    });

    it('should handle XSS attempts in search queries', async () => {
      const xssQuery = '<script>alert("xss")</script>';
      
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ q: xssQuery })
        .expect(200);

      // Should return empty results safely
      expect(response.body.tasks).toBeInstanceOf(Array);
      expect(response.body.total).toBe(0);
    });
  });

  describe('Performance and Load Testing', () => {
    let performanceTestTasks: any[] = [];

    beforeAll(async () => {
      // Create a larger dataset for performance testing
      const taskPromises = Array.from({ length: 50 }, (_, i) => 
        databaseService.task.create({
          data: {
            workspaceId,
            integrationId,
            externalId: `perf-task-${i}`,
            title: `Performance Test Task ${i}`,
            description: `Description for performance test task ${i}`,
            status: i % 3 === 0 ? 'todo' : i % 3 === 1 ? 'in_progress' : 'done',
            priority: i % 4 === 0 ? 'low' : i % 4 === 1 ? 'medium' : i % 4 === 2 ? 'high' : 'urgent',
            assigneeId: i % 5 === 0 ? `user-${i % 3}` : null,
            dueDate: i % 7 === 0 ? new Date(Date.now() + i * 24 * 60 * 60 * 1000) : null,
            tags: [`tag-${i % 10}`, `category-${i % 5}`],
            projectName: `Project ${i % 8}`,
            sourceUrl: `https://example.com/perf-task-${i}`,
          },
        })
      );

      performanceTestTasks = await Promise.all(taskPromises);
    });

    afterAll(async () => {
      // Cleanup performance test data
      await databaseService.task.deleteMany({
        where: { id: { in: performanceTestTasks.map(task => task.id) } },
      });
    });

    it('should handle large result sets efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ limit: 50 })
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.body.tasks.length).toBeLessThanOrEqual(50);
      expect(responseTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle complex search queries efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ 
          q: 'Performance Test',
          status: 'todo',
          priority: 'high',
          limit: 20
        })
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.body.tasks).toBeInstanceOf(Array);
      expect(responseTime).toBeLessThan(3000); // Should complete within 3 seconds
    });

    it('should handle multiple concurrent search requests', async () => {
      const searchQueries = [
        'Performance',
        'Test Task',
        'Description',
        'Project',
        'Category'
      ];

      const startTime = Date.now();
      
      const concurrentSearches = searchQueries.map(query =>
        request(app.getHttpServer())
          .get(`/workspaces/${workspaceId}/tasks/search`)
          .query({ q: query, limit: 10 })
      );

      const responses = await Promise.all(concurrentSearches);
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;

      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.tasks).toBeInstanceOf(Array);
      });

      expect(totalResponseTime).toBeLessThan(10000); // All searches should complete within 10 seconds
    });

    it('should handle bulk operations on large datasets efficiently', async () => {
      const bulkTaskIds = performanceTestTasks.slice(0, 20).map(task => task.id);
      
      const startTime = Date.now();
      
      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspaceId}/tasks/bulk`)
        .send({
          taskIds: bulkTaskIds,
          updates: {
            status: 'in_progress',
            priority: 'medium',
          },
        })
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.body.updated).toBe(20);
      expect(response.body.tasks).toHaveLength(20);
      expect(responseTime).toBeLessThan(5000); // Should complete within 5 seconds
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid workspace ID', async () => {
      const invalidWorkspaceId = 'invalid-workspace-id';
      
      await request(app.getHttpServer())
        .get(`/workspaces/${invalidWorkspaceId}/tasks`)
        .expect(400);
    });

    it('should handle malformed request bodies', async () => {
      await request(app.getHttpServer())
        .post(`/workspaces/${workspaceId}/tasks`)
        .send('invalid json')
        .expect(400);
    });

    it('should handle database connection errors gracefully', async () => {
      // This test would require mocking database failures
      // For now, we'll test that the API structure is robust
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .expect(200);

      expect(response.body).toHaveProperty('tasks');
      expect(response.body).toHaveProperty('total');
    });

    it('should handle timeout scenarios', async () => {
      // Test with a very complex query that might timeout
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks/search`)
        .query({ 
          q: 'a b c d e f g h i j k l m n o p q r s t u v w x y z',
          limit: 1
        })
        .expect(200);

      expect(response.body.tasks).toBeInstanceOf(Array);
    });

    it('should return appropriate error for non-existent workspace', async () => {
      const nonExistentWorkspaceId = '00000000-0000-0000-0000-000000000000';
      
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${nonExistentWorkspaceId}/tasks`)
        .expect(200); // Should return empty results, not error

      expect(response.body.tasks).toEqual([]);
      expect(response.body.total).toBe(0);
    });

    it('should handle malformed JSON in request body', async () => {
      await request(app.getHttpServer())
        .post(`/workspaces/${workspaceId}/tasks`)
        .set('Content-Type', 'application/json')
        .send('{"title": "Test", "sourceUrl": }') // Malformed JSON
        .expect(400);
    });

    it('should handle missing required fields in create request', async () => {
      await request(app.getHttpServer())
        .post(`/workspaces/${workspaceId}/tasks`)
        .send({
          description: 'Missing title and sourceUrl'
        })
        .expect(400);
    });

    it('should handle invalid enum values', async () => {
      await request(app.getHttpServer())
        .get(`/workspaces/${workspaceId}/tasks`)
        .query({ sortBy: 'invalid_sort_field' })
        .expect(400);
    });
  });
});