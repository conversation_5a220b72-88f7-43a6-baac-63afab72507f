import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'

import { ProgressTracker } from '../ProgressTracker'

describe('ProgressTracker', () => {
  const renderComponent = (props = {}) => {
    const defaultProps = {
      totalEstimated: 240, // 4 hours
      totalCompleted: 120, // 2 hours
      completionProgress: 50,
      exceedsRecommended: false,
      ...props
    }

    return render(<ProgressTracker {...defaultProps} />)
  }

  it('displays total planned time correctly', () => {
    renderComponent({ totalEstimated: 150 }) // 2h 30m

    expect(screen.getByText('2h 30m')).toBeInTheDocument()
    expect(screen.getByText('Total Planned')).toBeInTheDocument()
  })

  it('displays completed time correctly', () => {
    renderComponent({ totalCompleted: 90 }) // 1h 30m

    expect(screen.getByText('1h 30m')).toBeInTheDocument()
    expect(screen.getByText('Completed')).toBeInTheDocument()
  })

  it('shows completion progress percentage', () => {
    renderComponent({ completionProgress: 75 })

    expect(screen.getByText('75%')).toBeInTheDocument()
    expect(screen.getByText('75% of plan')).toBeInTheDocument()
  })

  it('displays progress bar with correct value', () => {
    renderComponent({ completionProgress: 60 })

    const progressBar = screen.getByRole('progressbar')
    expect(progressBar).toHaveAttribute('aria-valuenow', '60')
  })

  it('shows good workload status for normal time', () => {
    renderComponent({ totalEstimated: 300, exceedsRecommended: false })

    expect(screen.getByText('Good workload')).toBeInTheDocument()
  })

  it('shows full day planned status for 6+ hours', () => {
    renderComponent({ totalEstimated: 400, exceedsRecommended: false }) // 6h 40m

    expect(screen.getByText('Full day planned')).toBeInTheDocument()
  })

  it('shows exceeds recommended status when over 8 hours', () => {
    renderComponent({ totalEstimated: 500, exceedsRecommended: true })

    expect(screen.getByText('Exceeds recommended 8h')).toBeInTheDocument()
  })

  it('displays remaining time correctly', () => {
    renderComponent({ totalEstimated: 240, totalCompleted: 120 })

    expect(screen.getByText('2h')).toBeInTheDocument() // 240 - 120 = 120 minutes = 2h
    expect(screen.getByText('Remaining')).toBeInTheDocument()
  })

  it('calculates time efficiency correctly', () => {
    renderComponent({ totalEstimated: 200, totalCompleted: 100 })

    expect(screen.getByText('50%')).toBeInTheDocument()
    expect(screen.getByText('Time Efficiency')).toBeInTheDocument()
  })

  it('shows planned hours correctly', () => {
    renderComponent({ totalEstimated: 270 }) // 4.5 hours

    expect(screen.getByText('4.5h')).toBeInTheDocument()
    expect(screen.getByText('Planned Hours')).toBeInTheDocument()
  })

  it('shows workload status with checkmark for good workload', () => {
    renderComponent({ exceedsRecommended: false })

    expect(screen.getByText('✅')).toBeInTheDocument()
    expect(screen.getByText('Workload Status')).toBeInTheDocument()
  })

  it('shows workload status with warning for excessive workload', () => {
    renderComponent({ exceedsRecommended: true })

    expect(screen.getByText('⚠️')).toBeInTheDocument()
    expect(screen.getByText('Workload Status')).toBeInTheDocument()
  })

  it('handles zero completed time', () => {
    renderComponent({ totalCompleted: 0, completionProgress: 0 })

    expect(screen.getByText('0m')).toBeInTheDocument()
    expect(screen.getByText('0% of plan')).toBeInTheDocument()
  })

  it('handles zero estimated time', () => {
    renderComponent({ totalEstimated: 0, totalCompleted: 0, completionProgress: 0 })

    expect(screen.getByText('0m')).toBeInTheDocument()
    expect(screen.getByText('0% of plan')).toBeInTheDocument()
  })

  it('formats minutes-only durations correctly', () => {
    renderComponent({ totalEstimated: 45, totalCompleted: 30 })

    expect(screen.getByText('45m')).toBeInTheDocument() // Total planned
    expect(screen.getByText('30m')).toBeInTheDocument() // Completed
  })

  it('formats hour-only durations correctly', () => {
    renderComponent({ totalEstimated: 120 }) // Exactly 2 hours

    expect(screen.getByText('2h')).toBeInTheDocument()
  })

  it('shows progress bar scale markers', () => {
    renderComponent()

    expect(screen.getByText('0%')).toBeInTheDocument()
    expect(screen.getByText('50%')).toBeInTheDocument()
    expect(screen.getByText('100%')).toBeInTheDocument()
  })

  it('displays additional metrics section when there is estimated time', () => {
    renderComponent({ totalEstimated: 240 })

    expect(screen.getByText('Remaining')).toBeInTheDocument()
    expect(screen.getByText('Time Efficiency')).toBeInTheDocument()
    expect(screen.getByText('Planned Hours')).toBeInTheDocument()
    expect(screen.getByText('Workload Status')).toBeInTheDocument()
  })

  it('does not display additional metrics when no estimated time', () => {
    renderComponent({ totalEstimated: 0 })

    expect(screen.queryByText('Remaining')).not.toBeInTheDocument()
    expect(screen.queryByText('Time Efficiency')).not.toBeInTheDocument()
  })

  it('handles 100% completion correctly', () => {
    renderComponent({ 
      totalEstimated: 120, 
      totalCompleted: 120, 
      completionProgress: 100 
    })

    expect(screen.getByText('100%')).toBeInTheDocument()
    expect(screen.getByText('100% of plan')).toBeInTheDocument()
    expect(screen.getByText('0h')).toBeInTheDocument() // Remaining time
  })

  it('handles over 100% completion (overtime)', () => {
    renderComponent({ 
      totalEstimated: 120, 
      totalCompleted: 150, 
      completionProgress: 125 
    })

    expect(screen.getByText('125%')).toBeInTheDocument()
    expect(screen.getByText('125% of plan')).toBeInTheDocument()
  })

  it('shows correct time efficiency when no completion', () => {
    renderComponent({ totalEstimated: 120, totalCompleted: 0 })

    expect(screen.getByText('0%')).toBeInTheDocument()
    expect(screen.getByText('Time Efficiency')).toBeInTheDocument()
  })

  it('rounds decimal hours correctly', () => {
    renderComponent({ totalEstimated: 100 }) // 1.67 hours should round to 1.7h

    expect(screen.getByText('1.7h')).toBeInTheDocument()
  })

  it('shows appropriate status icons and colors', () => {
    renderComponent({ totalEstimated: 300, exceedsRecommended: false })

    // Should show target icon for full day
    const statusSection = screen.getByText('Full day planned').closest('div')
    expect(statusSection).toHaveClass('text-amber-600')
  })

  it('shows warning styling for excessive workload', () => {
    renderComponent({ totalEstimated: 500, exceedsRecommended: true })

    const statusSection = screen.getByText('Exceeds recommended 8h').closest('div')
    expect(statusSection).toHaveClass('text-red-600')
  })

  it('shows positive styling for good workload', () => {
    renderComponent({ totalEstimated: 200, exceedsRecommended: false })

    const statusSection = screen.getByText('Good workload').closest('div')
    expect(statusSection).toHaveClass('text-green-600')
  })

  it('handles edge case of very small remaining time', () => {
    renderComponent({ totalEstimated: 121, totalCompleted: 120 })

    expect(screen.getByText('0h')).toBeInTheDocument() // Should round down to 0h
  })
})