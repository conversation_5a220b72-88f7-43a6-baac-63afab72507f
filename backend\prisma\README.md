# Database Setup

This directory contains the Prisma schema and database migrations for TaskUnify.

## Prerequisites

- Neon PostgreSQL database (recommended) or PostgreSQL 14+
- Redis server for caching and job queues (can use Upstash Redis for serverless)

## Quick Start

### Option 1: Using Neon (Recommended)

1. **Create a Neon project**:
   - Go to [Neon Console](https://console.neon.tech)
   - Create a new project named "TaskUnify"
   - Copy the connection string

2. **Install dependencies** (if not already done):
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   Copy `.env.example` to `.env` and update with your Neon connection string:
   ```
   DATABASE_URL="postgresql://username:<EMAIL>/neondb?sslmode=require"
   DIRECT_URL="postgresql://username:<EMAIL>/neondb?sslmode=require"
   ```

### Option 2: Using Local PostgreSQL

1. **Install dependencies** (if not already done):
   ```bash
   npm install
   ```

2. **Set up environment variables**:
   Copy `.env.example` to `.env` and update the `DATABASE_URL` with your PostgreSQL connection string:
   ```
   DATABASE_URL="postgresql://username:password@localhost:5432/taskunify_dev"
   DIRECT_URL="postgresql://username:password@localhost:5432/taskunify_dev"
   ```

4. **Run database migrations**:
   ```bash
   npm run prisma:migrate
   ```

5. **Generate Prisma client**:
   ```bash
   npm run prisma:generate
   ```

6. **Seed the database with test data**:
   ```bash
   npm run prisma:seed
   ```

## Database Schema

The schema includes the following main entities:

### Core Models

- **User**: User accounts with authentication
- **Workspace**: Isolated environments for different contexts (personal, client work, etc.)
- **WorkspaceMember**: Many-to-many relationship between users and workspaces with roles
- **Integration**: Connected external services (Asana, Trello, Google Sheets, etc.)
- **Task**: Unified task representation from all integrations
- **SyncLog**: Audit trail for synchronization operations

### Key Features

- **UUID Primary Keys**: All entities use UUID for better security and distribution
- **Soft Relationships**: Tasks maintain external IDs for source system references
- **Flexible Metadata**: JSON fields for integration-specific data
- **Audit Trail**: Created/updated timestamps on all entities
- **Performance Indexes**: Optimized for common query patterns

## Available Scripts

- `npm run prisma:generate` - Generate Prisma client
- `npm run prisma:migrate` - Run pending migrations
- `npm run prisma:deploy` - Deploy migrations (production)
- `npm run prisma:studio` - Open Prisma Studio GUI
- `npm run prisma:seed` - Seed database with test data
- `npm run db:reset` - Reset database and re-run migrations

## Development Workflow

1. **Making Schema Changes**:
   - Edit `schema.prisma`
   - Run `npm run prisma:migrate` to create migration
   - Run `npm run prisma:generate` to update client

2. **Testing with Fresh Data**:
   ```bash
   npm run db:reset
   npm run prisma:seed
   ```

3. **Viewing Data**:
   ```bash
   npm run prisma:studio
   ```

## Production Deployment

For production deployments with Neon:

1. Create a separate Neon project for production
2. Set production `DATABASE_URL` and `DIRECT_URL` in your deployment environment
3. Run `npm run prisma:deploy` to apply migrations
4. Ensure `NODE_ENV=production` is set
5. Do not run seed script in production

## Neon-Specific Features

### Database Branching
Neon supports database branching for safe schema changes:
```bash
# This would be done through Neon Console or API
# Create a branch for testing migrations
# Test your changes on the branch
# Merge back to main when ready
```

### Connection Pooling
Neon automatically handles connection pooling, but you can optimize with:
```bash
# Add to your DATABASE_URL for better performance
?pgbouncer=true&connection_limit=10
```

## Seed Data

The seed script creates:
- 3 test users (<EMAIL>, <EMAIL>, <EMAIL>)
- 2 workspaces (Personal Tasks, Digital Agency)
- Multiple workspace memberships with different roles
- 3 integrations (Asana, Trello, Google Sheets)
- 5 sample tasks with various statuses and priorities
- Sample sync logs showing successful and partial sync operations

This provides a realistic development environment for testing all features.

## Troubleshooting

**Connection Issues**:
- Ensure PostgreSQL is running
- Check DATABASE_URL format
- Verify database exists and user has permissions

**Migration Issues**:
- Check for schema conflicts
- Ensure no manual database changes
- Use `npm run db:reset` for clean slate

**Performance Issues**:
- Review query patterns
- Check index usage with `EXPLAIN ANALYZE`
- Consider adding indexes for new query patterns