import { IsOptional, IsString, <PERSON><PERSON>ateString, <PERSON><PERSON><PERSON>y, <PERSON><PERSON><PERSON><PERSON>, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateTaskDto {
  @ApiProperty({ 
    description: 'Task title', 
    example: 'Fix authentication bug',
    required: false 
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ 
    description: 'Task description', 
    example: 'Users are unable to login with Google OAuth',
    required: false 
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    description: 'Task status', 
    example: 'in_progress',
    required: false 
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({ 
    description: 'Priority level', 
    example: 'high',
    required: false 
  })
  @IsOptional()
  @IsString()
  priority?: string;

  @ApiProperty({ 
    description: 'Assignee ID', 
    example: 'user-123',
    required: false 
  })
  @IsOptional()
  @IsString()
  assigneeId?: string;

  @ApiProperty({ 
    description: 'Assignee name', 
    example: '<PERSON>',
    required: false 
  })
  @IsOptional()
  @IsString()
  assigneeName?: string;

  @ApiProperty({ 
    description: 'Due date (ISO string)', 
    example: '2024-12-31T23:59:59Z',
    required: false 
  })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiProperty({ 
    description: 'Estimated time in minutes', 
    example: 120,
    required: false 
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  estimatedMinutes?: number;

  @ApiProperty({ 
    description: 'Tags array', 
    example: ['urgent', 'bug'],
    required: false 
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ 
    description: 'Project name', 
    example: 'Authentication System',
    required: false 
  })
  @IsOptional()
  @IsString()
  projectName?: string;
}