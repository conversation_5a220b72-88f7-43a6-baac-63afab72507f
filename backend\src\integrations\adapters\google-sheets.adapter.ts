import { Injectable, Logger } from '@nestjs/common';
import { google, sheets_v4 } from 'googleapis';
import { BaseIntegrationAdapter } from './base-integration.adapter';
import {
  IntegrationProvider,
  IntegrationConfig,
  OAuthCredentials,
  ExternalTask,
  TaskUpdate,
  CreateTaskRequest,
  AuthResult,
  TaskStatus,
  TaskPriority,
} from '../types';

/**
 * Google Sheets integration adapter
 * Provides task reading and writing functionality with configurable column mapping
 */
@Injectable()
export class GoogleSheetsAdapter extends BaseIntegrationAdapter {

  constructor() {
    super(IntegrationProvider.GOOGLE_SHEETS, 'https://sheets.googleapis.com/v4');
  }

  /**
   * Authenticate with Google Sheets using OAuth credentials
   */
  async authenticate(credentials: OAuthCredentials): Promise<AuthResult> {
    try {
      const auth = new google.auth.OAuth2();
      auth.setCredentials({
        access_token: credentials.accessToken,
        refresh_token: credentials.refreshToken,
      });

      // Test authentication by getting user info
      const oauth2 = google.oauth2({ version: 'v2', auth });
      const userInfo = await oauth2.userinfo.get();

      return {
        success: true,
        user: {
          id: userInfo.data.id || '',
          email: userInfo.data.email || '',
          name: userInfo.data.name || '',
          avatar: userInfo.data.picture,
        },
        credentials,
      };
    } catch (error) {
      this.logger.error('Google Sheets authentication failed:', error);
      return {
        success: false,
        error: 'Failed to authenticate with Google Sheets',
      };
    }
  }

  /**
   * Fetch tasks from Google Sheets with configurable column mapping
   */
  async fetchTasks(config: IntegrationConfig): Promise<ExternalTask[]> {
    try {
      const auth = this.createAuthClient(config);
      const sheets = google.sheets({ version: 'v4', auth });

      const spreadsheetId = config.customSettings?.spreadsheetId;
      const sheetName = config.customSettings?.sheetName || 'Tasks';
      const range = config.customSettings?.range || `${sheetName}!A:Z`;

      if (!spreadsheetId) {
        throw new Error('Spreadsheet ID is required in configuration');
      }

      const response = await sheets.spreadsheets.values.get({
        spreadsheetId,
        range,
      });

      const rows = response.data.values || [];
      if (rows.length === 0) {
        return [];
      }

      // First row contains headers
      const headers = rows[0];
      const dataRows = rows.slice(1);

      const columnMappings = this.getColumnMappings(config, headers);
      const tasks: ExternalTask[] = [];

      for (let i = 0; i < dataRows.length; i++) {
        const row = dataRows[i];
        const rowIndex = i + 2; // +2 because we skip header and arrays are 0-indexed

        try {
          const task = this.mapRowToTask(row, columnMappings, spreadsheetId, sheetName, rowIndex);
          if (task) {
            tasks.push(task);
          }
        } catch (error) {
          this.logger.warn(`Failed to parse row ${rowIndex}:`, error);
          continue;
        }
      }

      return tasks;
    } catch (error) {
      this.logger.error('Failed to fetch tasks from Google Sheets:', error);
      throw this.handleApiError(error, 'fetchTasks');
    }
  }

  /**
   * Update a task in Google Sheets
   */
  async updateTask(taskId: string, updates: TaskUpdate): Promise<ExternalTask> {
    try {
      const auth = this.createAuthClient();
      const sheets = google.sheets({ version: 'v4', auth });

      // Parse taskId to get spreadsheet info and row number
      const { spreadsheetId, sheetName, rowIndex } = this.parseTaskId(taskId);

      // Get current row data
      const range = `${sheetName}!${rowIndex}:${rowIndex}`;
      const response = await sheets.spreadsheets.values.get({
        spreadsheetId,
        range,
      });

      const currentRow = response.data.values?.[0] || [];
      
      // Get headers to understand column mapping
      const headersResponse = await sheets.spreadsheets.values.get({
        spreadsheetId,
        range: `${sheetName}!1:1`,
      });
      const headers = headersResponse.data.values?.[0] || [];

      // Apply updates to the row
      const updatedRow = this.applyUpdatesToRow(currentRow, headers, updates);

      // Update the row in the sheet
      await sheets.spreadsheets.values.update({
        spreadsheetId,
        range,
        valueInputOption: 'USER_ENTERED',
        requestBody: {
          values: [updatedRow],
        },
      });

      // Return the updated task
      const columnMappings = this.getDefaultColumnMappings(headers);
      return this.mapRowToTask(updatedRow, columnMappings, spreadsheetId, sheetName, parseInt(rowIndex));
    } catch (error) {
      this.logger.error('Failed to update task in Google Sheets:', error);
      throw this.handleApiError(error, 'updateTask');
    }
  }

  /**
   * Create a new task in Google Sheets
   */
  async createTask(task: CreateTaskRequest): Promise<ExternalTask> {
    try {
      const auth = this.createAuthClient();
      const sheets = google.sheets({ version: 'v4', auth });

      // Use default spreadsheet from config or throw error
      const spreadsheetId = process.env.GOOGLE_SHEETS_DEFAULT_SPREADSHEET_ID;
      const sheetName = process.env.GOOGLE_SHEETS_DEFAULT_SHEET_NAME || 'Tasks';

      if (!spreadsheetId) {
        throw new Error('Default spreadsheet ID not configured');
      }

      // Get headers to understand column structure
      const headersResponse = await sheets.spreadsheets.values.get({
        spreadsheetId,
        range: `${sheetName}!1:1`,
      });
      const headers = headersResponse.data.values?.[0] || [];

      // Create row data from task
      const rowData = this.mapTaskToRow(task, headers);

      // Append the new row
      const response = await sheets.spreadsheets.values.append({
        spreadsheetId,
        range: `${sheetName}!A:Z`,
        valueInputOption: 'USER_ENTERED',
        requestBody: {
          values: [rowData],
        },
      });

      // Calculate the row index of the newly created task
      const updatedRange = response.data.updates?.updatedRange || '';
      const rowMatch = updatedRange.match(/(\d+)$/);
      const rowIndex = rowMatch ? parseInt(rowMatch[1]) : 0;

      // Generate task ID
      const taskId = this.generateTaskId(spreadsheetId, sheetName, rowIndex);

      // Return the created task
      const columnMappings = this.getDefaultColumnMappings(headers);
      return this.mapRowToTask(rowData, columnMappings, spreadsheetId, sheetName, rowIndex);
    } catch (error) {
      this.logger.error('Failed to create task in Google Sheets:', error);
      throw this.handleApiError(error, 'createTask');
    }
  }

  /**
   * Validate Google Sheets credentials
   */
  async validateCredentials(credentials: OAuthCredentials): Promise<boolean> {
    try {
      const auth = new google.auth.OAuth2();
      auth.setCredentials({
        access_token: credentials.accessToken,
        refresh_token: credentials.refreshToken,
      });

      // Test by making a simple API call
      const oauth2 = google.oauth2({ version: 'v2', auth });
      await oauth2.userinfo.get();

      return true;
    } catch (error) {
      this.logger.error('Google Sheets credential validation failed:', error);
      return false;
    }
  }

  /**
   * Google Sheets supports two-way sync
   */
  supportsTwoWaySync(): boolean {
    return true;
  }

  /**
   * Google Sheets doesn't support webhooks directly
   */
  supportsWebhooks(): boolean {
    return false;
  }

  /**
   * Create authenticated Google client
   */
  private createAuthClient(config?: IntegrationConfig): any {
    const auth = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
    );

    // Set credentials from config or environment
    if (config?.customSettings?.credentials) {
      auth.setCredentials(config.customSettings.credentials);
    }

    return auth;
  }

  /**
   * Get column mappings from configuration or use defaults
   */
  private getColumnMappings(config: IntegrationConfig, headers: string[]): Record<string, number> {
    let mappings: Record<string, number> = {};

    // Use configured field mappings if available
    if (config.fieldMappings && config.fieldMappings.length > 0) {
      for (const mapping of config.fieldMappings) {
        const columnIndex = headers.findIndex(header => 
          header.toLowerCase() === mapping.externalField.toLowerCase()
        );
        if (columnIndex !== -1) {
          mappings[mapping.localField] = columnIndex;
        }
      }
    } else {
      // Use default mappings
      mappings = this.getDefaultColumnMappings(headers);
    }

    return mappings;
  }

  /**
   * Get default column mappings based on common header names
   */
  private getDefaultColumnMappings(headers: string[]): Record<string, number> {
    const mappings: Record<string, number> = {};

    const fieldMap = {
      title: ['title', 'task', 'name', 'summary', 'description'],
      description: ['description', 'details', 'notes', 'comment'],
      status: ['status', 'state', 'progress'],
      priority: ['priority', 'importance', 'urgency'],
      assignee: ['assignee', 'assigned to', 'owner', 'responsible'],
      dueDate: ['due date', 'deadline', 'due', 'target date'],
      estimatedMinutes: ['estimate', 'estimated time', 'duration', 'effort'],
      tags: ['tags', 'labels', 'categories'],
      project: ['project', 'category', 'area'],
    };

    for (const [localField, possibleHeaders] of Object.entries(fieldMap)) {
      for (const possibleHeader of possibleHeaders) {
        const columnIndex = headers.findIndex(header => 
          header.toLowerCase().includes(possibleHeader.toLowerCase())
        );
        if (columnIndex !== -1) {
          mappings[localField] = columnIndex;
          break;
        }
      }
    }

    return mappings;
  }

  /**
   * Map a spreadsheet row to an ExternalTask
   */
  private mapRowToTask(
    row: string[], 
    columnMappings: Record<string, number>, 
    spreadsheetId: string, 
    sheetName: string, 
    rowIndex: number
  ): ExternalTask | null {
    const title = this.getCellValue(row, columnMappings.title);
    if (!title || title.trim().length === 0) {
      return null; // Skip rows without titles
    }

    const taskId = this.generateTaskId(spreadsheetId, sheetName, rowIndex);
    const sourceUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit#gid=0&range=${sheetName}!${rowIndex}:${rowIndex}`;

    return {
      id: taskId,
      title: title.trim(),
      description: this.getCellValue(row, columnMappings.description),
      status: this.normalizeTaskStatus(this.getCellValue(row, columnMappings.status) || 'todo') as TaskStatus,
      priority: this.normalizeTaskPriority(this.getCellValue(row, columnMappings.priority) || 'medium') as TaskPriority,
      assigneeName: this.getCellValue(row, columnMappings.assignee),
      dueDate: this.parseDateValue(this.getCellValue(row, columnMappings.dueDate)),
      estimatedMinutes: this.parseNumberValue(this.getCellValue(row, columnMappings.estimatedMinutes)),
      tags: this.parseTagsValue(this.getCellValue(row, columnMappings.tags)),
      projectName: this.getCellValue(row, columnMappings.project),
      sourceUrl,
      metadata: {
        spreadsheetId,
        sheetName,
        rowIndex,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * Map a task to a spreadsheet row
   */
  private mapTaskToRow(task: CreateTaskRequest, headers: string[]): string[] {
    const row: string[] = new Array(headers.length).fill('');
    const mappings = this.getDefaultColumnMappings(headers);

    if (mappings.title !== undefined) {
      row[mappings.title] = task.title;
    }
    if (mappings.description !== undefined && task.description) {
      row[mappings.description] = task.description;
    }
    if (mappings.priority !== undefined && task.priority) {
      row[mappings.priority] = task.priority;
    }
    if (mappings.dueDate !== undefined && task.dueDate) {
      row[mappings.dueDate] = task.dueDate.toISOString().split('T')[0]; // YYYY-MM-DD format
    }
    if (mappings.estimatedMinutes !== undefined && task.estimatedMinutes) {
      row[mappings.estimatedMinutes] = task.estimatedMinutes.toString();
    }
    if (mappings.tags !== undefined && task.tags) {
      row[mappings.tags] = task.tags.join(', ');
    }
    if (mappings.status !== undefined) {
      row[mappings.status] = 'todo'; // New tasks start as todo
    }

    return row;
  }

  /**
   * Apply task updates to a spreadsheet row
   */
  private applyUpdatesToRow(currentRow: string[], headers: string[], updates: TaskUpdate): string[] {
    const updatedRow = [...currentRow];
    const mappings = this.getDefaultColumnMappings(headers);

    if (updates.title !== undefined && mappings.title !== undefined) {
      updatedRow[mappings.title] = updates.title;
    }
    if (updates.description !== undefined && mappings.description !== undefined) {
      updatedRow[mappings.description] = updates.description || '';
    }
    if (updates.status !== undefined && mappings.status !== undefined) {
      updatedRow[mappings.status] = updates.status;
    }
    if (updates.priority !== undefined && mappings.priority !== undefined) {
      updatedRow[mappings.priority] = updates.priority;
    }
    if (updates.assigneeId !== undefined && mappings.assignee !== undefined) {
      updatedRow[mappings.assignee] = updates.assigneeId || '';
    }
    if (updates.dueDate !== undefined && mappings.dueDate !== undefined) {
      updatedRow[mappings.dueDate] = updates.dueDate ? updates.dueDate.toISOString().split('T')[0] : '';
    }
    if (updates.estimatedMinutes !== undefined && mappings.estimatedMinutes !== undefined) {
      updatedRow[mappings.estimatedMinutes] = updates.estimatedMinutes ? updates.estimatedMinutes.toString() : '';
    }
    if (updates.tags !== undefined && mappings.tags !== undefined) {
      updatedRow[mappings.tags] = updates.tags ? updates.tags.join(', ') : '';
    }

    return updatedRow;
  }

  /**
   * Generate a unique task ID for Google Sheets tasks
   */
  private generateTaskId(spreadsheetId: string, sheetName: string, rowIndex: number): string {
    return `gs_${spreadsheetId}_${sheetName}_${rowIndex}`;
  }

  /**
   * Parse a task ID to extract spreadsheet information
   */
  private parseTaskId(taskId: string): { spreadsheetId: string; sheetName: string; rowIndex: string } {
    const parts = taskId.split('_');
    if (parts.length < 4 || parts[0] !== 'gs') {
      throw new Error('Invalid Google Sheets task ID format');
    }

    return {
      spreadsheetId: parts[1],
      sheetName: parts[2],
      rowIndex: parts[3],
    };
  }

  /**
   * Get cell value safely
   */
  private getCellValue(row: string[], columnIndex: number | undefined): string | undefined {
    if (columnIndex === undefined || columnIndex >= row.length) {
      return undefined;
    }
    return row[columnIndex]?.trim() || undefined;
  }

  /**
   * Parse date value from cell
   */
  private parseDateValue(value: string | undefined): Date | undefined {
    if (!value) return undefined;

    // Try different date formats
    const dateFormats = [
      /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
      /^\d{2}\/\d{2}\/\d{4}$/, // MM/DD/YYYY
      /^\d{2}-\d{2}-\d{4}$/, // MM-DD-YYYY
    ];

    for (const format of dateFormats) {
      if (format.test(value)) {
        const date = new Date(value);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
    }

    return undefined;
  }

  /**
   * Parse number value from cell
   */
  private parseNumberValue(value: string | undefined): number | undefined {
    if (!value) return undefined;
    const num = parseInt(value, 10);
    return isNaN(num) ? undefined : num;
  }

  /**
   * Parse tags value from cell
   */
  private parseTagsValue(value: string | undefined): string[] {
    if (!value) return [];
    return value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
  }
}