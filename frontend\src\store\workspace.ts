import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface Workspace {
  id: string
  name: string
  slug: string
  ownerId: string
  settings: WorkspaceSettings
  createdAt: string
  updatedAt: string
}

export interface WorkspaceSettings {
  priorityWeights: {
    dueDateProximity: number
    effortEstimate: number
    businessImpact: number
    contextSwitching: number
  }
  defaultSyncInterval: number
  enableTwoWaySync: boolean
}

export interface WorkspaceMember {
  id: string
  userId: string
  workspaceId: string
  role: 'OWNER' | 'ADMIN' | 'MEMBER'
  permissions: string[]
  joinedAt: string
}

interface WorkspaceState {
  workspaces: Workspace[]
  currentWorkspace: Workspace | null
  members: WorkspaceMember[]
  isLoading: boolean
}

interface WorkspaceActions {
  setWorkspaces: (workspaces: Workspace[]) => void
  setCurrentWorkspace: (workspace: Workspace | null) => void
  addWorkspace: (workspace: Workspace) => void
  updateWorkspace: (id: string, updates: Partial<Workspace>) => void
  removeWorkspace: (id: string) => void
  setMembers: (members: WorkspaceMember[]) => void
  addMember: (member: WorkspaceMember) => void
  updateMember: (id: string, updates: Partial<WorkspaceMember>) => void
  removeMember: (id: string) => void
  setLoading: (loading: boolean) => void
  switchWorkspace: (workspaceId: string) => void
}

export const useWorkspaceStore = create<WorkspaceState & WorkspaceActions>()(
  persist(
    (set, get) => ({
      // State
      workspaces: [],
      currentWorkspace: null,
      members: [],
      isLoading: false,

      // Actions
      setWorkspaces: (workspaces: Workspace[]) => {
        set({ workspaces })
        
        // If no current workspace is set, set the first one
        const { currentWorkspace } = get()
        if (!currentWorkspace && workspaces.length > 0) {
          set({ currentWorkspace: workspaces[0] })
        }
      },

      setCurrentWorkspace: (workspace: Workspace | null) => {
        set({ currentWorkspace: workspace })
      },

      addWorkspace: (workspace: Workspace) => {
        set((state) => ({
          workspaces: [...state.workspaces, workspace]
        }))
      },

      updateWorkspace: (id: string, updates: Partial<Workspace>) => {
        set((state) => ({
          workspaces: state.workspaces.map((workspace) =>
            workspace.id === id ? { ...workspace, ...updates } : workspace
          ),
          currentWorkspace: 
            state.currentWorkspace?.id === id 
              ? { ...state.currentWorkspace, ...updates }
              : state.currentWorkspace
        }))
      },

      removeWorkspace: (id: string) => {
        set((state) => {
          const filteredWorkspaces = state.workspaces.filter((w) => w.id !== id)
          return {
            workspaces: filteredWorkspaces,
            currentWorkspace: 
              state.currentWorkspace?.id === id 
                ? filteredWorkspaces[0] || null
                : state.currentWorkspace
          }
        })
      },

      setMembers: (members: WorkspaceMember[]) => {
        set({ members })
      },

      addMember: (member: WorkspaceMember) => {
        set((state) => ({
          members: [...state.members, member]
        }))
      },

      updateMember: (id: string, updates: Partial<WorkspaceMember>) => {
        set((state) => ({
          members: state.members.map((member) =>
            member.id === id ? { ...member, ...updates } : member
          )
        }))
      },

      removeMember: (id: string) => {
        set((state) => ({
          members: state.members.filter((member) => member.id !== id)
        }))
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      switchWorkspace: (workspaceId: string) => {
        const { workspaces } = get()
        const workspace = workspaces.find((w) => w.id === workspaceId)
        if (workspace) {
          set({ currentWorkspace: workspace })
        }
      },
    }),
    {
      name: 'workspace-storage',
      partialize: (state) => ({
        workspaces: state.workspaces,
        currentWorkspace: state.currentWorkspace,
      }),
    }
  )
)