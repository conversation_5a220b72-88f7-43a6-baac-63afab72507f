import { Test, TestingModule } from '@nestjs/testing';
import { SyncSchedulerService } from '../sync-scheduler.service';
import { DatabaseService } from '../../../database/database.service';
import { SyncEngineService } from '../sync-engine.service';
import { DEFAULT_SYNC_INTERVALS, SyncJobPriority } from '../../constants/sync.constants';
import { IntegrationStatus } from '../../../integrations/types';
import { SyncScheduleConfig } from '../../interfaces/sync-job.interface';

describe('SyncSchedulerService', () => {
  let service: SyncSchedulerService;
  let mockPrisma: any;
  let mockSyncEngine: any;

  const mockIntegrationId = 'integration-123';
  const mockWorkspaceId = 'workspace-123';

  beforeEach(async () => {
    mockPrisma = {
      integration: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
        update: jest.fn(),
      },
    };

    mockSyncEngine = {
      scheduleIncrementalSync: jest.fn(),
      scheduleFullSync: jest.fn(),
      getActiveJobs: jest.fn(),
      cancelJob: jest.fn(),
      pauseIntegrationSync: jest.fn(),
      resumeIntegrationSync: jest.fn(),
      getSyncStats: jest.fn(),
      cleanupOldData: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SyncSchedulerService,
        {
          provide: DatabaseService,
          useValue: mockPrisma,
        },
        {
          provide: SyncEngineService,
          useValue: mockSyncEngine,
        },
      ],
    }).compile();

    service = module.get<SyncSchedulerService>(SyncSchedulerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('onModuleInit', () => {
    it('should load schedule configurations on initialization', async () => {
      const mockIntegrations = [
        {
          id: mockIntegrationId,
          status: IntegrationStatus.ACTIVE,
          config: {
            fullSyncInterval: 120,
            incrementalSyncInterval: 30,
          },
        },
      ];

      mockPrisma.integration.findMany.mockResolvedValue(mockIntegrations);

      await service.onModuleInit();

      expect(mockPrisma.integration.findMany).toHaveBeenCalledWith({
        where: {
          status: IntegrationStatus.ACTIVE,
        },
      });

      const config = service.getScheduleConfig(mockIntegrationId);
      expect(config).toEqual({
        integrationId: mockIntegrationId,
        enabled: true,
        fullSyncInterval: 120,
        incrementalSyncInterval: 30,
        timezone: undefined,
        activeHours: undefined,
        excludeDays: undefined,
      });
    });
  });

  describe('runIncrementalSync', () => {
    it('should schedule incremental sync for eligible integrations', async () => {
      const mockIntegration = {
        id: mockIntegrationId,
        workspaceId: mockWorkspaceId,
        status: IntegrationStatus.ACTIVE,
        lastSyncAt: new Date(Date.now() - 20 * 60 * 1000), // 20 minutes ago
      };

      // Set up schedule config
      const scheduleConfig: SyncScheduleConfig = {
        integrationId: mockIntegrationId,
        enabled: true,
        fullSyncInterval: DEFAULT_SYNC_INTERVALS.FULL_SYNC,
        incrementalSyncInterval: 15, // 15 minutes
      };

      service['scheduleConfigs'].set(mockIntegrationId, scheduleConfig);
      mockPrisma.integration.findUnique.mockResolvedValue(mockIntegration);
      mockSyncEngine.scheduleIncrementalSync.mockResolvedValue('job-123');

      await service.runIncrementalSync();

      expect(mockSyncEngine.scheduleIncrementalSync).toHaveBeenCalledWith(
        mockIntegrationId,
        mockWorkspaceId,
        {
          priority: SyncJobPriority.NORMAL,
          lastSyncAt: mockIntegration.lastSyncAt,
        }
      );
    });

    it('should skip sync for integrations that synced recently', async () => {
      const mockIntegration = {
        id: mockIntegrationId,
        workspaceId: mockWorkspaceId,
        status: IntegrationStatus.ACTIVE,
        lastSyncAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
      };

      const scheduleConfig: SyncScheduleConfig = {
        integrationId: mockIntegrationId,
        enabled: true,
        fullSyncInterval: DEFAULT_SYNC_INTERVALS.FULL_SYNC,
        incrementalSyncInterval: 15, // 15 minutes
      };

      service['scheduleConfigs'].set(mockIntegrationId, scheduleConfig);
      mockPrisma.integration.findUnique.mockResolvedValue(mockIntegration);

      await service.runIncrementalSync();

      expect(mockSyncEngine.scheduleIncrementalSync).not.toHaveBeenCalled();
    });

    it('should skip disabled integrations', async () => {
      const scheduleConfig: SyncScheduleConfig = {
        integrationId: mockIntegrationId,
        enabled: false,
        fullSyncInterval: DEFAULT_SYNC_INTERVALS.FULL_SYNC,
        incrementalSyncInterval: DEFAULT_SYNC_INTERVALS.INCREMENTAL_SYNC,
      };

      service['scheduleConfigs'].set(mockIntegrationId, scheduleConfig);

      await service.runIncrementalSync();

      expect(mockPrisma.integration.findUnique).not.toHaveBeenCalled();
      expect(mockSyncEngine.scheduleIncrementalSync).not.toHaveBeenCalled();
    });
  });

  describe('runFullSync', () => {
    it('should schedule full sync for eligible integrations', async () => {
      const mockIntegration = {
        id: mockIntegrationId,
        workspaceId: mockWorkspaceId,
        status: IntegrationStatus.ACTIVE,
        lastSyncAt: new Date(Date.now() - 70 * 60 * 1000), // 70 minutes ago
      };

      const scheduleConfig: SyncScheduleConfig = {
        integrationId: mockIntegrationId,
        enabled: true,
        fullSyncInterval: 60, // 60 minutes
        incrementalSyncInterval: DEFAULT_SYNC_INTERVALS.INCREMENTAL_SYNC,
      };

      service['scheduleConfigs'].set(mockIntegrationId, scheduleConfig);
      mockPrisma.integration.findUnique.mockResolvedValue(mockIntegration);
      mockSyncEngine.scheduleFullSync.mockResolvedValue('job-456');

      await service.runFullSync();

      expect(mockSyncEngine.scheduleFullSync).toHaveBeenCalledWith(
        mockIntegrationId,
        mockWorkspaceId,
        {
          priority: SyncJobPriority.NORMAL,
          syncAllTasks: false,
        }
      );
    });
  });

  describe('runHealthCheck', () => {
    it('should check for stuck jobs and handle them', async () => {
      const mockIntegrations = [
        {
          id: mockIntegrationId,
          status: IntegrationStatus.ACTIVE,
          lastSyncAt: new Date(),
        },
      ];

      const stuckJob = {
        id: 'stuck-job-123',
        integrationId: mockIntegrationId,
        jobType: 'full-sync',
        status: 'active',
        createdAt: new Date(Date.now() - 35 * 60 * 1000), // 35 minutes ago (stuck)
      };

      mockPrisma.integration.findMany.mockResolvedValue(mockIntegrations);
      mockSyncEngine.getActiveJobs.mockResolvedValue([stuckJob]);
      mockSyncEngine.cancelJob.mockResolvedValue(true);
      mockSyncEngine.scheduleIncrementalSync.mockResolvedValue('recovery-job');

      await service.runHealthCheck();

      expect(mockSyncEngine.cancelJob).toHaveBeenCalledWith('stuck-job-123');
      expect(mockSyncEngine.scheduleIncrementalSync).toHaveBeenCalledWith(
        mockIntegrationId,
        mockIntegrations[0].workspaceId,
        {
          priority: SyncJobPriority.HIGH,
          lastSyncAt: mockIntegrations[0].lastSyncAt,
        }
      );
    });

    it('should update integration statuses based on sync results', async () => {
      const mockIntegrations = [
        {
          id: mockIntegrationId,
          status: IntegrationStatus.ACTIVE,
        },
      ];

      const mockStats = {
        totalJobs: 10,
        successfulJobs: 2,
        failedJobs: 8, // High failure rate (80%)
      };

      mockPrisma.integration.findMany.mockResolvedValue(mockIntegrations);
      mockSyncEngine.getActiveJobs.mockResolvedValue([]);
      mockSyncEngine.getSyncStats.mockResolvedValue(mockStats);

      await service.runHealthCheck();

      expect(mockPrisma.integration.update).toHaveBeenCalledWith({
        where: { id: mockIntegrationId },
        data: { status: IntegrationStatus.ERROR },
      });
    });
  });

  describe('updateScheduleConfig', () => {
    it('should update schedule configuration', async () => {
      const existingConfig: SyncScheduleConfig = {
        integrationId: mockIntegrationId,
        enabled: true,
        fullSyncInterval: 60,
        incrementalSyncInterval: 15,
      };

      const mockIntegration = {
        id: mockIntegrationId,
        config: {
          fullSyncInterval: 60,
          incrementalSyncInterval: 15,
        },
      };

      service['scheduleConfigs'].set(mockIntegrationId, existingConfig);
      mockPrisma.integration.findUnique.mockResolvedValue(mockIntegration);

      const updates = {
        fullSyncInterval: 120,
        incrementalSyncInterval: 30,
      };

      await service.updateScheduleConfig(mockIntegrationId, updates);

      const updatedConfig = service.getScheduleConfig(mockIntegrationId);
      expect(updatedConfig?.fullSyncInterval).toBe(120);
      expect(updatedConfig?.incrementalSyncInterval).toBe(30);

      expect(mockPrisma.integration.update).toHaveBeenCalledWith({
        where: { id: mockIntegrationId },
        data: {
          config: expect.objectContaining({
            fullSyncInterval: 120,
            incrementalSyncInterval: 30,
          }),
        },
      });
    });
  });

  describe('toggleSync', () => {
    it('should enable sync for integration', async () => {
      const config: SyncScheduleConfig = {
        integrationId: mockIntegrationId,
        enabled: false,
        fullSyncInterval: 60,
        incrementalSyncInterval: 15,
      };

      service['scheduleConfigs'].set(mockIntegrationId, config);

      await service.toggleSync(mockIntegrationId, true);

      const updatedConfig = service.getScheduleConfig(mockIntegrationId);
      expect(updatedConfig?.enabled).toBe(true);
      expect(mockSyncEngine.resumeIntegrationSync).toHaveBeenCalledWith(mockIntegrationId);
    });

    it('should disable sync for integration', async () => {
      const config: SyncScheduleConfig = {
        integrationId: mockIntegrationId,
        enabled: true,
        fullSyncInterval: 60,
        incrementalSyncInterval: 15,
      };

      service['scheduleConfigs'].set(mockIntegrationId, config);

      await service.toggleSync(mockIntegrationId, false);

      const updatedConfig = service.getScheduleConfig(mockIntegrationId);
      expect(updatedConfig?.enabled).toBe(false);
      expect(mockSyncEngine.pauseIntegrationSync).toHaveBeenCalledWith(mockIntegrationId);
    });
  });

  describe('addIntegration', () => {
    it('should add new integration to scheduler', async () => {
      const mockIntegration = {
        id: mockIntegrationId,
        config: {
          fullSyncInterval: 90,
          incrementalSyncInterval: 20,
        },
      };

      mockPrisma.integration.findUnique.mockResolvedValue(mockIntegration);

      await service.addIntegration(mockIntegrationId);

      const config = service.getScheduleConfig(mockIntegrationId);
      expect(config).toEqual({
        integrationId: mockIntegrationId,
        enabled: true,
        fullSyncInterval: 90,
        incrementalSyncInterval: 20,
        timezone: undefined,
        activeHours: undefined,
        excludeDays: undefined,
      });
    });
  });

  describe('removeIntegration', () => {
    it('should remove integration from scheduler', async () => {
      const config: SyncScheduleConfig = {
        integrationId: mockIntegrationId,
        enabled: true,
        fullSyncInterval: 60,
        incrementalSyncInterval: 15,
      };

      service['scheduleConfigs'].set(mockIntegrationId, config);

      await service.removeIntegration(mockIntegrationId);

      const removedConfig = service.getScheduleConfig(mockIntegrationId);
      expect(removedConfig).toBeUndefined();
      expect(mockSyncEngine.pauseIntegrationSync).toHaveBeenCalledWith(mockIntegrationId);
    });
  });
});