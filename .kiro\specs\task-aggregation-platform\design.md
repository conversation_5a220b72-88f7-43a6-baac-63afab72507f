# Design Document

## Overview

TaskUnify is designed as a modern, scalable SaaS platform that aggregates tasks from multiple project management tools into a unified workspace. The architecture follows microservices principles with clear separation between the web application, background processing, and data persistence layers.

The system is built on a proven tech stack: NestJS backend with TypeScript, React frontend with modern UI components, PostgreSQL for primary data storage, Redis for caching and job queues, and Docker for containerized deployment. This foundation supports the core requirements of multi-tool integration, real-time synchronization, intelligent prioritization, and collaborative workspace management.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[React Frontend]
        MOBILE[Mobile PWA]
    end
    
    subgraph "API Gateway"
        NGINX[Nginx Load Balancer]
    end
    
    subgraph "Application Layer"
        API[NestJS API Server]
        WORKER[Background Workers]
        WEBHOOK[Webhook Handler]
    end
    
    subgraph "Data Layer"
        PG[(PostgreSQL)]
        REDIS[(Redis)]
        S3[(File Storage)]
    end
    
    subgraph "External Services"
        ASANA[Asana API]
        TRELLO[Trello API]
        JIRA[Jira API]
        SHEETS[Google Sheets API]
        OAUTH[OAuth Providers]
    end
    
    WEB --> NGINX
    MOBILE --> NGINX
    NGINX --> API
    API --> PG
    API --> REDIS
    API --> S3
    WORKER --> PG
    WORKER --> REDIS
    WORKER --> ASANA
    WORKER --> TRELLO
    WORKER --> JIRA
    WORKER --> SHEETS
    WEBHOOK --> API
    API --> OAUTH
```

### System Components

**Frontend Application (React + TypeScript)**
- Single-page application with modern UI components using Radix UI
- State management with Zustand for global state and React Query for server state
- Real-time updates via WebSocket connections
- Responsive design with Tailwind CSS
- Progressive Web App capabilities for mobile experience

**API Server (NestJS + TypeScript)**
- RESTful API with OpenAPI documentation
- JWT-based authentication with OAuth2 integration
- Rate limiting and request throttling
- Input validation and sanitization
- WebSocket gateway for real-time updates
- Health checks and monitoring endpoints

**Background Workers (Bull Queue + Redis)**
- Separate worker processes for heavy operations
- Task synchronization jobs with retry logic
- Webhook processing and event handling
- Scheduled jobs for periodic sync operations
- Priority queue management for time-sensitive operations

**Database Layer (PostgreSQL + Prisma)**
- Primary data storage with ACID compliance
- Database migrations and schema versioning
- Connection pooling and query optimization
- Full-text search capabilities
- Audit logging for data changes

**Caching Layer (Redis)**
- Session storage and user authentication
- API response caching
- Job queue management
- Rate limiting counters
- Real-time event broadcasting

## Components and Interfaces

### Core Domain Models

**User Management**
```typescript
interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
  workspaces: WorkspaceMember[];
}

interface Workspace {
  id: string;
  name: string;
  slug: string;
  ownerId: string;
  settings: WorkspaceSettings;
  members: WorkspaceMember[];
  integrations: Integration[];
  createdAt: Date;
  updatedAt: Date;
}

interface WorkspaceMember {
  id: string;
  userId: string;
  workspaceId: string;
  role: 'OWNER' | 'ADMIN' | 'MEMBER';
  permissions: string[];
  joinedAt: Date;
}
```

**Integration Management**
```typescript
interface Integration {
  id: string;
  workspaceId: string;
  provider: IntegrationProvider;
  name: string;
  config: IntegrationConfig;
  credentials: EncryptedCredentials;
  status: 'ACTIVE' | 'ERROR' | 'DISABLED';
  lastSyncAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface IntegrationConfig {
  syncInterval: number; // minutes
  enableTwoWaySync: boolean;
  fieldMappings: FieldMapping[];
  filters: SyncFilter[];
}

enum IntegrationProvider {
  ASANA = 'asana',
  TRELLO = 'trello',
  JIRA = 'jira',
  CLICKUP = 'clickup',
  MONDAY = 'monday',
  TODOIST = 'todoist',
  NOTION = 'notion',
  GOOGLE_SHEETS = 'google_sheets'
}
```

**Task Management**
```typescript
interface Task {
  id: string;
  workspaceId: string;
  integrationId: string;
  externalId: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  priorityScore: number;
  assigneeId?: string;
  assigneeName?: string;
  dueDate?: Date;
  estimatedMinutes?: number;
  tags: string[];
  projectName?: string;
  sourceUrl: string;
  metadata: Record<string, any>;
  syncStatus: SyncStatus;
  createdAt: Date;
  updatedAt: Date;
  lastSyncAt: Date;
}

enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  DONE = 'done',
  CANCELLED = 'cancelled'
}

enum SyncStatus {
  SYNCED = 'synced',
  PENDING = 'pending',
  ERROR = 'error',
  CONFLICT = 'conflict'
}
```

### API Interfaces

**Authentication & Authorization**
```typescript
// OAuth flow endpoints
POST /auth/oauth/:provider/authorize
POST /auth/oauth/:provider/callback
POST /auth/refresh
POST /auth/logout

// User management
GET /users/me
PATCH /users/me
DELETE /users/me
```

**Workspace Management**
```typescript
// Workspace CRUD
GET /workspaces
POST /workspaces
GET /workspaces/:id
PATCH /workspaces/:id
DELETE /workspaces/:id

// Member management
GET /workspaces/:id/members
POST /workspaces/:id/members
PATCH /workspaces/:id/members/:userId
DELETE /workspaces/:id/members/:userId
```

**Integration Management**
```typescript
// Integration lifecycle
GET /workspaces/:id/integrations
POST /workspaces/:id/integrations
GET /workspaces/:id/integrations/:integrationId
PATCH /workspaces/:id/integrations/:integrationId
DELETE /workspaces/:id/integrations/:integrationId

// Sync operations
POST /workspaces/:id/integrations/:integrationId/sync
GET /workspaces/:id/integrations/:integrationId/sync-status
```

**Task Management**
```typescript
// Task operations
GET /workspaces/:id/tasks
POST /workspaces/:id/tasks
GET /workspaces/:id/tasks/:taskId
PATCH /workspaces/:id/tasks/:taskId
DELETE /workspaces/:id/tasks/:taskId

// Bulk operations
PATCH /workspaces/:id/tasks/bulk
POST /workspaces/:id/tasks/daily-plan

// Search and filtering
GET /workspaces/:id/tasks/search?q=:query
GET /workspaces/:id/tasks?filter=:filter&sort=:sort
```

### Integration Adapters

Each integration provider has a dedicated adapter implementing a common interface:

```typescript
interface IntegrationAdapter {
  authenticate(credentials: OAuthCredentials): Promise<AuthResult>;
  fetchTasks(config: IntegrationConfig): Promise<ExternalTask[]>;
  updateTask(taskId: string, updates: TaskUpdate): Promise<ExternalTask>;
  createTask(task: CreateTaskRequest): Promise<ExternalTask>;
  setupWebhook?(webhookUrl: string): Promise<WebhookConfig>;
  validateCredentials(credentials: any): Promise<boolean>;
}

// Provider-specific implementations
class AsanaAdapter implements IntegrationAdapter { ... }
class TrelloAdapter implements IntegrationAdapter { ... }
class JiraAdapter implements IntegrationAdapter { ... }
class GoogleSheetsAdapter implements IntegrationAdapter { ... }
```

## Data Models

### Database Schema

**Core Tables**
```sql
-- Users and authentication
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  avatar_url TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Workspaces
CREATE TABLE workspaces (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Workspace membership
CREATE TABLE workspace_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  role VARCHAR(20) NOT NULL DEFAULT 'MEMBER',
  permissions TEXT[] DEFAULT '{}',
  joined_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, workspace_id)
);

-- Integrations
CREATE TABLE integrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  provider VARCHAR(50) NOT NULL,
  name VARCHAR(255) NOT NULL,
  config JSONB DEFAULT '{}',
  encrypted_credentials TEXT NOT NULL,
  status VARCHAR(20) DEFAULT 'ACTIVE',
  last_sync_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Tasks
CREATE TABLE tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  integration_id UUID REFERENCES integrations(id) ON DELETE CASCADE,
  external_id VARCHAR(255) NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  status VARCHAR(20) NOT NULL DEFAULT 'todo',
  priority VARCHAR(20) DEFAULT 'medium',
  priority_score DECIMAL(5,2) DEFAULT 0,
  assignee_id VARCHAR(255),
  assignee_name VARCHAR(255),
  due_date TIMESTAMP,
  estimated_minutes INTEGER,
  tags TEXT[] DEFAULT '{}',
  project_name VARCHAR(255),
  source_url TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  sync_status VARCHAR(20) DEFAULT 'synced',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  last_sync_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(integration_id, external_id)
);

-- Sync logs and audit trail
CREATE TABLE sync_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  integration_id UUID REFERENCES integrations(id) ON DELETE CASCADE,
  operation VARCHAR(50) NOT NULL,
  status VARCHAR(20) NOT NULL,
  tasks_processed INTEGER DEFAULT 0,
  errors JSONB DEFAULT '[]',
  started_at TIMESTAMP NOT NULL,
  completed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

**Indexes for Performance**
```sql
-- Task queries
CREATE INDEX idx_tasks_workspace_status ON tasks(workspace_id, status);
CREATE INDEX idx_tasks_workspace_priority ON tasks(workspace_id, priority_score DESC);
CREATE INDEX idx_tasks_workspace_due_date ON tasks(workspace_id, due_date);
CREATE INDEX idx_tasks_assignee ON tasks(workspace_id, assignee_id);
CREATE INDEX idx_tasks_updated_at ON tasks(updated_at DESC);

-- Full-text search
CREATE INDEX idx_tasks_search ON tasks USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));

-- Integration queries
CREATE INDEX idx_integrations_workspace ON integrations(workspace_id, status);
CREATE INDEX idx_sync_logs_integration ON sync_logs(integration_id, created_at DESC);
```

### Prioritization Algorithm

The priority scoring system combines multiple factors with configurable weights:

```typescript
interface PriorityWeights {
  dueDateProximity: number;    // 0.0 - 1.0
  effortEstimate: number;      // 0.0 - 1.0
  businessImpact: number;      // 0.0 - 1.0
  contextSwitching: number;    // 0.0 - 1.0
}

function calculatePriorityScore(task: Task, weights: PriorityWeights): number {
  const dueDateScore = calculateDueDateScore(task.dueDate);
  const effortScore = calculateEffortScore(task.estimatedMinutes);
  const impactScore = calculateImpactScore(task.priority, task.tags);
  const contextScore = calculateContextScore(task.projectName, task.assigneeId);
  
  return (
    dueDateScore * weights.dueDateProximity +
    effortScore * weights.effortEstimate +
    impactScore * weights.businessImpact +
    contextScore * weights.contextSwitching
  ) * 100; // Scale to 0-100
}
```

## Error Handling

### API Error Responses

Standardized error format across all endpoints:

```typescript
interface ApiError {
  error: {
    code: string;
    message: string;
    details?: Record<string, any>;
    timestamp: string;
    requestId: string;
  };
}

// Common error codes
enum ErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  INTEGRATION_ERROR = 'INTEGRATION_ERROR',
  SYNC_CONFLICT = 'SYNC_CONFLICT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR'
}
```

### Integration Error Handling

```typescript
interface SyncError {
  type: 'AUTH_ERROR' | 'API_ERROR' | 'NETWORK_ERROR' | 'VALIDATION_ERROR';
  message: string;
  retryable: boolean;
  retryAfter?: number;
  details?: Record<string, any>;
}

class IntegrationErrorHandler {
  async handleSyncError(error: SyncError, integration: Integration): Promise<void> {
    // Log error with context
    await this.logError(error, integration);
    
    // Update integration status if needed
    if (!error.retryable) {
      await this.disableIntegration(integration.id);
      await this.notifyUser(integration.workspaceId, error);
    }
    
    // Schedule retry for retryable errors
    if (error.retryable) {
      await this.scheduleRetry(integration.id, error.retryAfter);
    }
  }
}
```

### Sync Conflict Resolution

```typescript
interface SyncConflict {
  taskId: string;
  field: string;
  localValue: any;
  remoteValue: any;
  lastSyncAt: Date;
  conflictedAt: Date;
}

class ConflictResolver {
  async resolveConflict(conflict: SyncConflict, strategy: 'LOCAL_WINS' | 'REMOTE_WINS' | 'MANUAL'): Promise<void> {
    switch (strategy) {
      case 'LOCAL_WINS':
        await this.pushToRemote(conflict);
        break;
      case 'REMOTE_WINS':
        await this.pullFromRemote(conflict);
        break;
      case 'MANUAL':
        await this.flagForManualResolution(conflict);
        break;
    }
  }
}
```

## Testing Strategy

### Unit Testing

**Backend Testing (Jest + Supertest)**
- Service layer unit tests with mocked dependencies
- Repository pattern testing with in-memory database
- Integration adapter testing with mocked external APIs
- Utility function testing for prioritization algorithms

```typescript
describe('TaskService', () => {
  let service: TaskService;
  let repository: MockTaskRepository;
  
  beforeEach(() => {
    repository = new MockTaskRepository();
    service = new TaskService(repository);
  });
  
  it('should calculate priority score correctly', () => {
    const task = createMockTask({ dueDate: addDays(new Date(), 1) });
    const score = service.calculatePriorityScore(task);
    expect(score).toBeGreaterThan(50);
  });
});
```

**Frontend Testing (Vitest + React Testing Library)**
- Component unit tests with mocked API calls
- Custom hook testing for state management
- Form validation and user interaction testing
- Accessibility testing with axe-core

```typescript
describe('TaskList Component', () => {
  it('should render tasks with correct priority order', async () => {
    const mockTasks = [
      createMockTask({ priorityScore: 85 }),
      createMockTask({ priorityScore: 92 })
    ];
    
    render(<TaskList tasks={mockTasks} />);
    
    const taskElements = screen.getAllByTestId('task-item');
    expect(taskElements[0]).toHaveTextContent(mockTasks[1].title);
  });
});
```

### Integration Testing

**API Integration Tests**
- End-to-end API workflow testing
- Database integration with test containers
- OAuth flow testing with mock providers
- Webhook handling and processing

**External Service Integration**
- Integration adapter testing with sandbox APIs
- Webhook delivery and processing validation
- Rate limiting and error handling verification
- Data consistency checks across sync operations

### End-to-End Testing

**User Journey Testing**
- Complete onboarding flow from signup to first sync
- Task management workflows (create, edit, complete)
- Multi-workspace switching and permission validation
- Integration setup and sync verification

**Performance Testing**
- Load testing for concurrent users and sync operations
- Database query performance under load
- Memory usage and garbage collection monitoring
- API response time benchmarking

### Testing Infrastructure

```typescript
// Test database setup
export class TestDatabase {
  static async setup(): Promise<PrismaClient> {
    const client = new PrismaClient({
      datasources: { db: { url: process.env.TEST_DATABASE_URL } }
    });
    
    await client.$executeRaw`TRUNCATE TABLE tasks, integrations, workspaces, users CASCADE`;
    return client;
  }
}

// Mock integration adapters
export class MockAsanaAdapter implements IntegrationAdapter {
  private mockTasks: ExternalTask[] = [];
  
  async fetchTasks(): Promise<ExternalTask[]> {
    return this.mockTasks;
  }
  
  setMockTasks(tasks: ExternalTask[]): void {
    this.mockTasks = tasks;
  }
}
```

This design provides a robust foundation for implementing TaskUnify's core functionality while maintaining scalability, security, and maintainability. The modular architecture allows for incremental development and easy extension of integration capabilities.