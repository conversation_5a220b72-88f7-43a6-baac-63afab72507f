import { IsArray, <PERSON><PERSON>ptional, IsString, IsDateString, ArrayMinSize, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class BulkTaskUpdate {
  @ApiProperty({ 
    description: 'Task status', 
    example: 'in_progress',
    required: false 
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({ 
    description: 'Assignee ID', 
    example: 'user-123',
    required: false 
  })
  @IsOptional()
  @IsString()
  assigneeId?: string;

  @ApiProperty({ 
    description: 'Assignee name', 
    example: '<PERSON>',
    required: false 
  })
  @IsOptional()
  @IsString()
  assigneeName?: string;

  @ApiProperty({ 
    description: 'Due date (ISO string)', 
    example: '2024-12-31T23:59:59Z',
    required: false 
  })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiProperty({ 
    description: 'Priority level', 
    example: 'high',
    required: false 
  })
  @IsOptional()
  @IsString()
  priority?: string;

  @ApiProperty({ 
    description: 'Tags array', 
    example: ['urgent', 'bug'],
    required: false 
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

export class BulkUpdateTasksDto {
  @ApiProperty({ 
    description: 'Array of task IDs to update', 
    example: ['task-1', 'task-2', 'task-3'],
    type: [String]
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  taskIds: string[];

  @ApiProperty({ 
    description: 'Updates to apply to all selected tasks',
    type: BulkTaskUpdate
  })
  @ValidateNested()
  @Type(() => BulkTaskUpdate)
  updates: BulkTaskUpdate;
}