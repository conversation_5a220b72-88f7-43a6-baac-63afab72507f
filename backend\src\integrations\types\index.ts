/**
 * Integration provider enumeration
 */
export enum IntegrationProvider {
  ASANA = 'asana',
  TRELLO = 'trello',
  JIRA = 'jira',
  CLICKUP = 'clickup',
  MONDAY = 'monday',
  TODOIST = 'todoist',
  NOTION = 'notion',
  GOOGLE_SHEETS = 'google_sheets',
}

/**
 * Integration configuration interface
 */
export interface IntegrationConfig {
  syncInterval: number; // minutes
  enableTwoWaySync: boolean;
  fieldMappings: FieldMapping[];
  filters: SyncFilter[];
  webhookEnabled?: boolean;
  customSettings?: Record<string, any>;
}

/**
 * Field mapping configuration for task synchronization
 */
export interface FieldMapping {
  localField: string;
  externalField: string;
  transform?: 'uppercase' | 'lowercase' | 'date' | 'number' | 'boolean';
  defaultValue?: any;
}

/**
 * Sync filter configuration
 */
export interface SyncFilter {
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'in' | 'notIn';
  value: any;
}

/**
 * OAuth credentials interface
 */
export interface OAuthCredentials {
  accessToken: string;
  refreshToken?: string;
  expiresAt?: Date;
  scope?: string[];
  tokenType?: string;
  userId?: string;
  userEmail?: string;
}

/**
 * Authentication result interface
 */
export interface AuthResult {
  success: boolean;
  user?: {
    id: string;
    email: string;
    name: string;
    avatar?: string;
  };
  error?: string;
  credentials?: OAuthCredentials;
}

/**
 * External task representation
 */
export interface ExternalTask {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority?: TaskPriority;
  assigneeId?: string;
  assigneeName?: string;
  dueDate?: Date;
  estimatedMinutes?: number;
  tags?: string[];
  projectName?: string;
  sourceUrl: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Task status enumeration
 */
export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  DONE = 'done',
  CANCELLED = 'cancelled',
}

/**
 * Task priority enumeration
 */
export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

/**
 * Task update interface
 */
export interface TaskUpdate {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  assigneeId?: string;
  dueDate?: Date;
  estimatedMinutes?: number;
  tags?: string[];
}

/**
 * Task creation request interface
 */
export interface CreateTaskRequest {
  title: string;
  description?: string;
  priority?: TaskPriority;
  assigneeId?: string;
  dueDate?: Date;
  estimatedMinutes?: number;
  tags?: string[];
  projectId?: string;
  metadata?: Record<string, any>;
}

/**
 * Webhook configuration interface
 */
export interface WebhookConfig {
  id: string;
  url: string;
  events: string[];
  secret?: string;
  active: boolean;
}

/**
 * Sync status enumeration
 */
export enum SyncStatus {
  SYNCED = 'synced',
  PENDING = 'pending',
  ERROR = 'error',
  CONFLICT = 'conflict',
}

/**
 * Integration status enumeration
 */
export enum IntegrationStatus {
  ACTIVE = 'ACTIVE',
  ERROR = 'ERROR',
  DISABLED = 'DISABLED',
  EXPIRED = 'EXPIRED',
}

/**
 * Sync error interface
 */
export interface SyncError {
  type: 'AUTH_ERROR' | 'API_ERROR' | 'NETWORK_ERROR' | 'VALIDATION_ERROR' | 'RATE_LIMIT_ERROR';
  message: string;
  retryable: boolean;
  retryAfter?: number;
  details?: Record<string, any>;
  timestamp: Date;
}

/**
 * Sync conflict interface
 */
export interface SyncConflict {
  taskId: string;
  field: string;
  localValue: any;
  remoteValue: any;
  lastSyncAt: Date;
  conflictedAt: Date;
}

/**
 * Sync operation result interface
 */
export interface SyncResult {
  success: boolean;
  tasksProcessed: number;
  tasksCreated: number;
  tasksUpdated: number;
  tasksDeleted: number;
  errors: SyncError[];
  conflicts: SyncConflict[];
  duration: number; // milliseconds
}