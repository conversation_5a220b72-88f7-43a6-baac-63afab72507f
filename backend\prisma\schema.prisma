// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email     String   @unique @db.VarChar(255)
  name      String   @db.VarChar(255)
  password  String?  @db.Text // Optional for OAuth users
  avatarUrl String?  @map("avatar_url") @db.Text
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)

  // Relations
  ownedWorkspaces      Workspace[]       @relation("WorkspaceOwner")
  workspaceMemberships WorkspaceMember[]
  dailyPlans           DailyPlan[]

  @@map("users")
}

model Workspace {
  id       String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name     String @db.VarChar(255)
  slug     String @unique @db.VarChar(100)
  ownerId  String @map("owner_id") @db.Uuid
  settings Json   @default("{}") @db.JsonB
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)

  // Relations
  owner        User              @relation("WorkspaceOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  members      WorkspaceMember[]
  integrations Integration[]
  tasks        Task[]
  dailyPlans   DailyPlan[]

  @@map("workspaces")
}

model WorkspaceMember {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId      String   @map("user_id") @db.Uuid
  workspaceId String   @map("workspace_id") @db.Uuid
  role        String   @default("MEMBER") @db.VarChar(20)
  permissions String[] @default([]) @db.Text
  joinedAt    DateTime @default(now()) @map("joined_at") @db.Timestamp(6)

  // Relations
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspace Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@unique([userId, workspaceId])
  @@map("workspace_members")
}

model Integration {
  id                   String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  workspaceId          String    @map("workspace_id") @db.Uuid
  provider             String    @db.VarChar(50)
  name                 String    @db.VarChar(255)
  config               Json      @default("{}") @db.JsonB
  encryptedCredentials String    @map("encrypted_credentials") @db.Text
  status               String    @default("ACTIVE") @db.VarChar(20)
  lastSyncAt           DateTime? @map("last_sync_at") @db.Timestamp(6)
  createdAt            DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt            DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)

  // Relations
  workspace Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  tasks     Task[]
  syncLogs  SyncLog[]

  @@map("integrations")
}

model Task {
  id               String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  workspaceId      String    @map("workspace_id") @db.Uuid
  integrationId    String    @map("integration_id") @db.Uuid
  externalId       String    @map("external_id") @db.VarChar(255)
  title            String    @db.Text
  description      String?   @db.Text
  status           String    @default("todo") @db.VarChar(20)
  priority         String?   @default("medium") @db.VarChar(20)
  priorityScore    Decimal   @default(0) @map("priority_score") @db.Decimal(5, 2)
  assigneeId       String?   @map("assignee_id") @db.VarChar(255)
  assigneeName     String?   @map("assignee_name") @db.VarChar(255)
  dueDate          DateTime? @map("due_date") @db.Timestamp(6)
  estimatedMinutes Int?      @map("estimated_minutes")
  tags             String[]  @default([]) @db.Text
  projectName      String?   @map("project_name") @db.VarChar(255)
  sourceUrl        String    @map("source_url") @db.Text
  metadata         Json      @default("{}") @db.JsonB
  syncStatus       String    @default("synced") @map("sync_status") @db.VarChar(20)
  createdAt        DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt        DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  lastSyncAt       DateTime  @default(now()) @map("last_sync_at") @db.Timestamp(6)

  // Relations
  workspace       Workspace       @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  integration     Integration     @relation(fields: [integrationId], references: [id], onDelete: Cascade)
  dailyPlanTasks  DailyPlanTask[]

  @@unique([integrationId, externalId])
  @@index([workspaceId, status], name: "idx_tasks_workspace_status")
  @@index([workspaceId, priorityScore(sort: Desc)], name: "idx_tasks_workspace_priority")
  @@index([workspaceId, dueDate], name: "idx_tasks_workspace_due_date")
  @@index([workspaceId, assigneeId], name: "idx_tasks_assignee")
  @@index([updatedAt(sort: Desc)], name: "idx_tasks_updated_at")
  @@map("tasks")
}

model SyncLog {
  id              String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  integrationId   String    @map("integration_id") @db.Uuid
  operation       String    @db.VarChar(50)
  status          String    @db.VarChar(20)
  tasksProcessed  Int       @default(0) @map("tasks_processed")
  errors          Json      @default("[]") @db.JsonB
  startedAt       DateTime  @map("started_at") @db.Timestamp(6)
  completedAt     DateTime? @map("completed_at") @db.Timestamp(6)
  createdAt       DateTime  @default(now()) @map("created_at") @db.Timestamp(6)

  // Relations
  integration Integration @relation(fields: [integrationId], references: [id], onDelete: Cascade)

  @@index([integrationId, createdAt(sort: Desc)], name: "idx_sync_logs_integration")
  @@map("sync_logs")
}

model DailyPlan {
  id                     String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  workspaceId            String    @map("workspace_id") @db.Uuid
  userId                 String    @map("user_id") @db.Uuid
  planDate               DateTime  @map("plan_date") @db.Date
  totalEstimatedMinutes  Int       @default(0) @map("total_estimated_minutes")
  totalCompletedMinutes  Int       @default(0) @map("total_completed_minutes")
  createdAt              DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt              DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)

  // Relations
  workspace       Workspace       @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  dailyPlanTasks  DailyPlanTask[]

  @@unique([workspaceId, userId, planDate])
  @@index([workspaceId, userId, planDate], name: "idx_daily_plans_workspace_user_date")
  @@map("daily_plans")
}

model DailyPlanTask {
  id               String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  dailyPlanId      String    @map("daily_plan_id") @db.Uuid
  taskId           String    @map("task_id") @db.Uuid
  estimatedMinutes Int       @default(0) @map("estimated_minutes")
  actualMinutes    Int?      @map("actual_minutes")
  orderIndex       Int       @default(0) @map("order_index")
  completedAt      DateTime? @map("completed_at") @db.Timestamp(6)
  createdAt        DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt        DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)

  // Relations
  dailyPlan DailyPlan @relation(fields: [dailyPlanId], references: [id], onDelete: Cascade)
  task      Task      @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@unique([dailyPlanId, taskId])
  @@index([dailyPlanId, orderIndex], name: "idx_daily_plan_tasks_plan_order")
  @@map("daily_plan_tasks")
}