/**
 * Example usage of Google Sheets Integration Adapter
 * 
 * This example demonstrates how to use the GoogleSheetsAdapter
 * to read and write tasks from Google Sheets.
 * 
 * Note: This is for demonstration purposes only.
 * In production, use the adapter through the IntegrationService.
 */

import { GoogleSheetsAdapter } from '../google-sheets.adapter';
import {
  IntegrationConfig,
  OAuthCredentials,
  CreateTaskRequest,
  TaskUpdate,
  TaskPriority,
  TaskStatus,
} from '../../types';

// Example configuration
const exampleConfig: IntegrationConfig = {
  syncInterval: 15,
  enableTwoWaySync: true,
  fieldMappings: [
    { localField: 'title', externalField: 'Task Name' },
    { localField: 'description', externalField: 'Description' },
    { localField: 'status', externalField: 'Status' },
    { localField: 'priority', externalField: 'Priority' },
    { localField: 'assignee', externalField: 'Assigned To' },
    { localField: 'dueDate', externalField: 'Due Date' },
    { localField: 'estimatedMinutes', externalField: 'Estimate (min)' },
    { localField: 'tags', externalField: 'Tags' },
    { localField: 'project', externalField: 'Project' },
  ],
  filters: [],
  customSettings: {
    spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms', // Example spreadsheet
    sheetName: 'Tasks',
    credentials: {
      accessToken: 'your-oauth-access-token',
      refreshToken: 'your-oauth-refresh-token',
      expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
      scope: ['https://www.googleapis.com/auth/spreadsheets'],
      tokenType: 'Bearer',
    } as OAuthCredentials,
  },
};

// Example credentials (replace with real OAuth tokens)
const exampleCredentials: OAuthCredentials = {
  accessToken: 'ya29.example-access-token',
  refreshToken: '1//example-refresh-token',
  expiresAt: new Date(Date.now() + 3600000),
  scope: ['https://www.googleapis.com/auth/spreadsheets'],
  tokenType: 'Bearer',
  userId: 'user123',
  userEmail: '<EMAIL>',
};

async function demonstrateGoogleSheetsAdapter() {
  console.log('🚀 Google Sheets Adapter Example');
  console.log('================================');

  const adapter = new GoogleSheetsAdapter();

  try {
    // 1. Test Authentication
    console.log('\n1. Testing Authentication...');
    const authResult = await adapter.authenticate(exampleCredentials);
    
    if (authResult.success) {
      console.log('✅ Authentication successful');
      console.log(`   User: ${authResult.user?.name} (${authResult.user?.email})`);
    } else {
      console.log('❌ Authentication failed:', authResult.error);
      return;
    }

    // 2. Validate Credentials
    console.log('\n2. Validating Credentials...');
    const isValid = await adapter.validateCredentials(exampleCredentials);
    console.log(`   Credentials valid: ${isValid ? '✅' : '❌'}`);

    // 3. Check Adapter Capabilities
    console.log('\n3. Adapter Capabilities...');
    console.log(`   Provider: ${adapter.getProvider()}`);
    console.log(`   Two-way sync: ${adapter.supportsTwoWaySync() ? '✅' : '❌'}`);
    console.log(`   Webhooks: ${adapter.supportsWebhooks() ? '✅' : '❌'}`);

    // 4. Fetch Tasks from Sheet
    console.log('\n4. Fetching Tasks from Sheet...');
    try {
      const tasks = await adapter.fetchTasks(exampleConfig);
      console.log(`   Found ${tasks.length} tasks`);
      
      if (tasks.length > 0) {
        console.log('   Sample task:');
        const sampleTask = tasks[0];
        console.log(`     ID: ${sampleTask.id}`);
        console.log(`     Title: ${sampleTask.title}`);
        console.log(`     Status: ${sampleTask.status}`);
        console.log(`     Priority: ${sampleTask.priority}`);
        console.log(`     Due Date: ${sampleTask.dueDate?.toISOString().split('T')[0] || 'None'}`);
        console.log(`     Source: ${sampleTask.sourceUrl}`);
      }
    } catch (error) {
      console.log('   ⚠️  Could not fetch tasks (expected if using example spreadsheet)');
      console.log(`   Error: ${error.message}`);
    }

    // 5. Create a New Task
    console.log('\n5. Creating a New Task...');
    const newTaskRequest: CreateTaskRequest = {
      title: 'Example Task from API',
      description: 'This task was created using the Google Sheets adapter',
      priority: TaskPriority.HIGH,
      dueDate: new Date('2024-02-15'),
      estimatedMinutes: 120,
      tags: ['api', 'example', 'google-sheets'],
    };

    try {
      const createdTask = await adapter.createTask(newTaskRequest);
      console.log('   ✅ Task created successfully');
      console.log(`     ID: ${createdTask.id}`);
      console.log(`     Title: ${createdTask.title}`);
      console.log(`     Source: ${createdTask.sourceUrl}`);

      // 6. Update the Created Task
      console.log('\n6. Updating the Created Task...');
      const taskUpdate: TaskUpdate = {
        status: TaskStatus.IN_PROGRESS,
        description: 'Updated description - task is now in progress',
      };

      const updatedTask = await adapter.updateTask(createdTask.id, taskUpdate);
      console.log('   ✅ Task updated successfully');
      console.log(`     Status: ${updatedTask.status}`);
      console.log(`     Description: ${updatedTask.description}`);

    } catch (error) {
      console.log('   ⚠️  Could not create/update task (expected without valid credentials)');
      console.log(`   Error: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Example failed:', error.message);
  }

  console.log('\n🎉 Example completed!');
  console.log('\nTo use this adapter in production:');
  console.log('1. Set up Google OAuth2 credentials');
  console.log('2. Configure environment variables');
  console.log('3. Register the adapter in IntegrationsModule');
  console.log('4. Use through IntegrationService');
}

// Example column mapping scenarios
function demonstrateColumnMapping() {
  console.log('\n📊 Column Mapping Examples');
  console.log('==========================');

  const scenarios = [
    {
      name: 'Default Mapping (Auto-detection)',
      headers: ['Title', 'Description', 'Status', 'Priority', 'Due Date'],
      description: 'Uses automatic column detection based on header names',
    },
    {
      name: 'Custom Mapping',
      headers: ['Task Name', 'Details', 'Current State', 'Importance', 'Deadline'],
      description: 'Uses explicit field mappings in configuration',
    },
    {
      name: 'Minimal Mapping',
      headers: ['Task', 'Status'],
      description: 'Only maps essential fields, others remain undefined',
    },
    {
      name: 'Extended Mapping',
      headers: ['Title', 'Description', 'Status', 'Priority', 'Assignee', 'Due Date', 'Estimate', 'Tags', 'Project'],
      description: 'Maps all supported fields for comprehensive task management',
    },
  ];

  scenarios.forEach((scenario, index) => {
    console.log(`\n${index + 1}. ${scenario.name}`);
    console.log(`   Headers: [${scenario.headers.join(', ')}]`);
    console.log(`   Description: ${scenario.description}`);
  });
}

// Example data format handling
function demonstrateDataFormats() {
  console.log('\n📅 Data Format Examples');
  console.log('=======================');

  const examples = {
    dates: [
      '2024-01-15',      // YYYY-MM-DD
      '01/15/2024',      // MM/DD/YYYY
      '01-15-2024',      // MM-DD-YYYY
    ],
    statuses: [
      'todo -> TODO',
      'in progress -> IN_PROGRESS',
      'done -> DONE',
      'cancelled -> CANCELLED',
    ],
    priorities: [
      'low -> LOW',
      'medium -> MEDIUM',
      'high -> HIGH',
      'urgent -> URGENT',
    ],
    tags: [
      'urgent, feature, bug -> ["urgent", "feature", "bug"]',
      'enhancement -> ["enhancement"]',
      '(empty) -> []',
    ],
  };

  Object.entries(examples).forEach(([type, formats]) => {
    console.log(`\n${type.toUpperCase()}:`);
    formats.forEach(format => console.log(`   ${format}`));
  });
}

// Run examples if this file is executed directly
if (require.main === module) {
  demonstrateGoogleSheetsAdapter()
    .then(() => {
      demonstrateColumnMapping();
      demonstrateDataFormats();
    })
    .catch(console.error);
}

export {
  demonstrateGoogleSheetsAdapter,
  demonstrateColumnMapping,
  demonstrateDataFormats,
  exampleConfig,
  exampleCredentials,
};