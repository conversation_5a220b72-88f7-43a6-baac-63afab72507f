import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import App from './App'

// Mock the auth store
vi.mock('@/store/auth', () => ({
  useAuthStore: () => ({
    user: null,
    isLoading: false,
    isAuthenticated: false,
  }),
}))

describe('App', () => {
  it('renders landing page when not authenticated', () => {
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })

    render(
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </QueryClientProvider>
    )

    // Should show landing page for unauthenticated users
    expect(window.location.pathname).toBe('/')
  })
})