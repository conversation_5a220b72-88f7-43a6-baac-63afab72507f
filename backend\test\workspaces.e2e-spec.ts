import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { DatabaseService } from '../src/database/database.service';
import { JwtService } from '@nestjs/jwt';
import { WorkspaceRole } from '../src/workspaces/entities/workspace.entity';

describe('WorkspacesController (e2e)', () => {
  let app: INestApplication;
  let databaseService: DatabaseService;
  let jwtService: JwtService;
  let ownerToken: string;
  let memberToken: string;
  let ownerUser: any;
  let memberUser: any;
  let workspace: any;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    await app.init();

    // Clean up database
    await databaseService.workspaceMember.deleteMany();
    await databaseService.workspace.deleteMany();
    await databaseService.user.deleteMany();

    // Create test users
    ownerUser = await databaseService.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Owner User',
        password: 'hashedPassword',
      },
    });

    memberUser = await databaseService.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Member User',
        password: 'hashedPassword',
      },
    });

    // Generate tokens
    ownerToken = await jwtService.signAsync({
      sub: ownerUser.id,
      email: ownerUser.email,
    });

    memberToken = await jwtService.signAsync({
      sub: memberUser.id,
      email: memberUser.email,
    });
  });

  afterAll(async () => {
    // Clean up
    await databaseService.workspaceMember.deleteMany();
    await databaseService.workspace.deleteMany();
    await databaseService.user.deleteMany();
    await app.close();
  });

  describe('POST /workspaces', () => {
    it('should create a new workspace', async () => {
      const createWorkspaceDto = {
        name: 'Test Workspace',
        slug: 'test-workspace',
        settings: { theme: 'dark' },
      };

      const response = await request(app.getHttpServer())
        .post('/workspaces')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(createWorkspaceDto)
        .expect(201);

      expect(response.body).toMatchObject({
        name: 'Test Workspace',
        slug: 'test-workspace',
        ownerId: ownerUser.id,
        settings: { theme: 'dark' },
      });

      workspace = response.body;
    });

    it('should reject duplicate slug', async () => {
      const createWorkspaceDto = {
        name: 'Another Workspace',
        slug: 'test-workspace', // Same slug
      };

      await request(app.getHttpServer())
        .post('/workspaces')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(createWorkspaceDto)
        .expect(409);
    });

    it('should require authentication', async () => {
      const createWorkspaceDto = {
        name: 'Unauthorized Workspace',
      };

      await request(app.getHttpServer())
        .post('/workspaces')
        .send(createWorkspaceDto)
        .expect(401);
    });
  });

  describe('GET /workspaces', () => {
    it('should return user workspaces', async () => {
      const response = await request(app.getHttpServer())
        .get('/workspaces')
        .set('Authorization', `Bearer ${ownerToken}`)
        .expect(200);

      expect(response.body).toHaveLength(1);
      expect(response.body[0]).toMatchObject({
        name: 'Test Workspace',
        slug: 'test-workspace',
        ownerId: ownerUser.id,
      });
      expect(response.body[0].memberCount).toBe(1);
    });

    it('should return empty array for user with no workspaces', async () => {
      const response = await request(app.getHttpServer())
        .get('/workspaces')
        .set('Authorization', `Bearer ${memberToken}`)
        .expect(200);

      expect(response.body).toHaveLength(0);
    });
  });

  describe('GET /workspaces/:id', () => {
    it('should return workspace details for member', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspace.id}`)
        .set('Authorization', `Bearer ${ownerToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: workspace.id,
        name: 'Test Workspace',
        slug: 'test-workspace',
      });
      expect(response.body.members).toHaveLength(1);
    });

    it('should deny access to non-member', async () => {
      await request(app.getHttpServer())
        .get(`/workspaces/${workspace.id}`)
        .set('Authorization', `Bearer ${memberToken}`)
        .expect(403);
    });

    it('should return 404 for non-existent workspace', async () => {
      await request(app.getHttpServer())
        .get('/workspaces/00000000-0000-0000-0000-000000000000')
        .set('Authorization', `Bearer ${ownerToken}`)
        .expect(404);
    });
  });

  describe('PATCH /workspaces/:id', () => {
    it('should update workspace as owner', async () => {
      const updateDto = {
        name: 'Updated Workspace',
        settings: { theme: 'light' },
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${workspace.id}`)
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(updateDto)
        .expect(200);

      expect(response.body).toMatchObject({
        name: 'Updated Workspace',
        settings: { theme: 'light' },
      });
    });

    it('should deny update to non-admin', async () => {
      // First add member to workspace
      await databaseService.workspaceMember.create({
        data: {
          userId: memberUser.id,
          workspaceId: workspace.id,
          role: WorkspaceRole.MEMBER,
          permissions: [],
        },
      });

      const updateDto = { name: 'Unauthorized Update' };

      await request(app.getHttpServer())
        .patch(`/workspaces/${workspace.id}`)
        .set('Authorization', `Bearer ${memberToken}`)
        .send(updateDto)
        .expect(403);
    });
  });

  describe('POST /workspaces/:id/members', () => {
    it('should invite member as owner', async () => {
      // Create a new user to invite
      const newUser = await databaseService.user.create({
        data: {
          email: '<EMAIL>',
          name: 'New Member',
          password: 'hashedPassword',
        },
      });

      const inviteDto = {
        email: '<EMAIL>',
        role: WorkspaceRole.MEMBER,
      };

      const response = await request(app.getHttpServer())
        .post(`/workspaces/${workspace.id}/members`)
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(inviteDto)
        .expect(201);

      expect(response.body).toMatchObject({
        userId: newUser.id,
        workspaceId: workspace.id,
        role: WorkspaceRole.MEMBER,
      });
    });

    it('should reject invitation for non-existent user', async () => {
      const inviteDto = {
        email: '<EMAIL>',
        role: WorkspaceRole.MEMBER,
      };

      await request(app.getHttpServer())
        .post(`/workspaces/${workspace.id}/members`)
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(inviteDto)
        .expect(404);
    });

    it('should reject duplicate invitation', async () => {
      const inviteDto = {
        email: '<EMAIL>', // Already a member
        role: WorkspaceRole.MEMBER,
      };

      await request(app.getHttpServer())
        .post(`/workspaces/${workspace.id}/members`)
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(inviteDto)
        .expect(409);
    });
  });

  describe('GET /workspaces/:id/members', () => {
    it('should return workspace members', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspace.id}/members`)
        .set('Authorization', `Bearer ${ownerToken}`)
        .expect(200);

      expect(response.body).toHaveLength(3); // Owner + 2 members
      expect(response.body.some((member: any) => member.user.email === '<EMAIL>')).toBe(true);
      expect(response.body.some((member: any) => member.user.email === '<EMAIL>')).toBe(true);
      expect(response.body.some((member: any) => member.user.email === '<EMAIL>')).toBe(true);
    });
  });

  describe('POST /workspaces/:id/leave', () => {
    it('should allow member to leave workspace', async () => {
      await request(app.getHttpServer())
        .post(`/workspaces/${workspace.id}/leave`)
        .set('Authorization', `Bearer ${memberToken}`)
        .expect(204);

      // Verify member is removed
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${workspace.id}/members`)
        .set('Authorization', `Bearer ${ownerToken}`)
        .expect(200);

      expect(response.body).toHaveLength(2); // Owner + 1 remaining member
      expect(response.body.some((member: any) => member.user.email === '<EMAIL>')).toBe(false);
    });

    it('should prevent owner from leaving', async () => {
      await request(app.getHttpServer())
        .post(`/workspaces/${workspace.id}/leave`)
        .set('Authorization', `Bearer ${ownerToken}`)
        .expect(403);
    });
  });

  describe('DELETE /workspaces/:id', () => {
    it('should delete workspace as owner', async () => {
      await request(app.getHttpServer())
        .delete(`/workspaces/${workspace.id}`)
        .set('Authorization', `Bearer ${ownerToken}`)
        .expect(204);

      // Verify workspace is deleted
      await request(app.getHttpServer())
        .get(`/workspaces/${workspace.id}`)
        .set('Authorization', `Bearer ${ownerToken}`)
        .expect(404);
    });
  });
});