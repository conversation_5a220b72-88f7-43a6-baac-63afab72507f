import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { DragDropContext } from '@hello-pangea/dnd'

import { DailyPlanner } from '../DailyPlanner'
import { useWorkspaceStore } from '@/store/workspace'
import { dailyPlanService } from '@/services/daily-plan'
import { taskService } from '@/services/task'

// Mock the stores and services
vi.mock('@/store/workspace')
vi.mock('@/services/daily-plan')
vi.mock('@/services/task')

// Mock drag and drop
vi.mock('react-beautiful-dnd', async () => {
  const actual = await vi.importActual('react-beautiful-dnd')
  return {
    ...actual,
    DragDropContext: ({ children, onDragEnd }: any) => (
      <div data-testid="drag-drop-context" data-on-drag-end={onDragEnd}>
        {children}
      </div>
    ),
    Droppable: ({ children, droppableId }: any) => (
      <div data-testid={`droppable-${droppableId}`}>
        {children({ innerRef: vi.fn(), droppableProps: {}, placeholder: null }, {})}
      </div>
    ),
    Draggable: ({ children, draggableId, index }: any) => (
      <div data-testid={`draggable-${draggableId}`}>
        {children({ innerRef: vi.fn(), draggableProps: {}, dragHandleProps: {} }, {})}
      </div>
    ),
  }
})

const mockWorkspace = {
  id: 'workspace-1',
  name: 'Test Workspace',
  slug: 'test-workspace',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockTasks = [
  {
    id: 'task-1',
    title: 'Test Task 1',
    description: 'Test description',
    status: 'todo' as const,
    priority: 'high' as const,
    priorityScore: 85,
    estimatedMinutes: 60,
    tags: ['urgent'],
    projectName: 'Test Project',
    sourceUrl: 'https://example.com/task-1',
    workspaceId: 'workspace-1',
    integrationId: 'integration-1',
    externalId: 'ext-1',
    syncStatus: 'synced' as const,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    lastSyncAt: '2024-01-01T00:00:00Z',
    metadata: {}
  },
  {
    id: 'task-2',
    title: 'Test Task 2',
    description: 'Another test description',
    status: 'todo' as const,
    priority: 'medium' as const,
    priorityScore: 65,
    estimatedMinutes: 30,
    tags: [],
    projectName: 'Test Project',
    sourceUrl: 'https://example.com/task-2',
    workspaceId: 'workspace-1',
    integrationId: 'integration-1',
    externalId: 'ext-2',
    syncStatus: 'synced' as const,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    lastSyncAt: '2024-01-01T00:00:00Z',
    metadata: {}
  }
]

const mockDailyPlan = {
  id: 'plan-1',
  workspaceId: 'workspace-1',
  userId: 'user-1',
  planDate: new Date('2024-01-15'),
  totalEstimatedMinutes: 90,
  totalCompletedMinutes: 30,
  completionProgress: 33,
  exceedsRecommendedTime: false,
  tasks: [
    {
      id: 'plan-task-1',
      taskId: 'task-1',
      estimatedMinutes: 60,
      actualMinutes: 30,
      orderIndex: 0,
      completedAt: null,
      task: {
        id: 'task-1',
        title: 'Test Task 1',
        description: 'Test description',
        status: 'todo' as const,
        priority: 'high' as const,
        priorityScore: 85,
        dueDate: null,
        tags: ['urgent'],
        projectName: 'Test Project',
        sourceUrl: 'https://example.com/task-1'
      }
    }
  ],
  createdAt: new Date('2024-01-15'),
  updatedAt: new Date('2024-01-15')
}

describe('DailyPlanner', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    // Mock workspace store
    vi.mocked(useWorkspaceStore).mockReturnValue({
      currentWorkspace: mockWorkspace,
      workspaces: [mockWorkspace],
      setCurrentWorkspace: vi.fn(),
      addWorkspace: vi.fn(),
      updateWorkspace: vi.fn(),
      removeWorkspace: vi.fn(),
    })

    // Mock services
    vi.mocked(dailyPlanService.getDailyPlan).mockResolvedValue(mockDailyPlan)
    vi.mocked(taskService.getTasks).mockResolvedValue({
      tasks: mockTasks,
      total: 2,
      count: 2,
      offset: 0,
      limit: 100,
      hasMore: false
    })
  })

  const renderComponent = (selectedDate = new Date('2024-01-15')) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <DailyPlanner selectedDate={selectedDate} />
      </QueryClientProvider>
    )
  }

  it('renders daily planner with correct date', async () => {
    renderComponent()

    expect(screen.getByText('Daily Plan - January 15, 2024')).toBeInTheDocument()
    
    await waitFor(() => {
      expect(screen.getByText('Available Tasks')).toBeInTheDocument()
      expect(screen.getByText("Today's Plan")).toBeInTheDocument()
    })
  })

  it('displays loading state initially', () => {
    vi.mocked(dailyPlanService.getDailyPlan).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    )

    renderComponent()

    expect(screen.getByRole('status')).toBeInTheDocument()
  })

  it('shows available tasks when loaded', async () => {
    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('Test Task 1')).toBeInTheDocument()
      expect(screen.getByText('Test Task 2')).toBeInTheDocument()
    })
  })

  it('displays daily plan tasks', async () => {
    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('Test Task 1')).toBeInTheDocument()
      expect(screen.getByText('1h planned')).toBeInTheDocument()
    })
  })

  it('shows progress tracker when daily plan exists', async () => {
    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('Total Planned')).toBeInTheDocument()
      expect(screen.getByText('Completed')).toBeInTheDocument()
      expect(screen.getByText('Daily Progress')).toBeInTheDocument()
    })
  })

  it('shows time warning when plan exceeds 8 hours', async () => {
    const longPlan = {
      ...mockDailyPlan,
      totalEstimatedMinutes: 500, // Over 8 hours
      exceedsRecommendedTime: true
    }
    vi.mocked(dailyPlanService.getDailyPlan).mockResolvedValue(longPlan)

    renderComponent()

    await waitFor(() => {
      expect(screen.getByText(/exceeds 8 hours/i)).toBeInTheDocument()
      expect(screen.getByText(/consider rescheduling/i)).toBeInTheDocument()
    })
  })

  it('opens task selector when Add Task button is clicked', async () => {
    renderComponent()

    await waitFor(() => {
      const addButton = screen.getByRole('button', { name: /add task/i })
      fireEvent.click(addButton)
    })

    expect(screen.getByText('Add Task to Daily Plan')).toBeInTheDocument()
  })

  it('shows empty state when no tasks are planned', async () => {
    const emptyPlan = {
      ...mockDailyPlan,
      tasks: [],
      totalEstimatedMinutes: 0,
      totalCompletedMinutes: 0,
      completionProgress: 0
    }
    vi.mocked(dailyPlanService.getDailyPlan).mockResolvedValue(emptyPlan)

    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('No tasks planned for today')).toBeInTheDocument()
      expect(screen.getByText(/drag tasks from the left/i)).toBeInTheDocument()
    })
  })

  it('filters available tasks to exclude already planned tasks', async () => {
    renderComponent()

    await waitFor(() => {
      // Task 1 should not appear in available tasks since it's already planned
      const availableSection = screen.getByText('Available Tasks').closest('.space-y-2')
      expect(availableSection).not.toHaveTextContent('Test Task 1')
      
      // Task 2 should appear since it's not planned
      expect(screen.getByText('Test Task 2')).toBeInTheDocument()
    })
  })

  it('calls create daily plan mutation when no plan exists', async () => {
    vi.mocked(dailyPlanService.getDailyPlan).mockResolvedValue(null)
    const createMock = vi.mocked(dailyPlanService.createDailyPlan).mockResolvedValue(mockDailyPlan)

    renderComponent()

    await waitFor(() => {
      const addButton = screen.getByRole('button', { name: /add task/i })
      fireEvent.click(addButton)
    })

    // Simulate adding a task through the task selector
    // This would normally be tested in the TaskSelector component tests
    expect(createMock).not.toHaveBeenCalled() // Until task is actually added
  })

  it('calls update daily plan mutation when plan exists', async () => {
    const updateMock = vi.mocked(dailyPlanService.updateDailyPlan).mockResolvedValue(mockDailyPlan)

    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('Test Task 1')).toBeInTheDocument()
    })

    // This would be triggered by drag and drop or task addition
    expect(updateMock).not.toHaveBeenCalled() // Until actual update occurs
  })

  it('handles service errors gracefully', async () => {
    vi.mocked(dailyPlanService.getDailyPlan).mockRejectedValue(new Error('Service error'))

    renderComponent()

    // Should not crash and should show some error handling
    await waitFor(() => {
      expect(screen.getByText('Available Tasks')).toBeInTheDocument()
    })
  })

  it('formats time correctly in header', async () => {
    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('1.5h planned')).toBeInTheDocument()
    })
  })

  it('uses correct query keys for caching', async () => {
    renderComponent()

    await waitFor(() => {
      expect(vi.mocked(dailyPlanService.getDailyPlan)).toHaveBeenCalledWith(
        'workspace-1',
        '2024-01-15'
      )
    })
  })
})