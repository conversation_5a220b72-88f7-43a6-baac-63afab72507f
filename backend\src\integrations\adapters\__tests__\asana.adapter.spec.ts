import { Test, TestingModule } from '@nestjs/testing';
import axios from 'axios';
import { AsanaAdapter } from '../asana.adapter';
import {
  IntegrationConfig,
  OAuthCredentials,
  TaskStatus,
  TaskPriority,
  TaskUpdate,
  CreateTaskRequest,
} from '../../types';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('AsanaAdapter', () => {
  let adapter: AsanaAdapter;
  let mockAxiosInstance: any;

  const mockCredentials: OAuthCredentials = {
    accessToken: 'test-access-token',
    refreshToken: 'test-refresh-token',
    expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
    userId: 'test-user-id',
    userEmail: '<EMAIL>',
  };

  const mockConfig: IntegrationConfig = {
    syncInterval: 15,
    enableTwoWaySync: true,
    fieldMappings: [],
    filters: [],
    customSettings: {
      credentials: mockCredentials,
      workspaceIds: ['workspace-1'],
      projectIds: ['project-1'],
    },
  };

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock axios.create to return a mock instance
    mockAxiosInstance = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      defaults: {
        headers: {
          common: {},
        },
      },
      interceptors: {
        response: {
          use: jest.fn(),
        },
      },
    };

    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    const module: TestingModule = await Test.createTestingModule({
      providers: [AsanaAdapter],
    }).compile();

    adapter = module.get<AsanaAdapter>(AsanaAdapter);
  });

  describe('authenticate', () => {
    it('should successfully authenticate with valid credentials', async () => {
      const mockUserResponse = {
        data: {
          data: {
            gid: 'user-123',
            name: 'John Doe',
            email: '<EMAIL>',
            photo: {
              image_128x128: 'https://example.com/avatar.jpg',
            },
          },
        },
      };

      mockAxiosInstance.get.mockResolvedValue(mockUserResponse);

      const result = await adapter.authenticate(mockCredentials);

      expect(result.success).toBe(true);
      expect(result.user).toEqual({
        id: 'user-123',
        email: '<EMAIL>',
        name: 'John Doe',
        avatar: 'https://example.com/avatar.jpg',
      });
      expect(result.credentials).toEqual(mockCredentials);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/users/me');
    });

    it('should handle authentication failure', async () => {
      const mockError = {
        response: {
          status: 401,
          statusText: 'Unauthorized',
          data: { message: 'Invalid token' },
        },
      };

      mockAxiosInstance.get.mockRejectedValue(mockError);

      const result = await adapter.authenticate(mockCredentials);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.user).toBeUndefined();
    });

    it('should handle network errors during authentication', async () => {
      const mockError = {
        code: 'ENOTFOUND',
        message: 'Network error',
      };

      mockAxiosInstance.get.mockRejectedValue(mockError);

      const result = await adapter.authenticate(mockCredentials);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('fetchTasks', () => {
    const mockAsanaTask = {
      gid: 'task-123',
      name: 'Test Task',
      notes: 'Task description',
      completed: false,
      completed_at: null,
      due_date: '2024-01-15',
      due_time: null,
      assignee: {
        gid: 'user-456',
        name: 'Jane Smith',
      },
      projects: [
        {
          gid: 'project-1',
          name: 'Test Project',
        },
      ],
      tags: [
        {
          gid: 'tag-1',
          name: 'urgent',
        },
      ],
      permalink_url: 'https://app.asana.com/0/project-1/task-123',
      created_at: '2024-01-01T10:00:00.000Z',
      modified_at: '2024-01-02T10:00:00.000Z',
      custom_fields: [
        {
          gid: 'field-1',
          name: 'Priority',
          type: 'enum',
          enum_value: {
            gid: 'enum-1',
            name: 'High',
          },
        },
      ],
    };

    it('should fetch tasks from specific projects', async () => {
      const mockProjectTasksResponse = {
        data: {
          data: [mockAsanaTask],
          next_page: null,
        },
      };

      mockAxiosInstance.get.mockResolvedValue(mockProjectTasksResponse);

      const tasks = await adapter.fetchTasks(mockConfig);

      expect(tasks).toHaveLength(1);
      expect(tasks[0]).toMatchObject({
        id: 'task-123',
        title: 'Test Task',
        description: 'Task description',
        status: TaskStatus.TODO,
        priority: TaskPriority.HIGH,
        assigneeId: 'user-456',
        assigneeName: 'Jane Smith',
        dueDate: new Date('2024-01-15'),
        tags: ['urgent'],
        projectName: 'Test Project',
        sourceUrl: 'https://app.asana.com/0/project-1/task-123',
      });

      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        '/projects/project-1/tasks',
        expect.objectContaining({
          params: expect.objectContaining({
            opt_fields: expect.stringContaining('gid,name,notes'),
            limit: 100,
          }),
        })
      );
    });

    it('should handle completed tasks correctly', async () => {
      const completedTask = {
        ...mockAsanaTask,
        completed: true,
        completed_at: '2024-01-03T15:30:00.000Z',
      };

      const mockResponse = {
        data: {
          data: [completedTask],
          next_page: null,
        },
      };

      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const tasks = await adapter.fetchTasks(mockConfig);

      expect(tasks[0].status).toBe(TaskStatus.DONE);
      expect(tasks[0].metadata?.completedAt).toBe('2024-01-03T15:30:00.000Z');
    });

    it('should handle pagination correctly', async () => {
      const mockFirstPageResponse = {
        data: {
          data: [mockAsanaTask],
          next_page: {
            offset: 'next-offset',
            path: '/projects/project-1/tasks?offset=next-offset',
            uri: 'https://app.asana.com/api/1.0/projects/project-1/tasks?offset=next-offset',
          },
        },
      };

      const mockSecondPageResponse = {
        data: {
          data: [{ ...mockAsanaTask, gid: 'task-456', name: 'Second Task' }],
          next_page: null,
        },
      };

      mockAxiosInstance.get
        .mockResolvedValueOnce(mockFirstPageResponse)
        .mockResolvedValueOnce(mockSecondPageResponse);

      const tasks = await adapter.fetchTasks(mockConfig);

      expect(tasks).toHaveLength(2);
      expect(tasks[0].id).toBe('task-123');
      expect(tasks[1].id).toBe('task-456');
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(2);
    });

    it('should fetch from all workspaces when no specific projects/workspaces configured', async () => {
      const configWithoutFilters = {
        ...mockConfig,
        customSettings: {
          credentials: mockCredentials,
        },
      };

      const mockWorkspacesResponse = {
        data: {
          data: [
            { gid: 'workspace-1', name: 'Workspace 1' },
            { gid: 'workspace-2', name: 'Workspace 2' },
          ],
        },
      };

      const mockProjectsResponse = {
        data: {
          data: [
            { gid: 'project-1', name: 'Project 1', archived: false },
          ],
        },
      };

      const mockTasksResponse = {
        data: {
          data: [mockAsanaTask],
          next_page: null,
        },
      };

      mockAxiosInstance.get
        .mockResolvedValueOnce(mockWorkspacesResponse) // Get workspaces
        .mockResolvedValueOnce(mockProjectsResponse) // Get projects for workspace-1
        .mockResolvedValueOnce(mockTasksResponse) // Get tasks for project-1
        .mockResolvedValueOnce(mockProjectsResponse) // Get projects for workspace-2
        .mockResolvedValueOnce(mockTasksResponse); // Get tasks for project-1 again

      const tasks = await adapter.fetchTasks(configWithoutFilters);

      expect(tasks).toHaveLength(2); // Same task from both workspaces
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/workspaces');
    });

    it('should apply filters correctly', async () => {
      const configWithFilters = {
        ...mockConfig,
        filters: [
          {
            field: 'status',
            operator: 'equals' as const,
            value: TaskStatus.TODO,
          },
        ],
      };

      const mockResponse = {
        data: {
          data: [
            mockAsanaTask,
            { ...mockAsanaTask, gid: 'task-456', completed: true },
          ],
          next_page: null,
        },
      };

      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const tasks = await adapter.fetchTasks(configWithFilters);

      expect(tasks).toHaveLength(1);
      expect(tasks[0].status).toBe(TaskStatus.TODO);
    });

    it('should handle API errors during task fetching', async () => {
      const mockError = {
        response: {
          status: 500,
          statusText: 'Internal Server Error',
          data: { message: 'Server error' },
        },
      };

      mockAxiosInstance.get.mockRejectedValue(mockError);

      await expect(adapter.fetchTasks(mockConfig)).rejects.toThrow();
    });
  });

  describe('updateTask', () => {
    it('should update task successfully', async () => {
      const taskUpdate: TaskUpdate = {
        title: 'Updated Task Title',
        description: 'Updated description',
        status: TaskStatus.DONE,
        dueDate: new Date('2024-02-01'),
        assigneeId: 'user-789',
      };

      const mockUpdatedTask = {
        ...mockAsanaTask,
        name: 'Updated Task Title',
        notes: 'Updated description',
        completed: true,
        due_date: '2024-02-01',
        assignee: {
          gid: 'user-789',
          name: 'New Assignee',
        },
      };

      const mockResponse = {
        data: {
          data: mockUpdatedTask,
        },
      };

      mockAxiosInstance.put.mockResolvedValue(mockResponse);

      const result = await adapter.updateTask('task-123', taskUpdate);

      expect(result.title).toBe('Updated Task Title');
      expect(result.description).toBe('Updated description');
      expect(result.status).toBe(TaskStatus.DONE);
      expect(result.assigneeId).toBe('user-789');

      expect(mockAxiosInstance.put).toHaveBeenCalledWith('/tasks/task-123', {
        data: {
          name: 'Updated Task Title',
          notes: 'Updated description',
          completed: true,
          due_date: '2024-02-01',
          assignee: 'user-789',
        },
      });
    });

    it('should handle clearing due date', async () => {
      const taskUpdate: TaskUpdate = {
        dueDate: undefined,
      };

      const mockResponse = {
        data: {
          data: { ...mockAsanaTask, due_date: null },
        },
      };

      mockAxiosInstance.put.mockResolvedValue(mockResponse);

      await adapter.updateTask('task-123', taskUpdate);

      expect(mockAxiosInstance.put).toHaveBeenCalledWith('/tasks/task-123', {
        data: {
          due_date: null,
        },
      });
    });

    it('should handle API errors during task update', async () => {
      const mockError = {
        response: {
          status: 404,
          statusText: 'Not Found',
          data: { message: 'Task not found' },
        },
      };

      mockAxiosInstance.put.mockRejectedValue(mockError);

      await expect(adapter.updateTask('task-123', {})).rejects.toThrow();
    });
  });

  describe('createTask', () => {
    it('should create task successfully', async () => {
      const createRequest: CreateTaskRequest = {
        title: 'New Task',
        description: 'New task description',
        priority: TaskPriority.HIGH,
        dueDate: new Date('2024-03-01'),
        assigneeId: 'user-456',
        projectId: 'project-1',
      };

      const mockCreatedTask = {
        gid: 'task-new',
        name: 'New Task',
        notes: 'New task description',
        completed: false,
        due_date: '2024-03-01',
        assignee: {
          gid: 'user-456',
          name: 'Assignee Name',
        },
        projects: [
          {
            gid: 'project-1',
            name: 'Project Name',
          },
        ],
        tags: [],
        permalink_url: 'https://app.asana.com/0/project-1/task-new',
        created_at: '2024-01-01T10:00:00.000Z',
        modified_at: '2024-01-01T10:00:00.000Z',
        custom_fields: [],
      };

      const mockResponse = {
        data: {
          data: mockCreatedTask,
        },
      };

      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await adapter.createTask(createRequest);

      expect(result.id).toBe('task-new');
      expect(result.title).toBe('New Task');
      expect(result.description).toBe('New task description');
      expect(result.assigneeId).toBe('user-456');
      expect(result.projectName).toBe('Project Name');

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/tasks', {
        data: {
          name: 'New Task',
          notes: 'New task description',
          due_date: '2024-03-01',
          assignee: 'user-456',
          projects: ['project-1'],
        },
      });
    });

    it('should create minimal task without optional fields', async () => {
      const createRequest: CreateTaskRequest = {
        title: 'Minimal Task',
      };

      const mockCreatedTask = {
        gid: 'task-minimal',
        name: 'Minimal Task',
        notes: '',
        completed: false,
        due_date: null,
        assignee: null,
        projects: [],
        tags: [],
        permalink_url: 'https://app.asana.com/0/0/task-minimal',
        created_at: '2024-01-01T10:00:00.000Z',
        modified_at: '2024-01-01T10:00:00.000Z',
        custom_fields: [],
      };

      const mockResponse = {
        data: {
          data: mockCreatedTask,
        },
      };

      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await adapter.createTask(createRequest);

      expect(result.id).toBe('task-minimal');
      expect(result.title).toBe('Minimal Task');

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/tasks', {
        data: {
          name: 'Minimal Task',
          notes: '',
        },
      });
    });

    it('should handle API errors during task creation', async () => {
      const mockError = {
        response: {
          status: 400,
          statusText: 'Bad Request',
          data: { message: 'Invalid task data' },
        },
      };

      mockAxiosInstance.post.mockRejectedValue(mockError);

      await expect(adapter.createTask({ title: 'Test' })).rejects.toThrow();
    });
  });

  describe('validateCredentials', () => {
    it('should return true for valid credentials', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        data: { data: { gid: 'user-123' } },
      });

      const result = await adapter.validateCredentials(mockCredentials);

      expect(result).toBe(true);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/users/me');
    });

    it('should return false for invalid credentials', async () => {
      const mockError = {
        response: {
          status: 401,
          statusText: 'Unauthorized',
        },
      };

      mockAxiosInstance.get.mockRejectedValue(mockError);

      const result = await adapter.validateCredentials(mockCredentials);

      expect(result).toBe(false);
    });
  });

  describe('setupWebhook', () => {
    it('should setup webhook successfully', async () => {
      const webhookUrl = 'https://example.com/webhook';

      const mockWorkspacesResponse = {
        data: {
          data: [
            { gid: 'workspace-1', name: 'Test Workspace' },
          ],
        },
      };

      const mockWebhookResponse = {
        data: {
          data: {
            gid: 'webhook-123',
            resource: 'workspace-1',
            target: webhookUrl,
            active: true,
          },
        },
      };

      mockAxiosInstance.get.mockResolvedValue(mockWorkspacesResponse);
      mockAxiosInstance.post.mockResolvedValue(mockWebhookResponse);

      const result = await adapter.setupWebhook(webhookUrl);

      expect(result).toEqual({
        id: 'webhook-123',
        url: webhookUrl,
        events: ['task.added', 'task.changed', 'task.deleted'],
        active: true,
      });

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/webhooks', {
        data: {
          resource: 'workspace-1',
          target: webhookUrl,
          filters: [
            { resource_type: 'task', action: 'added' },
            { resource_type: 'task', action: 'changed' },
            { resource_type: 'task', action: 'deleted' },
          ],
        },
      });
    });

    it('should handle webhook setup failure', async () => {
      const mockError = {
        response: {
          status: 400,
          statusText: 'Bad Request',
          data: { message: 'Invalid webhook URL' },
        },
      };

      mockAxiosInstance.get.mockResolvedValue({
        data: { data: [{ gid: 'workspace-1', name: 'Test' }] },
      });
      mockAxiosInstance.post.mockRejectedValue(mockError);

      await expect(adapter.setupWebhook('invalid-url')).rejects.toThrow();
    });
  });

  describe('adapter properties', () => {
    it('should return correct provider name', () => {
      expect(adapter.getProvider()).toBe('asana');
    });

    it('should support two-way sync', () => {
      expect(adapter.supportsTwoWaySync()).toBe(true);
    });

    it('should support webhooks', () => {
      expect(adapter.supportsWebhooks()).toBe(true);
    });
  });

  describe('error handling', () => {
    it('should handle rate limiting errors', async () => {
      const mockError = {
        response: {
          status: 429,
          statusText: 'Too Many Requests',
          headers: {
            'retry-after': '60',
          },
        },
      };

      mockAxiosInstance.get.mockRejectedValue(mockError);

      await expect(adapter.fetchTasks(mockConfig)).rejects.toMatchObject({
        type: 'RATE_LIMIT_ERROR',
        retryable: true,
        retryAfter: 60000,
      });
    });

    it('should handle authentication errors', async () => {
      const mockError = {
        response: {
          status: 401,
          statusText: 'Unauthorized',
        },
      };

      mockAxiosInstance.get.mockRejectedValue(mockError);

      await expect(adapter.fetchTasks(mockConfig)).rejects.toMatchObject({
        type: 'AUTH_ERROR',
        retryable: false,
      });
    });

    it('should handle network errors', async () => {
      const mockError = {
        code: 'ENOTFOUND',
        message: 'Network error',
      };

      mockAxiosInstance.get.mockRejectedValue(mockError);

      await expect(adapter.fetchTasks(mockConfig)).rejects.toMatchObject({
        type: 'NETWORK_ERROR',
        retryable: true,
        retryAfter: 30000,
      });
    });
  });
});