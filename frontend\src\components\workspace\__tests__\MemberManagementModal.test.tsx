import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { MemberManagementModal } from '../MemberManagementModal'
import { useWorkspaceStore } from '@/store/workspace'
import { workspaceService } from '@/services/workspace'
import toast from 'react-hot-toast'

// Mock dependencies
vi.mock('@/store/workspace')
vi.mock('@/services/workspace')
vi.mock('react-hot-toast')

const mockUseWorkspaceStore = useWorkspaceStore as any
const mockWorkspaceService = workspaceService as any
const mockToast = toast as any

const mockWorkspace = {
  id: '1',
  name: 'Test Workspace',
  slug: 'test',
  ownerId: 'user1',
  settings: {
    priorityWeights: {
      dueDateProximity: 0.3,
      effortEstimate: 0.2,
      businessImpact: 0.3,
      contextSwitching: 0.2
    },
    defaultSyncInterval: 15,
    enableTwoWaySync: true
  },
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockMembers = [
  {
    id: '1',
    userId: '<EMAIL>',
    workspaceId: '1',
    role: 'OWNER' as const,
    permissions: [],
    joinedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    userId: '<EMAIL>',
    workspaceId: '1',
    role: 'ADMIN' as const,
    permissions: [],
    joinedAt: '2024-01-02T00:00:00Z'
  },
  {
    id: '3',
    userId: '<EMAIL>',
    workspaceId: '1',
    role: 'MEMBER' as const,
    permissions: [],
    joinedAt: '2024-01-03T00:00:00Z'
  }
]

const mockOnClose = vi.fn()
const mockSetMembers = vi.fn()

const renderModal = (isOpen = true) => {
  return render(
    <MemberManagementModal
      isOpen={isOpen}
      onClose={mockOnClose}
      workspace={mockWorkspace}
    />
  )
}

describe('MemberManagementModal', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    mockUseWorkspaceStore.mockReturnValue({
      workspaces: [mockWorkspace],
      currentWorkspace: mockWorkspace,
      members: mockMembers,
      isLoading: false,
      setWorkspaces: vi.fn(),
      setCurrentWorkspace: vi.fn(),
      addWorkspace: vi.fn(),
      updateWorkspace: vi.fn(),
      removeWorkspace: vi.fn(),
      setMembers: mockSetMembers,
      addMember: vi.fn(),
      updateMember: vi.fn(),
      removeMember: vi.fn(),
      setLoading: vi.fn(),
      switchWorkspace: vi.fn()
    })
    
    mockWorkspaceService.getMembers.mockResolvedValue(mockMembers)
    mockWorkspaceService.inviteMember.mockResolvedValue(mockMembers[0])
    mockWorkspaceService.updateMember.mockResolvedValue(mockMembers[1])
    mockWorkspaceService.removeMember.mockResolvedValue()
  })

  it('does not render when closed', () => {
    renderModal(false)
    expect(screen.queryByText('Manage Members - Test Workspace')).not.toBeInTheDocument()
  })

  it('renders when open and loads members', async () => {
    renderModal()
    
    expect(screen.getByText('Manage Members - Test Workspace')).toBeInTheDocument()
    
    await waitFor(() => {
      expect(mockWorkspaceService.getMembers).toHaveBeenCalledWith('1')
      expect(mockSetMembers).toHaveBeenCalledWith(mockMembers)
    })
  })

  it('shows loading state initially', () => {
    renderModal()
    
    expect(screen.getByText('Loading members...')).toBeInTheDocument()
  })

  it('displays member list after loading', async () => {
    renderModal()
    
    await waitFor(() => {
      expect(screen.getByTestId('member-1')).toBeInTheDocument()
      expect(screen.getByTestId('member-2')).toBeInTheDocument()
      expect(screen.getByTestId('member-3')).toBeInTheDocument()
    })
    
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })

  it('shows correct role icons and labels', async () => {
    renderModal()
    
    await waitFor(() => {
      expect(screen.getByText('Owner')).toBeInTheDocument()
      expect(screen.getByText('Admin')).toBeInTheDocument()
      expect(screen.getByText('Member')).toBeInTheDocument()
    })
  })

  it('shows invite form when invite button is clicked', async () => {
    renderModal()
    
    await waitFor(() => {
      const inviteButton = screen.getByTestId('invite-member-button')
      fireEvent.click(inviteButton)
    })
    
    expect(screen.getByTestId('invite-email-input')).toBeInTheDocument()
    expect(screen.getByTestId('invite-role-select')).toBeInTheDocument()
  })

  it('validates email when inviting member', async () => {
    renderModal()
    
    await waitFor(() => {
      const inviteButton = screen.getByTestId('invite-member-button')
      fireEvent.click(inviteButton)
    })
    
    const sendButton = screen.getByTestId('send-invite-button')
    fireEvent.click(sendButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('invite-error')).toHaveTextContent('Email is required')
    })
    
    const emailInput = screen.getByTestId('invite-email-input')
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } })
    fireEvent.click(sendButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('invite-error')).toHaveTextContent('Please enter a valid email address')
    })
  })

  it('invites new member successfully', async () => {
    const newMember = {
      id: '4',
      userId: '<EMAIL>',
      workspaceId: '1',
      role: 'MEMBER' as const,
      permissions: [],
      joinedAt: '2024-01-04T00:00:00Z'
    }
    
    mockWorkspaceService.inviteMember.mockResolvedValue(newMember)
    
    renderModal()
    
    await waitFor(() => {
      const inviteButton = screen.getByTestId('invite-member-button')
      fireEvent.click(inviteButton)
    })
    
    const emailInput = screen.getByTestId('invite-email-input')
    const roleSelect = screen.getByTestId('invite-role-select')
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(roleSelect, { target: { value: 'ADMIN' } })
    
    const sendButton = screen.getByTestId('send-invite-button')
    fireEvent.click(sendButton)
    
    await waitFor(() => {
      expect(mockWorkspaceService.inviteMember).toHaveBeenCalledWith('1', {
        email: '<EMAIL>',
        role: 'ADMIN'
      })
      expect(mockToast.success).toHaveBeenCalledWith('Member invited successfully')
    })
  })

  it('handles invite member error', async () => {
    const error = {
      response: {
        data: {
          message: 'User already exists in workspace'
        }
      }
    }
    
    mockWorkspaceService.inviteMember.mockRejectedValue(error)
    
    renderModal()
    
    await waitFor(() => {
      const inviteButton = screen.getByTestId('invite-member-button')
      fireEvent.click(inviteButton)
    })
    
    const emailInput = screen.getByTestId('invite-email-input')
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    
    const sendButton = screen.getByTestId('send-invite-button')
    fireEvent.click(sendButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('invite-error')).toHaveTextContent('User already exists in workspace')
      expect(mockToast.error).toHaveBeenCalledWith('User already exists in workspace')
    })
  })

  it('updates member role', async () => {
    const updatedMember = { ...mockMembers[2], role: 'ADMIN' as const }
    mockWorkspaceService.updateMember.mockResolvedValue(updatedMember)
    
    renderModal()
    
    await waitFor(() => {
      const roleSelect = screen.getByTestId('role-select-3')
      fireEvent.change(roleSelect, { target: { value: 'ADMIN' } })
    })
    
    await waitFor(() => {
      expect(mockWorkspaceService.updateMember).toHaveBeenCalledWith('1', '3', {
        role: 'ADMIN'
      })
      expect(mockToast.success).toHaveBeenCalledWith('Member role updated successfully')
    })
  })

  it('removes member after confirmation', async () => {
    // Mock window.confirm
    const originalConfirm = window.confirm
    window.confirm = vi.fn(() => true)
    
    renderModal()
    
    await waitFor(() => {
      const removeButton = screen.getByTestId('remove-member-3')
      fireEvent.click(removeButton)
    })
    
    await waitFor(() => {
      expect(mockWorkspaceService.removeMember).toHaveBeenCalledWith('1', '3')
      expect(mockToast.success).toHaveBeenCalledWith('Member removed successfully')
    })
    
    window.confirm = originalConfirm
  })

  it('does not remove member if confirmation is cancelled', async () => {
    // Mock window.confirm
    const originalConfirm = window.confirm
    window.confirm = vi.fn(() => false)
    
    renderModal()
    
    await waitFor(() => {
      const removeButton = screen.getByTestId('remove-member-3')
      fireEvent.click(removeButton)
    })
    
    expect(mockWorkspaceService.removeMember).not.toHaveBeenCalled()
    
    window.confirm = originalConfirm
  })

  it('does not show role select and remove button for owner', async () => {
    renderModal()
    
    await waitFor(() => {
      expect(screen.queryByTestId('role-select-1')).not.toBeInTheDocument()
      expect(screen.queryByTestId('remove-member-1')).not.toBeInTheDocument()
    })
  })

  it('shows role select and remove button for non-owners', async () => {
    renderModal()
    
    await waitFor(() => {
      expect(screen.getByTestId('role-select-2')).toBeInTheDocument()
      expect(screen.getByTestId('remove-member-2')).toBeInTheDocument()
      expect(screen.getByTestId('role-select-3')).toBeInTheDocument()
      expect(screen.getByTestId('remove-member-3')).toBeInTheDocument()
    })
  })

  it('cancels invite form', async () => {
    renderModal()
    
    await waitFor(() => {
      const inviteButton = screen.getByTestId('invite-member-button')
      fireEvent.click(inviteButton)
    })
    
    const emailInput = screen.getByTestId('invite-email-input')
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    
    const cancelButton = screen.getByTestId('cancel-invite-button')
    fireEvent.click(cancelButton)
    
    expect(screen.queryByTestId('invite-email-input')).not.toBeInTheDocument()
  })

  it('closes modal when close button is clicked', () => {
    renderModal()
    
    const closeButton = screen.getByTestId('close-modal-button')
    fireEvent.click(closeButton)
    
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('closes modal when close button at bottom is clicked', async () => {
    renderModal()
    
    await waitFor(() => {
      const closeButton = screen.getByTestId('close-button')
      fireEvent.click(closeButton)
    })
    
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('shows empty state when no members', async () => {
    mockWorkspaceService.getMembers.mockResolvedValue([])
    
    renderModal()
    
    await waitFor(() => {
      expect(screen.getByText('No members found. Invite someone to get started.')).toBeInTheDocument()
    })
  })

  it('handles member loading error', async () => {
    mockWorkspaceService.getMembers.mockRejectedValue(new Error('Failed to load'))
    
    renderModal()
    
    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith('Failed to load workspace members')
    })
  })
})