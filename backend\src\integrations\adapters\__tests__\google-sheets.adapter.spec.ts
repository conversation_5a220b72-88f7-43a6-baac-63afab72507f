import { Test, TestingModule } from '@nestjs/testing';
import { GoogleSheetsAdapter } from '../google-sheets.adapter';
import {
  IntegrationConfig,
  OAuthCredentials,
  TaskUpdate,
  CreateTaskRequest,
  TaskStatus,
  TaskPriority,
  IntegrationProvider,
} from '../../types';

// Mock googleapis
const mockSheetsApi = {
  spreadsheets: {
    values: {
      get: jest.fn(),
      update: jest.fn(),
      append: jest.fn(),
    },
  },
};

const mockOAuth2Api = {
  userinfo: {
    get: jest.fn(),
  },
};

const mockAuth = {
  setCredentials: jest.fn(),
};

jest.mock('googleapis', () => ({
  google: {
    sheets: jest.fn(() => mockSheetsApi),
    oauth2: jest.fn(() => mockOAuth2Api),
    auth: {
      OAuth2: jest.fn(() => mockAuth),
    },
  },
}));

describe('GoogleSheetsAdapter', () => {
  let adapter: GoogleSheetsAdapter;
  let module: TestingModule;

  const mockCredentials: OAuthCredentials = {
    accessToken: 'mock-access-token',
    refreshToken: 'mock-refresh-token',
    expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
    scope: ['https://www.googleapis.com/auth/spreadsheets'],
    tokenType: 'Bearer',
    userId: 'user123',
    userEmail: '<EMAIL>',
  };

  const mockConfig: IntegrationConfig = {
    syncInterval: 15,
    enableTwoWaySync: true,
    fieldMappings: [],
    filters: [],
    customSettings: {
      spreadsheetId: 'test-spreadsheet-id',
      sheetName: 'Tasks',
      credentials: mockCredentials,
    },
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [GoogleSheetsAdapter],
    }).compile();

    adapter = module.get<GoogleSheetsAdapter>(GoogleSheetsAdapter);

    // Reset all mocks
    jest.clearAllMocks();
  });

  afterEach(async () => {
    await module.close();
  });

  describe('constructor', () => {
    it('should initialize with correct provider and base URL', () => {
      expect(adapter.getProvider()).toBe(IntegrationProvider.GOOGLE_SHEETS);
      expect(adapter.supportsTwoWaySync()).toBe(true);
      expect(adapter.supportsWebhooks()).toBe(false);
    });
  });

  describe('authenticate', () => {
    it('should authenticate successfully with valid credentials', async () => {
      const mockUserInfo = {
        data: {
          id: 'user123',
          email: '<EMAIL>',
          name: 'Test User',
          picture: 'https://example.com/avatar.jpg',
        },
      };

      mockOAuth2Api.userinfo.get.mockResolvedValue(mockUserInfo);

      const result = await adapter.authenticate(mockCredentials);

      expect(result.success).toBe(true);
      expect(result.user).toEqual({
        id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        avatar: 'https://example.com/avatar.jpg',
      });
      expect(result.credentials).toEqual(mockCredentials);
      expect(mockAuth.setCredentials).toHaveBeenCalledWith({
        access_token: mockCredentials.accessToken,
        refresh_token: mockCredentials.refreshToken,
      });
    });

    it('should handle authentication failure', async () => {
      mockOAuth2Api.userinfo.get.mockRejectedValue(new Error('Auth failed'));

      const result = await adapter.authenticate(mockCredentials);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to authenticate with Google Sheets');
    });
  });

  describe('fetchTasks', () => {
    const mockHeaders = ['Title', 'Description', 'Status', 'Priority', 'Due Date'];
    const mockRows = [
      mockHeaders,
      ['Task 1', 'Description 1', 'todo', 'high', '2024-01-15'],
      ['Task 2', 'Description 2', 'in_progress', 'medium', '2024-01-20'],
      ['Task 3', '', 'done', 'low', ''],
    ];

    it('should fetch and parse tasks successfully', async () => {
      mockSheetsApi.spreadsheets.values.get.mockResolvedValue({
        data: { values: mockRows },
      });

      const tasks = await adapter.fetchTasks(mockConfig);

      expect(tasks).toHaveLength(3);
      expect(tasks[0]).toMatchObject({
        title: 'Task 1',
        description: 'Description 1',
        status: TaskStatus.TODO,
        priority: TaskPriority.HIGH,
        dueDate: new Date('2024-01-15'),
      });
      expect(tasks[0].id).toMatch(/^gs_test-spreadsheet-id_Tasks_2$/);
      expect(tasks[0].sourceUrl).toContain('docs.google.com/spreadsheets');
    });

    it('should handle empty spreadsheet', async () => {
      mockSheetsApi.spreadsheets.values.get.mockResolvedValue({
        data: { values: [] },
      });

      const tasks = await adapter.fetchTasks(mockConfig);

      expect(tasks).toHaveLength(0);
    });

    it('should skip rows without titles', async () => {
      const rowsWithEmptyTitle = [
        mockHeaders,
        ['Task 1', 'Description 1', 'todo', 'high', '2024-01-15'],
        ['', 'Description 2', 'in_progress', 'medium', '2024-01-20'], // Empty title
        ['Task 3', '', 'done', 'low', ''],
      ];

      mockSheetsApi.spreadsheets.values.get.mockResolvedValue({
        data: { values: rowsWithEmptyTitle },
      });

      const tasks = await adapter.fetchTasks(mockConfig);

      expect(tasks).toHaveLength(2);
      expect(tasks.map(t => t.title)).toEqual(['Task 1', 'Task 3']);
    });

    it('should throw error when spreadsheet ID is missing', async () => {
      const configWithoutSpreadsheetId = {
        ...mockConfig,
        customSettings: { ...mockConfig.customSettings, spreadsheetId: undefined },
      };

      await expect(adapter.fetchTasks(configWithoutSpreadsheetId)).rejects.toThrow(
        'Spreadsheet ID is required in configuration'
      );
    });

    it('should handle API errors', async () => {
      const apiError = new Error('API Error');
      apiError['response'] = { status: 403 };
      mockSheetsApi.spreadsheets.values.get.mockRejectedValue(apiError);

      await expect(adapter.fetchTasks(mockConfig)).rejects.toThrow();
    });
  });

  describe('updateTask', () => {
    const mockTaskId = 'gs_test-spreadsheet-id_Tasks_2';
    const mockHeaders = ['Title', 'Description', 'Status', 'Priority'];
    const mockCurrentRow = ['Old Title', 'Old Description', 'todo', 'medium'];

    const mockUpdates: TaskUpdate = {
      title: 'Updated Title',
      status: TaskStatus.IN_PROGRESS,
      priority: TaskPriority.HIGH,
    };

    it('should update task successfully', async () => {
      // Mock getting current row
      mockSheetsApi.spreadsheets.values.get
        .mockResolvedValueOnce({ data: { values: [mockCurrentRow] } }) // Current row
        .mockResolvedValueOnce({ data: { values: [mockHeaders] } }); // Headers

      // Mock update operation
      mockSheetsApi.spreadsheets.values.update.mockResolvedValue({});

      const result = await adapter.updateTask(mockTaskId, mockUpdates);

      expect(result.title).toBe('Updated Title');
      expect(result.status).toBe(TaskStatus.IN_PROGRESS);
      expect(result.priority).toBe(TaskPriority.HIGH);

      expect(mockSheetsApi.spreadsheets.values.update).toHaveBeenCalledWith({
        spreadsheetId: 'test-spreadsheet-id',
        range: 'Tasks!2:2',
        valueInputOption: 'USER_ENTERED',
        requestBody: {
          values: [['Updated Title', 'Old Description', 'in_progress', 'high']],
        },
      });
    });

    it('should handle invalid task ID format', async () => {
      const invalidTaskId = 'invalid-task-id';

      await expect(adapter.updateTask(invalidTaskId, mockUpdates)).rejects.toThrow(
        'Invalid Google Sheets task ID format'
      );
    });
  });

  describe('createTask', () => {
    const mockTask: CreateTaskRequest = {
      title: 'New Task',
      description: 'New task description',
      priority: TaskPriority.HIGH,
      dueDate: new Date('2024-01-15'),
      estimatedMinutes: 120,
      tags: ['urgent', 'feature'],
    };

    beforeEach(() => {
      // Set environment variables for testing
      process.env.GOOGLE_SHEETS_DEFAULT_SPREADSHEET_ID = 'default-spreadsheet-id';
      process.env.GOOGLE_SHEETS_DEFAULT_SHEET_NAME = 'Tasks';
    });

    afterEach(() => {
      delete process.env.GOOGLE_SHEETS_DEFAULT_SPREADSHEET_ID;
      delete process.env.GOOGLE_SHEETS_DEFAULT_SHEET_NAME;
    });

    it('should create task successfully', async () => {
      const mockHeaders = ['Title', 'Description', 'Priority', 'Due Date', 'Estimate', 'Tags', 'Status'];

      mockSheetsApi.spreadsheets.values.get.mockResolvedValue({
        data: { values: [mockHeaders] },
      });

      mockSheetsApi.spreadsheets.values.append.mockResolvedValue({
        data: {
          updates: {
            updatedRange: 'Tasks!A5:G5',
          },
        },
      });

      const result = await adapter.createTask(mockTask);

      expect(result.title).toBe('New Task');
      expect(result.description).toBe('New task description');
      expect(result.priority).toBe(TaskPriority.HIGH);
      expect(result.id).toBe('gs_default-spreadsheet-id_Tasks_5');

      expect(mockSheetsApi.spreadsheets.values.append).toHaveBeenCalledWith({
        spreadsheetId: 'default-spreadsheet-id',
        range: 'Tasks!A:Z',
        valueInputOption: 'USER_ENTERED',
        requestBody: {
          values: [['New Task', 'New task description', 'high', '2024-01-15', '120', 'urgent, feature', 'todo']],
        },
      });
    });

    it('should throw error when default spreadsheet ID is not configured', async () => {
      delete process.env.GOOGLE_SHEETS_DEFAULT_SPREADSHEET_ID;

      await expect(adapter.createTask(mockTask)).rejects.toThrow(
        'Default spreadsheet ID not configured'
      );
    });
  });

  describe('validateCredentials', () => {
    it('should return true for valid credentials', async () => {
      mockOAuth2Api.userinfo.get.mockResolvedValue({ data: { id: 'user123' } });

      const result = await adapter.validateCredentials(mockCredentials);

      expect(result).toBe(true);
      expect(mockAuth.setCredentials).toHaveBeenCalledWith({
        access_token: mockCredentials.accessToken,
        refresh_token: mockCredentials.refreshToken,
      });
    });

    it('should return false for invalid credentials', async () => {
      mockOAuth2Api.userinfo.get.mockRejectedValue(new Error('Invalid credentials'));

      const result = await adapter.validateCredentials(mockCredentials);

      expect(result).toBe(false);
    });
  });

  describe('column mapping', () => {
    it('should use configured field mappings', async () => {
      const configWithMappings: IntegrationConfig = {
        ...mockConfig,
        fieldMappings: [
          { localField: 'title', externalField: 'Task Name' },
          { localField: 'status', externalField: 'Current Status' },
        ],
      };

      const mockHeaders = ['Task Name', 'Details', 'Current Status', 'Owner'];
      const mockRows = [
        mockHeaders,
        ['Custom Task', 'Custom Description', 'in progress', 'John Doe'],
      ];

      mockSheetsApi.spreadsheets.values.get.mockResolvedValue({
        data: { values: mockRows },
      });

      const tasks = await adapter.fetchTasks(configWithMappings);

      expect(tasks).toHaveLength(1);
      expect(tasks[0].title).toBe('Custom Task');
      expect(tasks[0].status).toBe(TaskStatus.IN_PROGRESS);
    });

    it('should handle various date formats', async () => {
      const mockHeaders = ['Title', 'Due Date'];
      const mockRows = [
        mockHeaders,
        ['Task 1', '2024-01-15'], // YYYY-MM-DD
        ['Task 2', '01/15/2024'], // MM/DD/YYYY
        ['Task 3', '01-15-2024'], // MM-DD-YYYY
        ['Task 4', 'invalid-date'], // Invalid date
      ];

      mockSheetsApi.spreadsheets.values.get.mockResolvedValue({
        data: { values: mockRows },
      });

      const tasks = await adapter.fetchTasks(mockConfig);

      expect(tasks).toHaveLength(4);
      expect(tasks[0].dueDate).toEqual(new Date('2024-01-15'));
      expect(tasks[1].dueDate).toEqual(new Date('01/15/2024'));
      expect(tasks[2].dueDate).toEqual(new Date('01-15-2024'));
      expect(tasks[3].dueDate).toBeUndefined(); // Invalid date should be undefined
    });

    it('should parse tags correctly', async () => {
      const mockHeaders = ['Title', 'Tags'];
      const mockRows = [
        mockHeaders,
        ['Task 1', 'urgent, feature, bug'],
        ['Task 2', 'enhancement'],
        ['Task 3', ''], // Empty tags
      ];

      mockSheetsApi.spreadsheets.values.get.mockResolvedValue({
        data: { values: mockRows },
      });

      const tasks = await adapter.fetchTasks(mockConfig);

      expect(tasks).toHaveLength(3);
      expect(tasks[0].tags).toEqual(['urgent', 'feature', 'bug']);
      expect(tasks[1].tags).toEqual(['enhancement']);
      expect(tasks[2].tags).toEqual([]);
    });
  });

  describe('error handling', () => {
    it('should handle authentication errors', async () => {
      const authError = new Error('Auth failed');
      authError['response'] = { status: 401 };
      mockSheetsApi.spreadsheets.values.get.mockRejectedValue(authError);

      await expect(adapter.fetchTasks(mockConfig)).rejects.toThrow();
    });

    it('should handle rate limiting errors', async () => {
      const rateLimitError = new Error('Rate limited');
      rateLimitError['response'] = { 
        status: 429,
        headers: { 'retry-after': '60' }
      };
      mockSheetsApi.spreadsheets.values.get.mockRejectedValue(rateLimitError);

      await expect(adapter.fetchTasks(mockConfig)).rejects.toThrow();
    });

    it('should handle network errors', async () => {
      const networkError = new Error('Network error');
      networkError['code'] = 'ENOTFOUND';
      mockSheetsApi.spreadsheets.values.get.mockRejectedValue(networkError);

      await expect(adapter.fetchTasks(mockConfig)).rejects.toThrow();
    });
  });

  describe('task ID generation and parsing', () => {
    it('should generate valid task IDs', async () => {
      const mockHeaders = ['Title'];
      const mockRows = [mockHeaders, ['Test Task']];

      mockSheetsApi.spreadsheets.values.get.mockResolvedValue({
        data: { values: mockRows },
      });

      const tasks = await adapter.fetchTasks(mockConfig);

      expect(tasks[0].id).toBe('gs_test-spreadsheet-id_Tasks_2');
    });

    it('should parse task IDs correctly', async () => {
      const taskId = 'gs_spreadsheet123_Sheet1_5';
      const mockHeaders = ['Title'];
      const mockCurrentRow = ['Test Task'];

      mockSheetsApi.spreadsheets.values.get
        .mockResolvedValueOnce({ data: { values: [mockCurrentRow] } })
        .mockResolvedValueOnce({ data: { values: [mockHeaders] } });

      mockSheetsApi.spreadsheets.values.update.mockResolvedValue({});

      await adapter.updateTask(taskId, { title: 'Updated Task' });

      expect(mockSheetsApi.spreadsheets.values.get).toHaveBeenCalledWith({
        spreadsheetId: 'spreadsheet123',
        range: 'Sheet1!5:5',
      });
    });
  });
});