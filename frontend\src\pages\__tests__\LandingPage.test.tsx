import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { render } from '@/test/test-utils'
import { LandingPage } from '../LandingPage'

// Mock the landing service
vi.mock('@/services/landing', () => ({
  landingService: {
    submitEarlyAccess: vi.fn(),
  },
}))

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

describe('LandingPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders landing page correctly', () => {
    render(<LandingPage />)
    
    expect(screen.getByText('Unify your tasks.')).toBeInTheDocument()
    expect(screen.getByText('Simplify your day.')).toBeInTheDocument()
    expect(screen.getByText(/transform scattered workflows/i)).toBeInTheDocument()
  })

  it('renders key features', () => {
    render(<LandingPage />)
    
    expect(screen.getByText('Smart Prioritization')).toBeInTheDocument()
    expect(screen.getByText('Seamless Integration')).toBeInTheDocument()
    expect(screen.getByText('Team Collaboration')).toBeInTheDocument()
  })

  it('renders integration logos', () => {
    render(<LandingPage />)
    
    expect(screen.getByText('Asana')).toBeInTheDocument()
    expect(screen.getByText('Trello')).toBeInTheDocument()
    expect(screen.getByText('Jira')).toBeInTheDocument()
    expect(screen.getByText('Google Sheets')).toBeInTheDocument()
  })

  it('renders testimonials', () => {
    render(<LandingPage />)
    
    expect(screen.getByText('Sarah Chen')).toBeInTheDocument()
    expect(screen.getByText('Marcus Rodriguez')).toBeInTheDocument()
    expect(screen.getByText('Emily Watson')).toBeInTheDocument()
  })

  it('has working email signup forms', () => {
    render(<LandingPage />)
    
    const emailInputs = screen.getAllByPlaceholderText(/enter your email/i)
    const signupButtons = screen.getAllByText(/join waitlist|get early access/i)
    
    expect(emailInputs).toHaveLength(2) // Hero and CTA sections
    expect(signupButtons.length).toBeGreaterThan(0)
  })

  it('has navigation links', () => {
    render(<LandingPage />)
    
    expect(screen.getByText('Sign In')).toBeInTheDocument()
    expect(screen.getByText('Get Started')).toBeInTheDocument()
  })
})