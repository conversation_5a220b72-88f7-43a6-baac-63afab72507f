import { Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import { useAuthStore } from '@/store/auth'
import { AuthLayout } from '@/components/layouts/AuthLayout'
import { AppLayout } from '@/components/layouts/AppLayout'
import { LandingPage } from '@/pages/LandingPage'
import { LoginPage } from '@/pages/auth/LoginPage'
import { SignupPage } from '@/pages/auth/SignupPage'
import { OAuthCallbackPage } from '@/pages/auth/OAuthCallbackPage'
import { OAuthCallbackPage as IntegrationOAuthCallbackPage } from '@/pages/integrations/OAuthCallbackPage'
import { DashboardPage } from '@/pages/DashboardPage'
import { TasksPage } from '@/pages/TasksPage'
import { DailyPlannerPage } from '@/pages/DailyPlannerPage'
import { IntegrationsPage } from '@/pages/IntegrationsPage'
import { WorkspacesPage } from '@/pages/WorkspacesPage'
import { SettingsPage } from '@/pages/SettingsPage'

function App() {
  const { user, isLoading } = useAuthStore()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <>
      <Routes>
        {/* Landing page */}
        <Route path="/" element={<LandingPage />} />

        {/* Public routes */}
        <Route path="/auth" element={<AuthLayout />}>
          <Route path="login" element={<LoginPage />} />
          <Route path="signup" element={<SignupPage />} />
          <Route path="callback/:provider" element={<OAuthCallbackPage />} />
        </Route>

        {/* Protected routes */}
        <Route
          path="/app/*"
          element={
            user ? (
              <AppLayout>
                <Routes>
                  <Route path="/" element={<Navigate to="/app/dashboard" replace />} />
                  <Route path="/dashboard" element={<DashboardPage />} />
                  <Route path="/tasks" element={<TasksPage />} />
                  <Route path="/planner" element={<DailyPlannerPage />} />
                  <Route path="/integrations" element={<IntegrationsPage />} />
                  <Route path="/integrations/oauth/callback" element={<IntegrationOAuthCallbackPage />} />
                  <Route path="/workspaces" element={<WorkspacesPage />} />
                  <Route path="/settings" element={<SettingsPage />} />
                </Routes>
              </AppLayout>
            ) : (
              <Navigate to="/auth/login" replace />
            )
          }
        />

        {/* Redirect authenticated users to app */}
        <Route
          path="/dashboard"
          element={user ? <Navigate to="/app/dashboard" replace /> : <Navigate to="/auth/login" replace />}
        />
      </Routes>
      <Toaster position="top-right" />
    </>
  )
}

export default App