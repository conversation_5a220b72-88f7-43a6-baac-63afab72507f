import { IsString, IsOptional, <PERSON>Date<PERSON>tring, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateTaskDto {
  @ApiProperty({ 
    description: 'Task title', 
    example: 'Fix authentication bug'
  })
  @IsString()
  title: string;

  @ApiProperty({ 
    description: 'Task description', 
    example: 'Users are unable to login with Google OAuth',
    required: false 
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    description: 'Task status', 
    example: 'todo',
    required: false,
    default: 'todo'
  })
  @IsOptional()
  @IsString()
  status?: string = 'todo';

  @ApiProperty({ 
    description: 'Priority level', 
    example: 'medium',
    required: false,
    default: 'medium'
  })
  @IsOptional()
  @IsString()
  priority?: string = 'medium';

  @ApiProperty({ 
    description: 'Assignee ID', 
    example: 'user-123',
    required: false 
  })
  @IsOptional()
  @IsString()
  assigneeId?: string;

  @ApiProperty({ 
    description: 'Assignee name', 
    example: '<PERSON>',
    required: false 
  })
  @IsOptional()
  @IsString()
  assigneeName?: string;

  @ApiProperty({ 
    description: 'Due date (ISO string)', 
    example: '2024-12-31T23:59:59Z',
    required: false 
  })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiProperty({ 
    description: 'Estimated time in minutes', 
    example: 120,
    required: false 
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  estimatedMinutes?: number;

  @ApiProperty({ 
    description: 'Tags array', 
    example: ['urgent', 'bug'],
    required: false,
    default: []
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[] = [];

  @ApiProperty({ 
    description: 'Project name', 
    example: 'Authentication System',
    required: false 
  })
  @IsOptional()
  @IsString()
  projectName?: string;

  @ApiProperty({ 
    description: 'Source URL for the task', 
    example: 'https://app.asana.com/0/123456789/987654321'
  })
  @IsString()
  sourceUrl: string;
}