import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '../../database/database.service';
import { IntegrationService } from '../../integrations/services/integration.service';
import { 
  SyncConflict, 
  SyncError, 
  TaskUpdate, 
  ExternalTask,
  TaskStatus,
  TaskPriority 
} from '../../integrations/types';
import { ConflictResolutionStrategy } from '../constants/sync.constants';

@Injectable()
export class ConflictResolutionService {
  private readonly logger = new Logger(ConflictResolutionService.name);

  constructor(
    private prisma: DatabaseService,
    private integrationsService: IntegrationService,
  ) {}

  /**
   * Resolve conflicts using the specified strategy
   */
  async resolveConflicts(
    integrationId: string,
    conflicts: SyncConflict[],
    strategy: ConflictResolutionStrategy,
    resolvedBy?: string
  ): Promise<{
    resolved: SyncConflict[];
    failed: Array<{ conflict: SyncConflict; error: SyncError }>;
  }> {
    const resolved: SyncConflict[] = [];
    const failed: Array<{ conflict: SyncConflict; error: SyncError }> = [];

    for (const conflict of conflicts) {
      try {
        await this.resolveConflict(integrationId, conflict, strategy, resolvedBy);
        resolved.push(conflict);
        this.logger.log(`Resolved conflict for task ${conflict.taskId} field ${conflict.field}`);
      } catch (error) {
        const syncError: SyncError = {
          type: 'VALIDATION_ERROR',
          message: `Failed to resolve conflict: ${error.message}`,
          retryable: false,
          timestamp: new Date(),
          details: { conflict, error: error.message },
        };
        failed.push({ conflict, error: syncError });
        this.logger.error(`Failed to resolve conflict for task ${conflict.taskId}:`, error);
      }
    }

    return { resolved, failed };
  }

  /**
   * Resolve a single conflict
   */
  private async resolveConflict(
    integrationId: string,
    conflict: SyncConflict,
    strategy: ConflictResolutionStrategy,
    resolvedBy?: string
  ): Promise<void> {
    const task = await this.prisma.task.findUnique({
      where: { id: conflict.taskId },
      include: { integration: true },
    });

    if (!task) {
      throw new Error(`Task ${conflict.taskId} not found`);
    }

    let resolvedValue: any;
    let shouldSyncToRemote = false;
    let shouldUpdateLocal = false;

    switch (strategy) {
      case ConflictResolutionStrategy.LOCAL_WINS:
        resolvedValue = conflict.localValue;
        shouldSyncToRemote = true;
        break;

      case ConflictResolutionStrategy.REMOTE_WINS:
        resolvedValue = conflict.remoteValue;
        shouldUpdateLocal = true;
        break;

      case ConflictResolutionStrategy.LAST_MODIFIED_WINS:
        // Compare timestamps to determine winner
        const localModified = task.updatedAt;
        const remoteModified = conflict.conflictedAt;
        
        if (localModified > remoteModified) {
          resolvedValue = conflict.localValue;
          shouldSyncToRemote = true;
        } else {
          resolvedValue = conflict.remoteValue;
          shouldUpdateLocal = true;
        }
        break;

      case ConflictResolutionStrategy.MANUAL:
        // For manual resolution, we just log the conflict and don't auto-resolve
        await this.logConflictForManualResolution(conflict, resolvedBy);
        return;

      default:
        throw new Error(`Unknown conflict resolution strategy: ${strategy}`);
    }

    // Apply the resolution
    if (shouldUpdateLocal) {
      await this.updateLocalTask(task.id, conflict.field, resolvedValue);
    }

    if (shouldSyncToRemote) {
      await this.syncToRemoteTask(integrationId, task.externalId, conflict.field, resolvedValue);
    }

    // Update sync status
    await this.prisma.task.update({
      where: { id: conflict.taskId },
      data: {
        syncStatus: 'synced',
        lastSyncAt: new Date(),
      },
    });

    // Log the resolution
    await this.logConflictResolution(conflict, strategy, resolvedValue, resolvedBy);
  }

  /**
   * Update local task with resolved value
   */
  private async updateLocalTask(taskId: string, field: string, value: any): Promise<void> {
    const updateData: any = {};
    
    // Map field names to database columns
    switch (field) {
      case 'title':
        updateData.title = value;
        break;
      case 'description':
        updateData.description = value;
        break;
      case 'status':
        updateData.status = value;
        break;
      case 'priority':
        updateData.priority = value;
        break;
      case 'assigneeId':
        updateData.assigneeId = value;
        break;
      case 'assigneeName':
        updateData.assigneeName = value;
        break;
      case 'dueDate':
        updateData.dueDate = value ? new Date(value) : null;
        break;
      case 'estimatedMinutes':
        updateData.estimatedMinutes = value;
        break;
      case 'tags':
        updateData.tags = Array.isArray(value) ? value : [];
        break;
      case 'projectName':
        updateData.projectName = value;
        break;
      default:
        // Store in metadata for unknown fields
        const task = await this.prisma.task.findUnique({
          where: { id: taskId },
          select: { metadata: true },
        });
        
        const metadata = task?.metadata as Record<string, any> || {};
        metadata[field] = value;
        updateData.metadata = metadata;
    }

    await this.prisma.task.update({
      where: { id: taskId },
      data: updateData,
    });
  }

  /**
   * Sync resolved value to remote task
   */
  private async syncToRemoteTask(
    integrationId: string,
    externalTaskId: string,
    field: string,
    value: any
  ): Promise<void> {
    const integration = await this.prisma.integration.findUnique({
      where: { id: integrationId },
    });

    if (!integration) {
      throw new Error(`Integration ${integrationId} not found`);
    }

    const adapter = await this.integrationsService.getAdapter(integration.provider as any);
    if (!adapter) {
      throw new Error(`No adapter found for provider ${integration.provider}`);
    }

    // Build update object
    const update: TaskUpdate = {};
    switch (field) {
      case 'title':
        update.title = value;
        break;
      case 'description':
        update.description = value;
        break;
      case 'status':
        update.status = value as TaskStatus;
        break;
      case 'priority':
        update.priority = value as TaskPriority;
        break;
      case 'assigneeId':
        update.assigneeId = value;
        break;
      case 'dueDate':
        update.dueDate = value ? new Date(value) : undefined;
        break;
      case 'estimatedMinutes':
        update.estimatedMinutes = value;
        break;
      case 'tags':
        update.tags = Array.isArray(value) ? value : [];
        break;
    }

    // Only sync if the adapter supports two-way sync
    const config = integration.config as any;
    if (config.enableTwoWaySync) {
      await adapter.updateTask(externalTaskId, update);
    }
  }

  /**
   * Log conflict for manual resolution
   */
  private async logConflictForManualResolution(
    conflict: SyncConflict,
    resolvedBy?: string
  ): Promise<void> {
    // In a real implementation, you might store this in a separate conflicts table
    // or send notifications to users for manual resolution
    this.logger.warn(`Manual resolution required for conflict:`, {
      taskId: conflict.taskId,
      field: conflict.field,
      localValue: conflict.localValue,
      remoteValue: conflict.remoteValue,
      resolvedBy,
    });
  }

  /**
   * Log conflict resolution for audit trail
   */
  private async logConflictResolution(
    conflict: SyncConflict,
    strategy: ConflictResolutionStrategy,
    resolvedValue: any,
    resolvedBy?: string
  ): Promise<void> {
    // Create audit log entry
    const auditEntry = {
      taskId: conflict.taskId,
      field: conflict.field,
      strategy,
      localValue: conflict.localValue,
      remoteValue: conflict.remoteValue,
      resolvedValue,
      resolvedBy,
      resolvedAt: new Date(),
    };

    // In a real implementation, you might store this in a separate audit table
    this.logger.log(`Conflict resolved:`, auditEntry);
  }

  /**
   * Detect conflicts between local and remote tasks
   */
  async detectConflicts(
    localTask: any,
    remoteTask: ExternalTask,
    lastSyncAt: Date
  ): Promise<SyncConflict[]> {
    const conflicts: SyncConflict[] = [];
    const conflictedAt = new Date();

    // Fields to check for conflicts
    const fieldsToCheck = [
      'title',
      'description', 
      'status',
      'priority',
      'assigneeId',
      'assigneeName',
      'dueDate',
      'estimatedMinutes',
      'tags',
      'projectName',
    ];

    for (const field of fieldsToCheck) {
      const localValue = this.getFieldValue(localTask, field);
      const remoteValue = this.getFieldValue(remoteTask, field);

      // Check if values are different and both have been modified since last sync
      if (this.valuesAreDifferent(localValue, remoteValue)) {
        const localModified = localTask.updatedAt > lastSyncAt;
        const remoteModified = remoteTask.updatedAt > lastSyncAt;

        // Only create conflict if both sides have been modified
        if (localModified && remoteModified) {
          conflicts.push({
            taskId: localTask.id,
            field,
            localValue,
            remoteValue,
            lastSyncAt,
            conflictedAt,
          });
        }
      }
    }

    return conflicts;
  }

  /**
   * Get field value from task object
   */
  private getFieldValue(task: any, field: string): any {
    switch (field) {
      case 'dueDate':
        return task.dueDate ? new Date(task.dueDate) : null;
      case 'tags':
        return Array.isArray(task.tags) ? task.tags : [];
      default:
        return task[field];
    }
  }

  /**
   * Check if two values are different
   */
  private valuesAreDifferent(value1: any, value2: any): boolean {
    // Handle null/undefined
    if (value1 == null && value2 == null) return false;
    if (value1 == null || value2 == null) return true;

    // Handle arrays
    if (Array.isArray(value1) && Array.isArray(value2)) {
      if (value1.length !== value2.length) return true;
      return value1.some((item, index) => item !== value2[index]);
    }

    // Handle dates
    if (value1 instanceof Date && value2 instanceof Date) {
      return value1.getTime() !== value2.getTime();
    }

    // Handle primitives
    return value1 !== value2;
  }

  /**
   * Get pending conflicts for an integration
   */
  async getPendingConflicts(integrationId: string): Promise<SyncConflict[]> {
    // In a real implementation, you would query a conflicts table
    // For now, we'll return conflicts from tasks with conflict sync status
    const conflictedTasks = await this.prisma.task.findMany({
      where: {
        integrationId,
        syncStatus: 'conflict',
      },
    });

    // This is a simplified implementation - in reality you'd store conflicts separately
    return conflictedTasks.map(task => ({
      taskId: task.id,
      field: 'unknown', // Would be stored in conflicts table
      localValue: null,
      remoteValue: null,
      lastSyncAt: task.lastSyncAt,
      conflictedAt: task.updatedAt,
    }));
  }

  /**
   * Mark conflicts as resolved
   */
  async markConflictsResolved(taskIds: string[]): Promise<void> {
    await this.prisma.task.updateMany({
      where: {
        id: { in: taskIds },
        syncStatus: 'conflict',
      },
      data: {
        syncStatus: 'synced',
        lastSyncAt: new Date(),
      },
    });
  }
}