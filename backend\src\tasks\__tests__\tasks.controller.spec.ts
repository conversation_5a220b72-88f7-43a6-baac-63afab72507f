import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { TasksController } from '../tasks.controller';
import { TasksService } from '../services/tasks.service';
import { PrioritizationService } from '../services/prioritization.service';
import { UpdatePrioritizationSettingsDto } from '../dto/update-prioritization-settings.dto';
import { GetTasksQueryDto, TaskSortField, SortOrder } from '../dto/get-tasks-query.dto';
import { CreateTaskDto } from '../dto/create-task.dto';
import { UpdateTaskDto } from '../dto/update-task.dto';
import { BulkUpdateTasksDto } from '../dto/bulk-update-tasks.dto';
import { PaginatedTasksResponseDto } from '../dto/paginated-tasks-response.dto';
import { DEFAULT_PRIORITIZATION_SETTINGS } from '../interfaces/prioritization.interface';

describe('TasksController', () => {
  let controller: TasksController;
  let tasksService: jest.Mocked<TasksService>;
  let prioritizationService: jest.Mocked<PrioritizationService>;

  const mockWorkspaceId = 'workspace-123';
  const mockTaskId = 'task-123';

  const mockTask = {
    id: mockTaskId,
    workspaceId: mockWorkspaceId,
    integrationId: 'integration-123',
    externalId: 'ext-123',
    title: 'Test Task',
    description: 'Test Description',
    status: 'todo',
    priority: 'high',
    priorityScore: 75.5,
    assigneeId: 'user-123',
    assigneeName: 'John Doe',
    dueDate: new Date('2024-12-31'),
    estimatedMinutes: 60,
    tags: ['urgent'],
    projectName: 'Test Project',
    sourceUrl: 'https://example.com/task/123',
    metadata: {},
    syncStatus: 'synced',
    createdAt: new Date(),
    updatedAt: new Date(),
    lastSyncAt: new Date(),
  };

  beforeEach(async () => {
    const mockTasksService = {
      getTasks: jest.fn(),
      searchTasks: jest.fn(),
      getTask: jest.fn(),
      createTask: jest.fn(),
      updateTask: jest.fn(),
      deleteTask: jest.fn(),
      bulkUpdateTasks: jest.fn(),
      getTasksByPriority: jest.fn(),
      getWorkspacePrioritizationSettings: jest.fn(),
      updateWorkspacePrioritizationSettings: jest.fn(),
      updateWorkspacePriorityScores: jest.fn(),
      updateTaskPriorityScore: jest.fn(),
    };

    const mockPrioritizationService = {
      validateWeights: jest.fn(),
      normalizeWeights: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [TasksController],
      providers: [
        {
          provide: TasksService,
          useValue: mockTasksService,
        },
        {
          provide: PrioritizationService,
          useValue: mockPrioritizationService,
        },
      ],
    }).compile();

    controller = module.get<TasksController>(TasksController);
    tasksService = module.get(TasksService);
    prioritizationService = module.get(PrioritizationService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getTasks', () => {
    it('should return paginated tasks with filtering and sorting', async () => {
      const query: GetTasksQueryDto = { 
        limit: 10, 
        offset: 0,
        status: 'todo',
        sortBy: TaskSortField.PRIORITY_SCORE,
        sortOrder: SortOrder.DESC
      };

      const mockResponse: PaginatedTasksResponseDto = {
        tasks: [mockTask],
        total: 1,
        count: 1,
        offset: 0,
        limit: 10,
        hasMore: false,
      };

      tasksService.getTasks.mockResolvedValue(mockResponse);

      const result = await controller.getTasks(mockWorkspaceId, query);

      expect(tasksService.getTasks).toHaveBeenCalledWith(mockWorkspaceId, query);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty query parameters with defaults', async () => {
      const query: GetTasksQueryDto = {};
      const mockResponse: PaginatedTasksResponseDto = {
        tasks: [mockTask],
        total: 1,
        count: 1,
        offset: 0,
        limit: 50,
        hasMore: false,
      };

      tasksService.getTasks.mockResolvedValue(mockResponse);

      const result = await controller.getTasks(mockWorkspaceId, query);

      expect(tasksService.getTasks).toHaveBeenCalledWith(mockWorkspaceId, query);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('searchTasks', () => {
    it('should search tasks using full-text search', async () => {
      const searchQuery = 'bug fix';
      const query: GetTasksQueryDto = { limit: 10, offset: 0 };
      const mockResponse: PaginatedTasksResponseDto = {
        tasks: [mockTask],
        total: 1,
        count: 1,
        offset: 0,
        limit: 10,
        hasMore: false,
      };

      tasksService.searchTasks.mockResolvedValue(mockResponse);

      const result = await controller.searchTasks(mockWorkspaceId, searchQuery, query);

      expect(tasksService.searchTasks).toHaveBeenCalledWith(mockWorkspaceId, searchQuery, query);
      expect(result).toEqual(mockResponse);
    });

    it('should fallback to regular getTasks when search query is empty', async () => {
      const searchQuery = '';
      const query: GetTasksQueryDto = { limit: 10, offset: 0 };
      const mockResponse: PaginatedTasksResponseDto = {
        tasks: [mockTask],
        total: 1,
        count: 1,
        offset: 0,
        limit: 10,
        hasMore: false,
      };

      tasksService.getTasks.mockResolvedValue(mockResponse);

      const result = await controller.searchTasks(mockWorkspaceId, searchQuery, query);

      expect(tasksService.getTasks).toHaveBeenCalledWith(mockWorkspaceId, query);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('createTask', () => {
    it('should create a new task', async () => {
      const createTaskDto: CreateTaskDto = {
        title: 'New Task',
        description: 'Task description',
        status: 'todo',
        priority: 'high',
        sourceUrl: 'https://example.com/task/new',
      };

      const createdTask = { ...mockTask, ...createTaskDto };
      tasksService.createTask.mockResolvedValue(createdTask);

      const result = await controller.createTask(mockWorkspaceId, createTaskDto);

      expect(tasksService.createTask).toHaveBeenCalledWith(mockWorkspaceId, createTaskDto);
      expect(result).toEqual(createdTask);
    });
  });

  describe('getTask', () => {
    it('should return a specific task', async () => {
      tasksService.getTask.mockResolvedValue(mockTask);

      const result = await controller.getTask(mockWorkspaceId, mockTaskId);

      expect(tasksService.getTask).toHaveBeenCalledWith(mockWorkspaceId, mockTaskId);
      expect(result).toEqual(mockTask);
    });

    it('should throw NotFoundException when task not found', async () => {
      tasksService.getTask.mockRejectedValue(new NotFoundException('Task not found'));

      await expect(controller.getTask(mockWorkspaceId, 'non-existent')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('updateTask', () => {
    it('should update a task', async () => {
      const updateTaskDto: UpdateTaskDto = {
        title: 'Updated Task',
        status: 'in_progress',
      };

      const updatedTask = { ...mockTask, ...updateTaskDto };
      tasksService.updateTask.mockResolvedValue(updatedTask);

      const result = await controller.updateTask(mockWorkspaceId, mockTaskId, updateTaskDto);

      expect(tasksService.updateTask).toHaveBeenCalledWith(mockWorkspaceId, mockTaskId, updateTaskDto);
      expect(result).toEqual(updatedTask);
    });
  });

  describe('deleteTask', () => {
    it('should delete a task', async () => {
      tasksService.deleteTask.mockResolvedValue(undefined);

      await controller.deleteTask(mockWorkspaceId, mockTaskId);

      expect(tasksService.deleteTask).toHaveBeenCalledWith(mockWorkspaceId, mockTaskId);
    });
  });

  describe('bulkUpdateTasks', () => {
    it('should bulk update multiple tasks', async () => {
      const bulkUpdateDto: BulkUpdateTasksDto = {
        taskIds: ['task-1', 'task-2'],
        updates: {
          status: 'done',
          assigneeId: 'user-456',
        },
      };

      const mockResponse = {
        updated: 2,
        tasks: [mockTask, { ...mockTask, id: 'task-2' }],
      };

      tasksService.bulkUpdateTasks.mockResolvedValue(mockResponse);

      const result = await controller.bulkUpdateTasks(mockWorkspaceId, bulkUpdateDto);

      expect(tasksService.bulkUpdateTasks).toHaveBeenCalledWith(mockWorkspaceId, bulkUpdateDto);
      expect(result).toEqual(mockResponse);
    });

    it('should handle bulk update errors', async () => {
      const bulkUpdateDto: BulkUpdateTasksDto = {
        taskIds: ['task-1', 'non-existent'],
        updates: {
          status: 'done',
        },
      };

      tasksService.bulkUpdateTasks.mockRejectedValue(
        new BadRequestException('Some tasks not found'),
      );

      await expect(controller.bulkUpdateTasks(mockWorkspaceId, bulkUpdateDto)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('getPrioritizationSettings', () => {
    it('should return workspace prioritization settings', async () => {
      const mockSettings = DEFAULT_PRIORITIZATION_SETTINGS;

      tasksService.getWorkspacePrioritizationSettings.mockResolvedValue(mockSettings);

      const result = await controller.getPrioritizationSettings(mockWorkspaceId);

      expect(tasksService.getWorkspacePrioritizationSettings).toHaveBeenCalledWith(mockWorkspaceId);
      expect(result).toEqual(mockSettings);
    });
  });

  describe('updatePrioritizationSettings', () => {
    it('should update prioritization settings with valid weights', async () => {
      const updateDto: UpdatePrioritizationSettingsDto = {
        weights: {
          dueDateProximity: 0.5,
          effortEstimate: 0.2,
          businessImpact: 0.2,
          contextSwitching: 0.1,
        },
        maxScore: 100,
      };

      const updatedSettings = { ...DEFAULT_PRIORITIZATION_SETTINGS, ...updateDto };

      prioritizationService.validateWeights.mockReturnValue(true);
      tasksService.updateWorkspacePrioritizationSettings.mockResolvedValue(undefined);
      tasksService.getWorkspacePrioritizationSettings.mockResolvedValue(updatedSettings);

      const result = await controller.updatePrioritizationSettings(mockWorkspaceId, updateDto);

      expect(prioritizationService.validateWeights).toHaveBeenCalledWith(updateDto.weights);
      expect(tasksService.updateWorkspacePrioritizationSettings).toHaveBeenCalledWith(
        mockWorkspaceId,
        updateDto,
      );
      expect(tasksService.getWorkspacePrioritizationSettings).toHaveBeenCalledWith(mockWorkspaceId);
      expect(result).toEqual({
        message: 'Prioritization settings updated successfully',
        settings: updatedSettings,
      });
    });

    it('should normalize weights when they are invalid', async () => {
      const updateDto: UpdatePrioritizationSettingsDto = {
        weights: {
          dueDateProximity: 0.6,
          effortEstimate: 0.6,
          businessImpact: 0.6,
          contextSwitching: 0.6,
        },
      };

      const normalizedWeights = {
        dueDateProximity: 0.25,
        effortEstimate: 0.25,
        businessImpact: 0.25,
        contextSwitching: 0.25,
      };

      const updatedSettings = { ...DEFAULT_PRIORITIZATION_SETTINGS, weights: normalizedWeights };

      prioritizationService.validateWeights.mockReturnValue(false);
      prioritizationService.normalizeWeights.mockReturnValue(normalizedWeights);
      tasksService.updateWorkspacePrioritizationSettings.mockResolvedValue(undefined);
      tasksService.getWorkspacePrioritizationSettings.mockResolvedValue(updatedSettings);

      const result = await controller.updatePrioritizationSettings(mockWorkspaceId, updateDto);

      expect(prioritizationService.validateWeights).toHaveBeenCalledWith(updateDto.weights);
      expect(prioritizationService.normalizeWeights).toHaveBeenCalledWith(updateDto.weights);
      expect(tasksService.updateWorkspacePrioritizationSettings).toHaveBeenCalledWith(
        mockWorkspaceId,
        { ...updateDto, weights: normalizedWeights },
      );
      expect(result.message).toBe('Prioritization settings updated successfully');
    });

    it('should handle updates without weights', async () => {
      const updateDto: UpdatePrioritizationSettingsDto = {
        maxScore: 150,
        dueDateUrgencyDays: 10,
      };

      const updatedSettings = { ...DEFAULT_PRIORITIZATION_SETTINGS, ...updateDto };

      tasksService.updateWorkspacePrioritizationSettings.mockResolvedValue(undefined);
      tasksService.getWorkspacePrioritizationSettings.mockResolvedValue(updatedSettings);

      const result = await controller.updatePrioritizationSettings(mockWorkspaceId, updateDto);

      expect(prioritizationService.validateWeights).not.toHaveBeenCalled();
      expect(tasksService.updateWorkspacePrioritizationSettings).toHaveBeenCalledWith(
        mockWorkspaceId,
        updateDto,
      );
      expect(result.message).toBe('Prioritization settings updated successfully');
    });
  });

  describe('recalculatePriorityScores', () => {
    it('should recalculate priority scores for all tasks in workspace', async () => {
      const mockSettings = DEFAULT_PRIORITIZATION_SETTINGS;

      tasksService.getWorkspacePrioritizationSettings.mockResolvedValue(mockSettings);
      tasksService.updateWorkspacePriorityScores.mockResolvedValue(undefined);

      const result = await controller.recalculatePriorityScores(mockWorkspaceId);

      expect(tasksService.getWorkspacePrioritizationSettings).toHaveBeenCalledWith(mockWorkspaceId);
      expect(tasksService.updateWorkspacePriorityScores).toHaveBeenCalledWith(
        mockWorkspaceId,
        mockSettings,
      );
      expect(result).toEqual({
        message: 'Priority scores recalculated successfully',
      });
    });
  });

  describe('updateTaskPriorityScore', () => {
    it('should update priority score for a specific task', async () => {
      const mockSettings = DEFAULT_PRIORITIZATION_SETTINGS;
      const updatedTask = { ...mockTask, priorityScore: 90.0 };

      tasksService.getWorkspacePrioritizationSettings.mockResolvedValue(mockSettings);
      tasksService.updateTaskPriorityScore.mockResolvedValue(updatedTask);

      const result = await controller.updateTaskPriorityScore(mockWorkspaceId, mockTaskId);

      expect(tasksService.getWorkspacePrioritizationSettings).toHaveBeenCalledWith(mockWorkspaceId);
      expect(tasksService.updateTaskPriorityScore).toHaveBeenCalledWith(mockTaskId, mockSettings);
      expect(result).toEqual({
        message: 'Task priority score updated successfully',
        task: updatedTask,
      });
    });
  });
});