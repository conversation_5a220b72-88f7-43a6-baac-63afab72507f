import { IntegrationConfig, OAuthCredentials, ExternalTask, TaskUpdate, CreateTaskRequest, WebhookConfig, AuthResult } from '../types';

/**
 * Base interface that all integration adapters must implement
 * Provides standardized methods for authentication, task management, and synchronization
 */
export interface IntegrationAdapter {
  /**
   * Authenticate with the external service using OAuth credentials
   * @param credentials OAuth credentials for authentication
   * @returns Authentication result with success status and user info
   */
  authenticate(credentials: OAuthCredentials): Promise<AuthResult>;

  /**
   * Fetch tasks from the external service based on configuration
   * @param config Integration configuration including filters and mappings
   * @returns Array of external tasks
   */
  fetchTasks(config: IntegrationConfig): Promise<ExternalTask[]>;

  /**
   * Update a task in the external service
   * @param taskId External task identifier
   * @param updates Task updates to apply
   * @returns Updated external task
   */
  updateTask(taskId: string, updates: TaskUpdate): Promise<ExternalTask>;

  /**
   * Create a new task in the external service
   * @param task Task creation request
   * @returns Created external task
   */
  createTask(task: CreateTaskRequest): Promise<ExternalTask>;

  /**
   * Set up webhook for real-time updates (optional)
   * @param webhookUrl URL to receive webhook notifications
   * @returns Webhook configuration
   */
  setupWebhook?(webhookUrl: string): Promise<WebhookConfig>;

  /**
   * Validate that the provided credentials are still valid
   * @param credentials Credentials to validate
   * @returns True if credentials are valid
   */
  validateCredentials(credentials: any): Promise<boolean>;

  /**
   * Get the provider name for this adapter
   * @returns Provider identifier
   */
  getProvider(): string;

  /**
   * Check if the adapter supports two-way sync
   * @returns True if two-way sync is supported
   */
  supportsTwoWaySync(): boolean;

  /**
   * Check if the adapter supports webhook notifications
   * @returns True if webhooks are supported
   */
  supportsWebhooks(): boolean;
}