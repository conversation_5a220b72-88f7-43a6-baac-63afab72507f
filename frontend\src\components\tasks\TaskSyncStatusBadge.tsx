import { Badge } from '@/components/ui/badge'
import { SyncStatus } from '@/types/task'
import { cn } from '@/lib/utils'
import { CheckCircle, Clock, AlertCircle, AlertTriangle } from 'lucide-react'

interface TaskSyncStatusBadgeProps {
  syncStatus: SyncStatus
  className?: string
  showIcon?: boolean
}

const syncStatusConfig = {
  [SyncStatus.SYNCED]: {
    label: 'Synced',
    variant: 'default' as const,
    className: 'bg-green-100 text-green-800 hover:bg-green-200',
    icon: CheckCircle
  },
  [SyncStatus.PENDING]: {
    label: 'Pending',
    variant: 'secondary' as const,
    className: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
    icon: Clock
  },
  [SyncStatus.ERROR]: {
    label: 'Error',
    variant: 'destructive' as const,
    className: 'bg-red-100 text-red-800 hover:bg-red-200',
    icon: AlertCircle
  },
  [SyncStatus.CONFLICT]: {
    label: 'Conflict',
    variant: 'destructive' as const,
    className: 'bg-orange-100 text-orange-800 hover:bg-orange-200',
    icon: AlertTriangle
  }
}

export function TaskSyncStatusBadge({ 
  syncStatus, 
  className,
  showIcon = true 
}: TaskSyncStatusBadgeProps) {
  const config = syncStatusConfig[syncStatus]
  const Icon = config.icon
  
  return (
    <Badge 
      variant={config.variant}
      className={cn(config.className, 'flex items-center gap-1', className)}
    >
      {showIcon && <Icon className="h-3 w-3" />}
      {config.label}
    </Badge>
  )
}