import { apiService } from './api'
import { DailyPlan, CreateDailyPlanRequest, UpdateDailyPlanTaskRequest } from '@/types/task'

export const dailyPlanService = {
  async getDailyPlan(workspaceId: string, date: string): Promise<DailyPlan | null> {
    try {
      const response = await apiService.get(`/workspaces/${workspaceId}/daily-plans/${date}`)
      return response.data
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null
      }
      throw error
    }
  },

  async createDailyPlan(workspaceId: string, data: CreateDailyPlanRequest): Promise<DailyPlan> {
    const response = await apiService.post(`/workspaces/${workspaceId}/daily-plans`, data)
    return response.data
  },

  async updateDailyPlan(workspaceId: string, planId: string, data: CreateDailyPlanRequest): Promise<DailyPlan> {
    const response = await apiService.put(`/workspaces/${workspaceId}/daily-plans/${planId}`, data)
    return response.data
  },

  async updateDailyPlanTask(
    workspaceId: string, 
    planId: string, 
    taskId: string, 
    data: UpdateDailyPlanTaskRequest
  ): Promise<void> {
    await apiService.patch(`/workspaces/${workspaceId}/daily-plans/${planId}/tasks/${taskId}`, data)
  },

  async completeDailyPlanTask(workspaceId: string, planId: string, taskId: string): Promise<void> {
    await apiService.patch(`/workspaces/${workspaceId}/daily-plans/${planId}/tasks/${taskId}/complete`)
  },

  async removeDailyPlanTask(workspaceId: string, planId: string, taskId: string): Promise<void> {
    await apiService.delete(`/workspaces/${workspaceId}/daily-plans/${planId}/tasks/${taskId}`)
  }
}