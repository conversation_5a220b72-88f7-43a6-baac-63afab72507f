import { BaseIntegrationAdapter } from '../base-integration.adapter';
import {
  IntegrationProvider,
  IntegrationConfig,
  OAuthCredentials,
  ExternalTask,
  TaskUpdate,
  CreateTaskRequest,
  AuthResult,
} from '../../types';

// Concrete implementation for testing
class TestIntegrationAdapter extends BaseIntegrationAdapter {
  constructor() {
    super(IntegrationProvider.ASANA, 'https://api.test.com', 'v1');
  }

  async authenticate(credentials: OAuthCredentials): Promise<AuthResult> {
    return { success: true };
  }

  async fetchTasks(config: IntegrationConfig): Promise<ExternalTask[]> {
    return [];
  }

  async updateTask(taskId: string, updates: TaskUpdate): Promise<ExternalTask> {
    return {} as ExternalTask;
  }

  async createTask(task: CreateTaskRequest): Promise<ExternalTask> {
    return {} as ExternalTask;
  }

  async validateCredentials(credentials: any): Promise<boolean> {
    return true;
  }
}

describe('BaseIntegrationAdapter', () => {
  let adapter: TestIntegrationAdapter;

  beforeEach(() => {
    adapter = new TestIntegrationAdapter();
  });

  it('should be defined', () => {
    expect(adapter).toBeDefined();
  });

  describe('getProvider', () => {
    it('should return the provider name', () => {
      expect(adapter.getProvider()).toBe(IntegrationProvider.ASANA);
    });
  });

  describe('supportsTwoWaySync', () => {
    it('should return true by default', () => {
      expect(adapter.supportsTwoWaySync()).toBe(true);
    });
  });

  describe('supportsWebhooks', () => {
    it('should return false by default', () => {
      expect(adapter.supportsWebhooks()).toBe(false);
    });
  });

  describe('setupWebhook', () => {
    it('should throw error for unsupported webhooks', async () => {
      await expect(adapter.setupWebhook?.('https://webhook.url'))
        .rejects.toThrow('Webhooks not supported for asana');
    });
  });

  describe('handleApiError', () => {
    it('should handle authentication errors', () => {
      const error = {
        response: { status: 401 },
      };

      const syncError = (adapter as any).handleApiError(error, 'test operation');

      expect(syncError.type).toBe('AUTH_ERROR');
      expect(syncError.retryable).toBe(false);
      expect(syncError.message).toBe('Authentication failed. Please reconnect your account.');
    });

    it('should handle rate limit errors', () => {
      const error = {
        response: { 
          status: 429,
          headers: { 'retry-after': '60' },
        },
      };

      const syncError = (adapter as any).handleApiError(error, 'test operation');

      expect(syncError.type).toBe('RATE_LIMIT_ERROR');
      expect(syncError.retryable).toBe(true);
      expect(syncError.retryAfter).toBe(60000);
    });

    it('should handle network errors', () => {
      const error = {
        code: 'ENOTFOUND',
      };

      const syncError = (adapter as any).handleApiError(error, 'test operation');

      expect(syncError.type).toBe('NETWORK_ERROR');
      expect(syncError.retryable).toBe(true);
      expect(syncError.retryAfter).toBe(30000);
    });

    it('should handle validation errors', () => {
      const error = {
        response: { 
          status: 400,
          data: { message: 'Invalid request data' },
        },
      };

      const syncError = (adapter as any).handleApiError(error, 'test operation');

      expect(syncError.type).toBe('VALIDATION_ERROR');
      expect(syncError.retryable).toBe(false);
      expect(syncError.message).toBe('Invalid request data');
    });

    it('should handle generic API errors', () => {
      const error = {
        message: 'Something went wrong',
      };

      const syncError = (adapter as any).handleApiError(error, 'test operation');

      expect(syncError.type).toBe('API_ERROR');
      expect(syncError.retryable).toBe(true);
      expect(syncError.retryAfter).toBe(60000);
    });
  });

  describe('buildHeaders', () => {
    it('should build headers with access token', () => {
      const credentials: OAuthCredentials = {
        accessToken: 'test-token',
        tokenType: 'Bearer',
      };

      const headers = (adapter as any).buildHeaders(credentials);

      expect(headers).toEqual({
        'Content-Type': 'application/json',
        'User-Agent': 'TaskUnify/1.0',
        'Authorization': 'Bearer test-token',
      });
    });

    it('should build headers without token type', () => {
      const credentials: OAuthCredentials = {
        accessToken: 'test-token',
      };

      const headers = (adapter as any).buildHeaders(credentials);

      expect(headers.Authorization).toBe('Bearer test-token');
    });

    it('should build headers without access token', () => {
      const credentials: OAuthCredentials = {
        accessToken: '',
      };

      const headers = (adapter as any).buildHeaders(credentials);

      expect(headers.Authorization).toBeUndefined();
      expect(headers['Content-Type']).toBe('application/json');
      expect(headers['User-Agent']).toBe('TaskUnify/1.0');
    });
  });

  describe('isCredentialsExpired', () => {
    it('should return false when no expiration set', () => {
      const credentials: OAuthCredentials = {
        accessToken: 'test-token',
      };

      const isExpired = (adapter as any).isCredentialsExpired(credentials);
      expect(isExpired).toBe(false);
    });

    it('should return false when not expired', () => {
      const futureDate = new Date();
      futureDate.setHours(futureDate.getHours() + 1);

      const credentials: OAuthCredentials = {
        accessToken: 'test-token',
        expiresAt: futureDate,
      };

      const isExpired = (adapter as any).isCredentialsExpired(credentials);
      expect(isExpired).toBe(false);
    });

    it('should return true when expired', () => {
      const pastDate = new Date();
      pastDate.setHours(pastDate.getHours() - 1);

      const credentials: OAuthCredentials = {
        accessToken: 'test-token',
        expiresAt: pastDate,
      };

      const isExpired = (adapter as any).isCredentialsExpired(credentials);
      expect(isExpired).toBe(true);
    });

    it('should return true when expiring within buffer time', () => {
      const nearFutureDate = new Date();
      nearFutureDate.setMinutes(nearFutureDate.getMinutes() + 2); // Within 5 minute buffer

      const credentials: OAuthCredentials = {
        accessToken: 'test-token',
        expiresAt: nearFutureDate,
      };

      const isExpired = (adapter as any).isCredentialsExpired(credentials);
      expect(isExpired).toBe(true);
    });
  });

  describe('normalizeTaskStatus', () => {
    it('should normalize common todo statuses', () => {
      expect((adapter as any).normalizeTaskStatus('open')).toBe('todo');
      expect((adapter as any).normalizeTaskStatus('new')).toBe('todo');
      expect((adapter as any).normalizeTaskStatus('to do')).toBe('todo');
      expect((adapter as any).normalizeTaskStatus('pending')).toBe('todo');
      expect((adapter as any).normalizeTaskStatus('backlog')).toBe('todo');
    });

    it('should normalize common in-progress statuses', () => {
      expect((adapter as any).normalizeTaskStatus('in progress')).toBe('in_progress');
      expect((adapter as any).normalizeTaskStatus('in-progress')).toBe('in_progress');
      expect((adapter as any).normalizeTaskStatus('active')).toBe('in_progress');
      expect((adapter as any).normalizeTaskStatus('working')).toBe('in_progress');
      expect((adapter as any).normalizeTaskStatus('started')).toBe('in_progress');
    });

    it('should normalize common done statuses', () => {
      expect((adapter as any).normalizeTaskStatus('done')).toBe('done');
      expect((adapter as any).normalizeTaskStatus('completed')).toBe('done');
      expect((adapter as any).normalizeTaskStatus('finished')).toBe('done');
      expect((adapter as any).normalizeTaskStatus('resolved')).toBe('done');
      expect((adapter as any).normalizeTaskStatus('closed')).toBe('done');
    });

    it('should normalize common cancelled statuses', () => {
      expect((adapter as any).normalizeTaskStatus('cancelled')).toBe('cancelled');
      expect((adapter as any).normalizeTaskStatus('canceled')).toBe('cancelled');
      expect((adapter as any).normalizeTaskStatus('rejected')).toBe('cancelled');
      expect((adapter as any).normalizeTaskStatus('abandoned')).toBe('cancelled');
    });

    it('should default to todo for unknown statuses', () => {
      expect((adapter as any).normalizeTaskStatus('unknown')).toBe('todo');
      expect((adapter as any).normalizeTaskStatus('custom-status')).toBe('todo');
    });

    it('should handle case insensitive matching', () => {
      expect((adapter as any).normalizeTaskStatus('DONE')).toBe('done');
      expect((adapter as any).normalizeTaskStatus('In Progress')).toBe('in_progress');
      expect((adapter as any).normalizeTaskStatus('CANCELLED')).toBe('cancelled');
    });
  });

  describe('normalizeTaskPriority', () => {
    it('should normalize numeric priorities', () => {
      expect((adapter as any).normalizeTaskPriority(4)).toBe('urgent');
      expect((adapter as any).normalizeTaskPriority(3)).toBe('high');
      expect((adapter as any).normalizeTaskPriority(2)).toBe('medium');
      expect((adapter as any).normalizeTaskPriority(1)).toBe('low');
      expect((adapter as any).normalizeTaskPriority(0)).toBe('low');
    });

    it('should normalize string priorities', () => {
      expect((adapter as any).normalizeTaskPriority('urgent')).toBe('urgent');
      expect((adapter as any).normalizeTaskPriority('critical')).toBe('urgent');
      expect((adapter as any).normalizeTaskPriority('highest')).toBe('urgent');
      expect((adapter as any).normalizeTaskPriority('high')).toBe('high');
      expect((adapter as any).normalizeTaskPriority('important')).toBe('high');
      expect((adapter as any).normalizeTaskPriority('medium')).toBe('medium');
      expect((adapter as any).normalizeTaskPriority('normal')).toBe('medium');
      expect((adapter as any).normalizeTaskPriority('low')).toBe('low');
      expect((adapter as any).normalizeTaskPriority('minor')).toBe('low');
      expect((adapter as any).normalizeTaskPriority('lowest')).toBe('low');
    });

    it('should handle numeric strings', () => {
      expect((adapter as any).normalizeTaskPriority('4')).toBe('urgent');
      expect((adapter as any).normalizeTaskPriority('3')).toBe('high');
      expect((adapter as any).normalizeTaskPriority('2')).toBe('medium');
      expect((adapter as any).normalizeTaskPriority('1')).toBe('low');
      expect((adapter as any).normalizeTaskPriority('0')).toBe('low');
    });

    it('should default to medium for unknown priorities', () => {
      expect((adapter as any).normalizeTaskPriority('unknown')).toBe('medium');
      expect((adapter as any).normalizeTaskPriority('custom')).toBe('medium');
    });

    it('should handle case insensitive matching', () => {
      expect((adapter as any).normalizeTaskPriority('URGENT')).toBe('urgent');
      expect((adapter as any).normalizeTaskPriority('High')).toBe('high');
      expect((adapter as any).normalizeTaskPriority('MEDIUM')).toBe('medium');
    });
  });

  describe('validateTaskData', () => {
    it('should validate required task fields', () => {
      const validTask: Partial<ExternalTask> = {
        id: 'task-123',
        title: 'Test Task',
        sourceUrl: 'https://example.com/task/123',
      };

      expect(() => (adapter as any).validateTaskData(validTask)).not.toThrow();
    });

    it('should throw error for missing ID', () => {
      const invalidTask: Partial<ExternalTask> = {
        title: 'Test Task',
        sourceUrl: 'https://example.com/task/123',
      };

      expect(() => (adapter as any).validateTaskData(invalidTask))
        .toThrow('Task ID is required');
    });

    it('should throw error for missing title', () => {
      const invalidTask: Partial<ExternalTask> = {
        id: 'task-123',
        sourceUrl: 'https://example.com/task/123',
      };

      expect(() => (adapter as any).validateTaskData(invalidTask))
        .toThrow('Task title is required');
    });

    it('should throw error for empty title', () => {
      const invalidTask: Partial<ExternalTask> = {
        id: 'task-123',
        title: '   ',
        sourceUrl: 'https://example.com/task/123',
      };

      expect(() => (adapter as any).validateTaskData(invalidTask))
        .toThrow('Task title is required');
    });

    it('should throw error for missing source URL', () => {
      const invalidTask: Partial<ExternalTask> = {
        id: 'task-123',
        title: 'Test Task',
      };

      expect(() => (adapter as any).validateTaskData(invalidTask))
        .toThrow('Task source URL is required');
    });
  });

  describe('sanitizeTaskData', () => {
    it('should sanitize task data', () => {
      const dirtyTask: Partial<ExternalTask> = {
        title: '  Test Task  ',
        description: '  Task description  ',
        tags: ['tag1', '', '  tag2  ', ''],
        assigneeName: '  John Doe  ',
        projectName: '  Project Name  ',
      };

      const sanitized = (adapter as any).sanitizeTaskData(dirtyTask);

      expect(sanitized).toEqual({
        title: 'Test Task',
        description: 'Task description',
        tags: ['tag1', '  tag2  '],
        assigneeName: 'John Doe',
        projectName: 'Project Name',
      });
    });

    it('should handle undefined description', () => {
      const task: Partial<ExternalTask> = {
        title: 'Test Task',
        description: undefined,
      };

      const sanitized = (adapter as any).sanitizeTaskData(task);

      expect(sanitized.description).toBeUndefined();
    });

    it('should handle empty description', () => {
      const task: Partial<ExternalTask> = {
        title: 'Test Task',
        description: '   ',
      };

      const sanitized = (adapter as any).sanitizeTaskData(task);

      expect(sanitized.description).toBeUndefined();
    });

    it('should handle undefined tags', () => {
      const task: Partial<ExternalTask> = {
        title: 'Test Task',
        tags: undefined,
      };

      const sanitized = (adapter as any).sanitizeTaskData(task);

      expect(sanitized.tags).toEqual([]);
    });

    it('should handle undefined optional fields', () => {
      const task: Partial<ExternalTask> = {
        title: 'Test Task',
        assigneeName: undefined,
        projectName: undefined,
      };

      const sanitized = (adapter as any).sanitizeTaskData(task);

      expect(sanitized.assigneeName).toBeUndefined();
      expect(sanitized.projectName).toBeUndefined();
    });
  });
});