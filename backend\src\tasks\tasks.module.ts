import { Module } from '@nestjs/common';
import { TasksService } from './services/tasks.service';
import { PrioritizationService } from './services/prioritization.service';
import { DailyPlanningService } from './services/daily-planning.service';
import { QuickTaskService } from './services/quick-task.service';
import { ProgressTrackingService } from './services/progress-tracking.service';
import { ReportExportService } from './services/report-export.service';
import { TasksController } from './tasks.controller';
import { DatabaseModule } from '../database/database.module';
import { IntegrationsModule } from '../integrations/integrations.module';

@Module({
  imports: [DatabaseModule, IntegrationsModule],
  controllers: [TasksController],
  providers: [
    TasksService, 
    PrioritizationService, 
    DailyPlanningService, 
    QuickTaskService,
    ProgressTrackingService,
    ReportExportService,
  ],
  exports: [TasksService, PrioritizationService, QuickTaskService, ProgressTrackingService],
})
export class TasksModule {}