import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { TaskPriorityBadge } from '../TaskPriorityBadge'
import { TaskPriority } from '@/types/task'

describe('TaskPriorityBadge', () => {
  it('should render LOW priority correctly', () => {
    render(<TaskPriorityBadge priority={TaskPriority.LOW} />)
    
    const badge = screen.getByText('Low')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass('bg-gray-100', 'text-gray-600')
  })

  it('should render MEDIUM priority correctly', () => {
    render(<TaskPriorityBadge priority={TaskPriority.MEDIUM} />)
    
    const badge = screen.getByText('Medium')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass('bg-yellow-100', 'text-yellow-800')
  })

  it('should render HIGH priority correctly', () => {
    render(<TaskPriorityBadge priority={TaskPriority.HIGH} />)
    
    const badge = screen.getByText('High')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass('bg-orange-100', 'text-orange-800')
  })

  it('should render URGENT priority correctly', () => {
    render(<TaskPriorityBadge priority={TaskPriority.URGENT} />)
    
    const badge = screen.getByText('Urgent')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass('bg-red-100', 'text-red-800')
  })

  it('should show priority score when provided', () => {
    render(<TaskPriorityBadge priority={TaskPriority.HIGH} priorityScore={85.7} />)
    
    expect(screen.getByText('High')).toBeInTheDocument()
    expect(screen.getByText('(86)')).toBeInTheDocument()
  })

  it('should show icon when showIcon is true', () => {
    render(<TaskPriorityBadge priority={TaskPriority.URGENT} showIcon />)
    
    const badge = screen.getByText('Urgent')
    expect(badge.querySelector('svg')).toBeInTheDocument()
  })

  it('should have title with priority score', () => {
    render(<TaskPriorityBadge priority={TaskPriority.HIGH} priorityScore={75.5} />)
    
    const badge = screen.getByText('High')
    expect(badge).toHaveAttribute('title', 'Priority Score: 75.5')
  })

  it('should default to MEDIUM priority when none provided', () => {
    render(<TaskPriorityBadge />)
    
    expect(screen.getByText('Medium')).toBeInTheDocument()
  })
})