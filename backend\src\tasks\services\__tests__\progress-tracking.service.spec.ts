import { Test, TestingModule } from '@nestjs/testing';
import { ProgressTrackingService } from '../progress-tracking.service';
import { DatabaseService } from '../../../database/database.service';
import { ProgressReportFilters } from '../../interfaces/progress-tracking.interface';

describe('ProgressTrackingService', () => {
  let service: ProgressTrackingService;
  let prismaService: DatabaseService;

  const mockPrismaService = {
    task: {
      count: jest.fn(),
      groupBy: jest.fn(),
      findMany: jest.fn(),
    },
    integration: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProgressTrackingService,
        {
          provide: DatabaseService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<ProgressTrackingService>(ProgressTrackingService);
    prismaService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getCompletionRateReport', () => {
    it('should generate completion rate report with overall statistics', async () => {
      const workspaceId = 'workspace-1';
      const filters: ProgressReportFilters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
      };

      // Mock total and completed task counts
      mockPrismaService.task.count
        .mockResolvedValueOnce(100) // total tasks
        .mockResolvedValueOnce(75); // completed tasks

      // Mock groupBy for sources
      mockPrismaService.task.groupBy.mockResolvedValueOnce([
        { integrationId: 'int-1', _count: { _all: 50 } },
        { integrationId: 'int-2', _count: { _all: 50 } },
      ]);

      // Mock integration details
      mockPrismaService.integration.findUnique
        .mockResolvedValueOnce({ provider: 'asana', name: 'Asana Integration' })
        .mockResolvedValueOnce({ provider: 'trello', name: 'Trello Integration' });

      // Mock completed counts for sources
      mockPrismaService.task.count
        .mockResolvedValueOnce(40) // completed for int-1
        .mockResolvedValueOnce(35); // completed for int-2

      // Mock groupBy for projects
      mockPrismaService.task.groupBy.mockResolvedValueOnce([
        { projectName: 'Project A', _count: { _all: 60 } },
        { projectName: 'Project B', _count: { _all: 40 } },
      ]);

      // Mock completed counts for projects
      mockPrismaService.task.count
        .mockResolvedValueOnce(45) // completed for Project A
        .mockResolvedValueOnce(30); // completed for Project B

      const result = await service.getCompletionRateReport(workspaceId, filters);

      expect(result.overall.totalTasks).toBe(100);
      expect(result.overall.completedTasks).toBe(75);
      expect(result.overall.completionRate).toBe(75);

      expect(result.bySource).toHaveLength(2);
      expect(result.bySource[0]).toEqual({
        source: 'asana',
        sourceName: 'Asana Integration',
        totalTasks: 50,
        completedTasks: 40,
        completionRate: 80,
      });

      expect(result.byProject).toHaveLength(2);
      expect(result.byProject[0]).toEqual({
        project: 'Project A',
        totalTasks: 60,
        completedTasks: 45,
        completionRate: 75,
      });

      expect(result.filters).toEqual(filters);
      expect(result.generatedAt).toBeInstanceOf(Date);
    });

    it('should handle empty results gracefully', async () => {
      const workspaceId = 'workspace-1';
      const filters: ProgressReportFilters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
      };

      mockPrismaService.task.count.mockResolvedValue(0);
      mockPrismaService.task.groupBy.mockResolvedValue([]);

      const result = await service.getCompletionRateReport(workspaceId, filters);

      expect(result.overall.totalTasks).toBe(0);
      expect(result.overall.completedTasks).toBe(0);
      expect(result.overall.completionRate).toBe(0);
      expect(result.bySource).toHaveLength(0);
      expect(result.byProject).toHaveLength(0);
    });

    it('should apply filters correctly', async () => {
      const workspaceId = 'workspace-1';
      const filters: ProgressReportFilters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        source: 'asana',
        project: 'Project A',
        assignee: 'user-1',
      };

      mockPrismaService.task.count.mockResolvedValue(10);
      mockPrismaService.task.groupBy.mockResolvedValue([]);

      await service.getCompletionRateReport(workspaceId, filters);

      // Verify that the where clause includes all filters
      const expectedWhereClause = {
        workspaceId,
        createdAt: {
          gte: filters.startDate,
          lte: filters.endDate,
        },
        integration: { provider: 'asana' },
        projectName: 'Project A',
        assigneeId: 'user-1',
      };

      expect(mockPrismaService.task.count).toHaveBeenCalledWith({
        where: expectedWhereClause,
      });
    });
  });

  describe('getTaskAgingReport', () => {
    it('should generate task aging report with default age ranges', async () => {
      const workspaceId = 'workspace-1';
      const filters: ProgressReportFilters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
      };

      const mockTasks = [
        {
          id: 'task-1',
          title: 'Recent Task',
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
          dueDate: null,
          status: 'todo',
          projectName: 'Project A',
          assigneeName: 'John Doe',
          integration: { provider: 'asana', name: 'Asana Integration' },
        },
        {
          id: 'task-2',
          title: 'Old Task',
          createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000), // 45 days ago
          dueDate: new Date(),
          status: 'in_progress',
          projectName: 'Project B',
          assigneeName: 'Jane Smith',
          integration: { provider: 'trello', name: 'Trello Integration' },
        },
      ];

      mockPrismaService.task.findMany.mockResolvedValue(mockTasks);

      const result = await service.getTaskAgingReport(workspaceId, filters);

      expect(result.totalActiveTasks).toBe(2);
      expect(result.tasksByAgeRange).toHaveLength(3);

      // Check that tasks are grouped correctly
      const recentRange = result.tasksByAgeRange.find(r => r.ageRange.label === '0-7 days');
      const oldRange = result.tasksByAgeRange.find(r => r.ageRange.label === '30+ days');

      expect(recentRange?.taskCount).toBe(1);
      expect(recentRange?.tasks[0].title).toBe('Recent Task');

      expect(oldRange?.taskCount).toBe(1);
      expect(oldRange?.tasks[0].title).toBe('Old Task');
    });

    it('should use custom age ranges when provided', async () => {
      const workspaceId = 'workspace-1';
      const filters: ProgressReportFilters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
      };

      const customAgeRanges = [
        { label: '0-3 days', minDays: 0, maxDays: 3 },
        { label: '4-14 days', minDays: 4, maxDays: 14 },
        { label: '15+ days', minDays: 15, maxDays: null },
      ];

      mockPrismaService.task.findMany.mockResolvedValue([]);

      const result = await service.getTaskAgingReport(workspaceId, filters, customAgeRanges);

      expect(result.tasksByAgeRange).toHaveLength(3);
      expect(result.tasksByAgeRange[0].ageRange.label).toBe('0-3 days');
      expect(result.tasksByAgeRange[1].ageRange.label).toBe('4-14 days');
      expect(result.tasksByAgeRange[2].ageRange.label).toBe('15+ days');
    });

    it('should exclude completed tasks from aging report', async () => {
      const workspaceId = 'workspace-1';
      const filters: ProgressReportFilters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
      };

      await service.getTaskAgingReport(workspaceId, filters);

      expect(mockPrismaService.task.findMany).toHaveBeenCalledWith({
        where: {
          workspaceId,
          status: { not: 'done' },
        },
        select: expect.any(Object),
      });
    });
  });

  describe('getVelocityReport', () => {
    it('should generate velocity report with trend analysis', async () => {
      const workspaceId = 'workspace-1';
      const filters: ProgressReportFilters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
      };

      const mockCompletedTasks = [
        {
          id: 'task-1',
          updatedAt: new Date('2024-01-15'),
          estimatedMinutes: 60,
          integration: { provider: 'asana', name: 'Asana Integration' },
        },
        {
          id: 'task-2',
          updatedAt: new Date('2024-01-22'),
          estimatedMinutes: 120,
          integration: { provider: 'asana', name: 'Asana Integration' },
        },
      ];

      mockPrismaService.task.findMany.mockResolvedValue(mockCompletedTasks);
      mockPrismaService.task.groupBy.mockResolvedValue([
        {
          integrationId: 'int-1',
          _count: { _all: 2 },
          _sum: { estimatedMinutes: 180 },
        },
      ]);
      mockPrismaService.integration.findUnique.mockResolvedValue({
        provider: 'asana',
        name: 'Asana Integration',
      });

      const result = await service.getVelocityReport(workspaceId, filters, 4);

      expect(result.weeklyData).toHaveLength(4);
      expect(result.averageTasksPerWeek).toBeGreaterThanOrEqual(0);
      expect(result.trendAnalysis).toHaveProperty('direction');
      expect(result.trendAnalysis).toHaveProperty('percentage');
      expect(result.velocityBySource).toHaveLength(1);
      expect(result.velocityBySource[0]).toEqual({
        source: 'asana',
        sourceName: 'Asana Integration',
        totalTasksCompleted: 2,
        totalEstimatedMinutes: 180,
        averageTasksPerWeek: expect.any(Number),
      });
    });

    it('should calculate trend direction correctly', async () => {
      const workspaceId = 'workspace-1';
      const filters: ProgressReportFilters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
      };

      // Mock tasks distributed to show increasing trend
      const mockTasks = [];
      for (let i = 0; i < 20; i++) {
        const date = new Date('2024-01-01');
        date.setDate(date.getDate() + i * 7);
        
        // More tasks in recent weeks
        const taskCount = i < 10 ? 1 : 3;
        for (let j = 0; j < taskCount; j++) {
          mockTasks.push({
            id: `task-${i}-${j}`,
            updatedAt: date,
            estimatedMinutes: 60,
            integration: { provider: 'asana', name: 'Asana Integration' },
          });
        }
      }

      mockPrismaService.task.findMany.mockResolvedValue(mockTasks);
      mockPrismaService.task.groupBy.mockResolvedValue([]);

      const result = await service.getVelocityReport(workspaceId, filters, 12);

      // With more tasks in recent weeks, trend should be increasing
      expect(['increasing', 'decreasing', 'stable']).toContain(result.trendAnalysis.direction);
      expect(result.trendAnalysis.percentage).toBeGreaterThanOrEqual(0);
    });
  });

  describe('getReportFilterOptions', () => {
    it('should return available filter options', async () => {
      const workspaceId = 'workspace-1';

      mockPrismaService.integration.findMany.mockResolvedValue([
        { provider: 'asana', name: 'Asana Integration' },
        { provider: 'trello', name: 'Trello Integration' },
      ]);

      mockPrismaService.task.findMany
        .mockResolvedValueOnce([
          { projectName: 'Project A' },
          { projectName: 'Project B' },
        ])
        .mockResolvedValueOnce([
          { assigneeId: 'user-1', assigneeName: 'John Doe' },
          { assigneeId: 'user-2', assigneeName: 'Jane Smith' },
        ]);

      const result = await service.getReportFilterOptions(workspaceId);

      expect(result.sources).toEqual([
        { value: 'asana', label: 'Asana Integration' },
        { value: 'trello', label: 'Trello Integration' },
      ]);

      expect(result.projects).toEqual([
        { value: 'Project A', label: 'Project A' },
        { value: 'Project B', label: 'Project B' },
      ]);

      expect(result.assignees).toEqual([
        { value: 'user-1', label: 'John Doe' },
        { value: 'user-2', label: 'Jane Smith' },
      ]);
    });

    it('should handle empty filter options', async () => {
      const workspaceId = 'workspace-1';

      mockPrismaService.integration.findMany.mockResolvedValue([]);
      mockPrismaService.task.findMany.mockResolvedValue([]);

      const result = await service.getReportFilterOptions(workspaceId);

      expect(result.sources).toEqual([]);
      expect(result.projects).toEqual([]);
      expect(result.assignees).toEqual([]);
    });
  });
});