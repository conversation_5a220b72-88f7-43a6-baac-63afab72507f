import { useState } from 'react'
import { Trash2, Edit, CheckCircle, XCir<PERSON>, Clock, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { TaskStatus, TaskPriority, type BulkUpdateTasksRequest } from '@/types/task'
import { cn } from '@/lib/utils'

interface BulkOperationsProps {
  selectedTaskIds: string[]
  onBulkUpdate: (request: BulkUpdateTasksRequest) => Promise<void>
  onBulkDelete: (taskIds: string[]) => Promise<void>
  onClearSelection: () => void
  isLoading?: boolean
  className?: string
}

export function BulkOperations({
  selectedTaskIds,
  onBulkUpdate,
  onBulkDelete,
  onClearSelection,
  isLoading = false,
  className
}: BulkOperationsProps) {
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [updateData, setUpdateData] = useState<BulkUpdateTasksRequest['updates']>({})

  if (selectedTaskIds.length === 0) {
    return null
  }

  const handleBulkUpdate = async () => {
    // Filter out empty values and "keep" values
    const cleanUpdates = Object.entries(updateData).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== '' && value !== 'keep') {
        acc[key as keyof typeof updateData] = value
      }
      return acc
    }, {} as BulkUpdateTasksRequest['updates'])

    if (Object.keys(cleanUpdates).length === 0) {
      return
    }

    await onBulkUpdate({
      taskIds: selectedTaskIds,
      updates: cleanUpdates
    })

    setUpdateData({})
    setIsUpdateDialogOpen(false)
    onClearSelection()
  }

  const handleBulkDelete = async () => {
    await onBulkDelete(selectedTaskIds)
    setIsDeleteDialogOpen(false)
    onClearSelection()
  }

  const quickStatusUpdate = async (status: TaskStatus) => {
    await onBulkUpdate({
      taskIds: selectedTaskIds,
      updates: { status }
    })
    onClearSelection()
  }

  return (
    <div className={cn(
      'flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg',
      className
    )}>
      <div className="flex items-center gap-2 flex-1">
        <Badge variant="secondary">
          {selectedTaskIds.length} selected
        </Badge>

        {/* Quick status updates */}
        <div className="flex gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => quickStatusUpdate(TaskStatus.IN_PROGRESS)}
            disabled={isLoading}
            className="flex items-center gap-1"
          >
            <Clock className="h-3 w-3" />
            In Progress
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => quickStatusUpdate(TaskStatus.DONE)}
            disabled={isLoading}
            className="flex items-center gap-1"
          >
            <CheckCircle className="h-3 w-3" />
            Done
          </Button>
        </div>

        {/* Advanced update dialog */}
        <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" disabled={isLoading}>
              <Edit className="h-3 w-3 mr-1" />
              Edit
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Update {selectedTaskIds.length} Tasks</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              {/* Status */}
              <div>
                <Label htmlFor="bulk-status">Status</Label>
                <Select
                  value={updateData.status || 'keep'}
                  onValueChange={(value) => setUpdateData(prev => ({ 
                    ...prev, 
                    status: value as TaskStatus || undefined 
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Keep current status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="keep">Keep current status</SelectItem>
                    <SelectItem value={TaskStatus.TODO}>To Do</SelectItem>
                    <SelectItem value={TaskStatus.IN_PROGRESS}>In Progress</SelectItem>
                    <SelectItem value={TaskStatus.DONE}>Done</SelectItem>
                    <SelectItem value={TaskStatus.CANCELLED}>Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Priority */}
              <div>
                <Label htmlFor="bulk-priority">Priority</Label>
                <Select
                  value={updateData.priority || 'keep'}
                  onValueChange={(value) => setUpdateData(prev => ({ 
                    ...prev, 
                    priority: value as TaskPriority || undefined 
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Keep current priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="keep">Keep current priority</SelectItem>
                    <SelectItem value={TaskPriority.LOW}>Low</SelectItem>
                    <SelectItem value={TaskPriority.MEDIUM}>Medium</SelectItem>
                    <SelectItem value={TaskPriority.HIGH}>High</SelectItem>
                    <SelectItem value={TaskPriority.URGENT}>Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Assignee */}
              <div>
                <Label htmlFor="bulk-assignee">Assignee Name</Label>
                <Input
                  id="bulk-assignee"
                  placeholder="Keep current assignee"
                  value={updateData.assigneeName || ''}
                  onChange={(e) => setUpdateData(prev => ({ 
                    ...prev, 
                    assigneeName: e.target.value || undefined 
                  }))}
                />
              </div>

              {/* Due Date */}
              <div>
                <Label htmlFor="bulk-due-date">Due Date</Label>
                <Input
                  id="bulk-due-date"
                  type="date"
                  value={updateData.dueDate ? updateData.dueDate.split('T')[0] : ''}
                  onChange={(e) => setUpdateData(prev => ({ 
                    ...prev, 
                    dueDate: e.target.value ? `${e.target.value}T23:59:59Z` : undefined 
                  }))}
                />
              </div>

              {/* Tags */}
              <div>
                <Label htmlFor="bulk-tags">Tags (comma-separated)</Label>
                <Input
                  id="bulk-tags"
                  placeholder="Keep current tags"
                  value={updateData.tags?.join(', ') || ''}
                  onChange={(e) => setUpdateData(prev => ({ 
                    ...prev, 
                    tags: e.target.value ? e.target.value.split(',').map(tag => tag.trim()) : undefined 
                  }))}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setIsUpdateDialogOpen(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleBulkUpdate}
                disabled={isLoading}
              >
                Update Tasks
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Delete dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="destructive" size="sm" disabled={isLoading}>
              <Trash2 className="h-3 w-3 mr-1" />
              Delete
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete {selectedTaskIds.length} Tasks</DialogTitle>
            </DialogHeader>
            
            <p className="text-sm text-gray-600">
              Are you sure you want to delete {selectedTaskIds.length} selected tasks? 
              This action cannot be undone.
            </p>

            <div className="flex justify-end gap-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleBulkDelete}
                disabled={isLoading}
              >
                Delete Tasks
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Clear selection */}
      <Button
        variant="ghost"
        size="sm"
        onClick={onClearSelection}
        disabled={isLoading}
      >
        <XCircle className="h-3 w-3 mr-1" />
        Clear
      </Button>
    </div>
  )
}