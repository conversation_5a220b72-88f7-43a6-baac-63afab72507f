import { Injectable, Logger } from '@nestjs/common';
import { IntegrationAdapter } from '../interfaces/integration-adapter.interface';
import { IntegrationProvider } from '../types';

/**
 * Registry for managing integration adapters
 * Provides centralized access to all available integration adapters
 */
@Injectable()
export class IntegrationAdapterRegistry {
  private readonly logger = new Logger(IntegrationAdapterRegistry.name);
  private readonly adapters = new Map<IntegrationProvider, IntegrationAdapter>();

  /**
   * Register an integration adapter
   * @param provider Integration provider
   * @param adapter Adapter instance
   */
  register(provider: IntegrationProvider, adapter: IntegrationAdapter): void {
    this.adapters.set(provider, adapter);
    this.logger.log(`Registered adapter for ${provider}`);
  }

  /**
   * Get an integration adapter by provider
   * @param provider Integration provider
   * @returns Integration adapter instance
   */
  getAdapter(provider: IntegrationProvider): IntegrationAdapter {
    const adapter = this.adapters.get(provider);
    if (!adapter) {
      throw new Error(`No adapter registered for provider: ${provider}`);
    }
    return adapter;
  }

  /**
   * Check if an adapter is registered for a provider
   * @param provider Integration provider
   * @returns True if adapter is registered
   */
  hasAdapter(provider: IntegrationProvider): boolean {
    return this.adapters.has(provider);
  }

  /**
   * Get all registered providers
   * @returns Array of registered providers
   */
  getRegisteredProviders(): IntegrationProvider[] {
    return Array.from(this.adapters.keys());
  }

  /**
   * Get adapter capabilities for a provider
   * @param provider Integration provider
   * @returns Adapter capabilities
   */
  getAdapterCapabilities(provider: IntegrationProvider): {
    supportsTwoWaySync: boolean;
    supportsWebhooks: boolean;
  } {
    const adapter = this.getAdapter(provider);
    return {
      supportsTwoWaySync: adapter.supportsTwoWaySync(),
      supportsWebhooks: adapter.supportsWebhooks(),
    };
  }

  /**
   * Get all adapter capabilities
   * @returns Map of provider to capabilities
   */
  getAllCapabilities(): Map<IntegrationProvider, {
    supportsTwoWaySync: boolean;
    supportsWebhooks: boolean;
  }> {
    const capabilities = new Map();
    
    for (const provider of this.adapters.keys()) {
      capabilities.set(provider, this.getAdapterCapabilities(provider));
    }
    
    return capabilities;
  }

  /**
   * Validate that all required adapters are registered
   * @param requiredProviders Array of required providers
   * @throws Error if any required provider is missing
   */
  validateRequiredAdapters(requiredProviders: IntegrationProvider[]): void {
    const missing = requiredProviders.filter(provider => !this.hasAdapter(provider));
    
    if (missing.length > 0) {
      throw new Error(`Missing required adapters: ${missing.join(', ')}`);
    }
  }

  /**
   * Get registry statistics
   * @returns Registry statistics
   */
  getStats(): {
    totalAdapters: number;
    twoWaySyncSupported: number;
    webhooksSupported: number;
    providers: IntegrationProvider[];
  } {
    const providers = this.getRegisteredProviders();
    const capabilities = this.getAllCapabilities();
    
    let twoWaySyncSupported = 0;
    let webhooksSupported = 0;
    
    for (const [, caps] of capabilities) {
      if (caps.supportsTwoWaySync) twoWaySyncSupported++;
      if (caps.supportsWebhooks) webhooksSupported++;
    }
    
    return {
      totalAdapters: providers.length,
      twoWaySyncSupported,
      webhooksSupported,
      providers,
    };
  }
}