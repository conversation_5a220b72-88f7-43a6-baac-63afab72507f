# TaskUnify Project Structure & Architecture

## Project Organization

```
TaskUnify/
├── backend/                 # NestJS API server
│   ├── src/
│   │   ├── auth/           # Authentication module (JWT, OAuth)
│   │   ├── integrations/   # Third-party integrations (Asana, Google Sheets, etc.)
│   │   ├── tasks/          # Task management core functionality
│   │   ├── sync/           # Synchronization engine and conflict resolution
│   │   ├── workspaces/     # Workspace and team management
│   │   ├── users/          # User profile management
│   │   └── common/         # Shared utilities, guards, decorators
│   ├── prisma/             # Database schema and migrations
│   ├── scripts/            # Development and deployment scripts
│   └── test/               # E2E and integration tests
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Page components and routing
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API services and data fetching
│   │   ├── store/          # Zustand state management
│   │   ├── lib/            # Utility functions and configurations
│   │   └── types/          # TypeScript type definitions
│   └── public/             # Static assets
├── docs/                   # Project documentation
├── .kiro/                  # Kiro AI assistant configuration
└── docker-compose files   # Container orchestration
```

## Backend Architecture (NestJS)

### Module Structure
- **Feature-based modules**: Each domain (auth, tasks, integrations) is a separate module
- **Shared common module**: Guards, decorators, utilities, and cross-cutting concerns
- **Database module**: Prisma service and database configuration

### Key Patterns
- **DTOs**: Data Transfer Objects for request/response validation using class-validator
- **Services**: Business logic layer with dependency injection
- **Controllers**: HTTP request handling with Swagger documentation
- **Guards**: Authentication and authorization middleware
- **Interceptors**: Request/response transformation and logging

### Path Aliases (tsconfig.json)
```typescript
"@/*": ["src/*"]
"@/common/*": ["src/common/*"]
"@/auth/*": ["src/auth/*"]
"@/tasks/*": ["src/tasks/*"]
"@/workspaces/*": ["src/workspaces/*"]
"@/integrations/*": ["src/integrations/*"]
"@/users/*": ["src/users/*"]
```

## Frontend Architecture (React + Vite)

### Component Organization
- **components/**: Reusable UI components organized by feature or type
- **pages/**: Route-level components and page layouts
- **hooks/**: Custom React hooks for shared logic
- **services/**: API client and data fetching logic

### State Management
- **Zustand**: Global state for user session, app settings
- **React Query**: Server state management and caching
- **React Hook Form**: Form state and validation

### Path Aliases (tsconfig.json)
```typescript
"@/*": ["./src/*"]
"@/components/*": ["./src/components/*"]
"@/pages/*": ["./src/pages/*"]
"@/hooks/*": ["./src/hooks/*"]
"@/services/*": ["./src/services/*"]
"@/utils/*": ["./src/utils/*"]
"@/types/*": ["./src/types/*"]
"@/store/*": ["./src/store/*"]
"@/lib/*": ["./src/lib/*"]
```

## Database Architecture

### Prisma Schema Organization
- **User management**: Users, profiles, authentication
- **Workspace management**: Workspaces, members, roles
- **Task management**: Tasks, priorities, categories
- **Integration management**: Connections, sync status, webhooks
- **Daily planning**: Plans, schedules, time estimates

### Migration Strategy
- Use Prisma migrations for schema changes
- Seed data for development and testing
- Database verification scripts for deployment

## Code Organization Principles

### File Naming Conventions
- **kebab-case** for files and directories
- **PascalCase** for React components
- **camelCase** for functions and variables
- **UPPER_SNAKE_CASE** for constants

### Module Boundaries
- Each feature module should be self-contained
- Shared functionality goes in `common/` or `lib/`
- Cross-module dependencies should be minimal and well-defined
- Integration adapters are isolated and interchangeable

### Testing Structure
- **Unit tests**: `*.spec.ts` files alongside source code
- **Integration tests**: `*.integration.spec.ts` for API endpoints
- **E2E tests**: Separate `test/` directory for full workflow testing

## Development Workflow

### Branch Strategy
- **main**: Production-ready code
- **feature/***: Feature development branches
- **hotfix/***: Critical bug fixes

### Code Quality
- ESLint and Prettier for consistent formatting
- Husky pre-commit hooks for quality checks
- Minimum 80% test coverage requirement
- TypeScript strict mode enabled