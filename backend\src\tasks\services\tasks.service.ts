import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { DatabaseService } from '../../database/database.service';
import { PrioritizationService } from './prioritization.service';
import { PrioritizationSettings, TaskPriorityData } from '../interfaces/prioritization.interface';
import { Task, Prisma } from '@prisma/client';
import { GetTasksQueryDto, TaskSortField, SortOrder } from '../dto/get-tasks-query.dto';
import { CreateTaskDto } from '../dto/create-task.dto';
import { UpdateTaskDto } from '../dto/update-task.dto';
import { BulkUpdateTasksDto } from '../dto/bulk-update-tasks.dto';
import { PaginatedTasksResponseDto } from '../dto/paginated-tasks-response.dto';

@Injectable()
export class TasksService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly prioritizationService: PrioritizationService,
  ) {}

  /**
   * Update priority scores for all tasks in a workspace
   */
  async updateWorkspacePriorityScores(
    workspaceId: string,
    settings?: PrioritizationSettings,
  ): Promise<void> {
    const tasks = await this.databaseService.task.findMany({
      where: { workspaceId },
      select: {
        id: true,
        dueDate: true,
        estimatedMinutes: true,
        priority: true,
        tags: true,
        projectName: true,
        assigneeId: true,
        status: true,
      },
    });

    const updates = tasks.map(task => {
      const priorityData: TaskPriorityData = {
        id: task.id,
        dueDate: task.dueDate,
        estimatedMinutes: task.estimatedMinutes,
        priority: task.priority,
        tags: task.tags,
        projectName: task.projectName,
        assigneeId: task.assigneeId,
        status: task.status,
      };

      const priorityScore = this.prioritizationService.calculatePriorityScore(
        priorityData,
        settings,
      );

      return this.databaseService.task.update({
        where: { id: task.id },
        data: { priorityScore },
      });
    });

    await Promise.all(updates);
  }

  /**
   * Update priority score for a single task
   */
  async updateTaskPriorityScore(
    taskId: string,
    settings?: PrioritizationSettings,
  ): Promise<Task> {
    const task = await this.databaseService.task.findUnique({
      where: { id: taskId },
    });

    if (!task) {
      throw new Error(`Task with id ${taskId} not found`);
    }

    const priorityData: TaskPriorityData = {
      id: task.id,
      dueDate: task.dueDate,
      estimatedMinutes: task.estimatedMinutes,
      priority: task.priority,
      tags: task.tags,
      projectName: task.projectName,
      assigneeId: task.assigneeId,
      status: task.status,
    };

    const priorityScore = this.prioritizationService.calculatePriorityScore(
      priorityData,
      settings,
    );

    return this.databaseService.task.update({
      where: { id: taskId },
      data: { priorityScore },
    });
  }

  /**
   * Get tasks with advanced filtering, sorting, and pagination
   */
  async getTasks(
    workspaceId: string,
    query: GetTasksQueryDto,
  ): Promise<PaginatedTasksResponseDto> {
    const where = this.buildWhereClause(workspaceId, query);
    const orderBy = this.buildOrderByClause(query.sortBy, query.sortOrder);

    // Get total count for pagination
    const total = await this.databaseService.task.count({ where });

    // Get tasks with pagination
    const tasks = await this.databaseService.task.findMany({
      where,
      orderBy,
      take: query.limit,
      skip: query.offset,
      include: {
        integration: {
          select: {
            id: true,
            name: true,
            provider: true,
          },
        },
      },
    });

    return {
      tasks,
      total,
      count: tasks.length,
      offset: query.offset || 0,
      limit: query.limit || 50,
      hasMore: (query.offset || 0) + tasks.length < total,
    };
  }

  /**
   * Get tasks ordered by priority score (legacy method for backward compatibility)
   */
  async getTasksByPriority(
    workspaceId: string,
    limit?: number,
    offset?: number,
  ): Promise<Task[]> {
    return this.databaseService.task.findMany({
      where: { workspaceId },
      orderBy: [
        { priorityScore: 'desc' },
        { dueDate: 'asc' },
        { createdAt: 'desc' },
      ],
      take: limit,
      skip: offset,
    });
  }

  /**
   * Search tasks using case-insensitive text search
   */
  async searchTasks(
    workspaceId: string,
    searchQuery: string,
    query: GetTasksQueryDto,
  ): Promise<PaginatedTasksResponseDto> {
    const where: Prisma.TaskWhereInput = {
      workspaceId,
      OR: [
        {
          title: {
            contains: searchQuery,
            mode: 'insensitive',
          },
        },
        {
          description: {
            contains: searchQuery,
            mode: 'insensitive',
          },
        },
      ],
      // Apply additional filters
      ...(query.status && { status: query.status }),
      ...(query.assigneeId && { assigneeId: query.assigneeId }),
      ...(query.projectName && { projectName: { contains: query.projectName, mode: 'insensitive' } }),
      ...(query.integrationId && { integrationId: query.integrationId }),
      ...(query.priority && { priority: query.priority }),
      ...(query.tags && query.tags.length > 0 && { tags: { hasSome: query.tags } }),
      ...(query.dueDateFrom && { dueDate: { gte: new Date(query.dueDateFrom) } }),
      ...(query.dueDateTo && { dueDate: { lte: new Date(query.dueDateTo) } }),
    };

    const orderBy = this.buildOrderByClause(query.sortBy, query.sortOrder);

    // Get total count
    const total = await this.databaseService.task.count({ where });

    // Get tasks
    const tasks = await this.databaseService.task.findMany({
      where,
      orderBy,
      take: query.limit,
      skip: query.offset,
      include: {
        integration: {
          select: {
            id: true,
            name: true,
            provider: true,
          },
        },
      },
    });

    return {
      tasks,
      total,
      count: tasks.length,
      offset: query.offset || 0,
      limit: query.limit || 50,
      hasMore: (query.offset || 0) + tasks.length < total,
    };
  }

  /**
   * Get a single task by ID
   */
  async getTask(workspaceId: string, taskId: string): Promise<Task> {
    const task = await this.databaseService.task.findFirst({
      where: {
        id: taskId,
        workspaceId,
      },
      include: {
        integration: {
          select: {
            id: true,
            name: true,
            provider: true,
          },
        },
      },
    });

    if (!task) {
      throw new NotFoundException(`Task with id ${taskId} not found`);
    }

    return task;
  }

  /**
   * Create a new task
   */
  async createTask(workspaceId: string, createTaskDto: CreateTaskDto): Promise<Task> {
    // For now, we'll create tasks without an integration (personal tasks)
    // In a real implementation, you'd need to specify which integration to use
    const task = await this.databaseService.task.create({
      data: {
        workspaceId,
        integrationId: 'personal', // This would need to be handled properly
        externalId: `personal-${Date.now()}`, // Generate a unique external ID
        title: createTaskDto.title,
        description: createTaskDto.description,
        status: createTaskDto.status || 'todo',
        priority: createTaskDto.priority || 'medium',
        assigneeId: createTaskDto.assigneeId,
        assigneeName: createTaskDto.assigneeName,
        dueDate: createTaskDto.dueDate ? new Date(createTaskDto.dueDate) : null,
        estimatedMinutes: createTaskDto.estimatedMinutes,
        tags: createTaskDto.tags || [],
        projectName: createTaskDto.projectName,
        sourceUrl: createTaskDto.sourceUrl,
        syncStatus: 'synced',
      },
    });

    // Calculate and update priority score
    const settings = await this.getWorkspacePrioritizationSettings(workspaceId);
    await this.updateTaskPriorityScore(task.id, settings);

    return this.getTask(workspaceId, task.id);
  }

  /**
   * Update a task
   */
  async updateTask(
    workspaceId: string,
    taskId: string,
    updateTaskDto: UpdateTaskDto,
  ): Promise<Task> {
    const existingTask = await this.getTask(workspaceId, taskId);

    const updatedTask = await this.databaseService.task.update({
      where: { id: taskId },
      data: {
        ...(updateTaskDto.title && { title: updateTaskDto.title }),
        ...(updateTaskDto.description !== undefined && { description: updateTaskDto.description }),
        ...(updateTaskDto.status && { status: updateTaskDto.status }),
        ...(updateTaskDto.priority && { priority: updateTaskDto.priority }),
        ...(updateTaskDto.assigneeId !== undefined && { assigneeId: updateTaskDto.assigneeId }),
        ...(updateTaskDto.assigneeName !== undefined && { assigneeName: updateTaskDto.assigneeName }),
        ...(updateTaskDto.dueDate !== undefined && { 
          dueDate: updateTaskDto.dueDate ? new Date(updateTaskDto.dueDate) : null 
        }),
        ...(updateTaskDto.estimatedMinutes !== undefined && { estimatedMinutes: updateTaskDto.estimatedMinutes }),
        ...(updateTaskDto.tags && { tags: updateTaskDto.tags }),
        ...(updateTaskDto.projectName !== undefined && { projectName: updateTaskDto.projectName }),
        syncStatus: 'pending', // Mark as needing sync
      },
    });

    // Recalculate priority score if relevant fields changed
    if (updateTaskDto.priority || updateTaskDto.dueDate !== undefined || updateTaskDto.estimatedMinutes !== undefined) {
      const settings = await this.getWorkspacePrioritizationSettings(workspaceId);
      await this.updateTaskPriorityScore(taskId, settings);
    }

    return this.getTask(workspaceId, taskId);
  }

  /**
   * Delete a task
   */
  async deleteTask(workspaceId: string, taskId: string): Promise<void> {
    const task = await this.getTask(workspaceId, taskId);
    
    await this.databaseService.task.delete({
      where: { id: taskId },
    });
  }

  /**
   * Bulk update tasks
   */
  async bulkUpdateTasks(
    workspaceId: string,
    bulkUpdateDto: BulkUpdateTasksDto,
  ): Promise<{ updated: number; tasks: Task[] }> {
    // Verify all tasks belong to the workspace
    const tasks = await this.databaseService.task.findMany({
      where: {
        id: { in: bulkUpdateDto.taskIds },
        workspaceId,
      },
    });

    if (tasks.length !== bulkUpdateDto.taskIds.length) {
      throw new BadRequestException('Some tasks not found or do not belong to this workspace');
    }

    // Prepare update data
    const updateData: Prisma.TaskUpdateInput = {
      ...(bulkUpdateDto.updates.status && { status: bulkUpdateDto.updates.status }),
      ...(bulkUpdateDto.updates.assigneeId !== undefined && { assigneeId: bulkUpdateDto.updates.assigneeId }),
      ...(bulkUpdateDto.updates.assigneeName !== undefined && { assigneeName: bulkUpdateDto.updates.assigneeName }),
      ...(bulkUpdateDto.updates.dueDate !== undefined && { 
        dueDate: bulkUpdateDto.updates.dueDate ? new Date(bulkUpdateDto.updates.dueDate) : null 
      }),
      ...(bulkUpdateDto.updates.priority && { priority: bulkUpdateDto.updates.priority }),
      ...(bulkUpdateDto.updates.tags && { tags: bulkUpdateDto.updates.tags }),
      syncStatus: 'pending', // Mark as needing sync
    };

    // Perform bulk update
    await this.databaseService.task.updateMany({
      where: {
        id: { in: bulkUpdateDto.taskIds },
      },
      data: updateData,
    });

    // Recalculate priority scores if relevant fields changed
    if (bulkUpdateDto.updates.priority || bulkUpdateDto.updates.dueDate !== undefined) {
      const settings = await this.getWorkspacePrioritizationSettings(workspaceId);
      await Promise.all(
        bulkUpdateDto.taskIds.map(taskId => 
          this.updateTaskPriorityScore(taskId, settings)
        )
      );
    }

    // Return updated tasks
    const updatedTasks = await this.databaseService.task.findMany({
      where: {
        id: { in: bulkUpdateDto.taskIds },
      },
      include: {
        integration: {
          select: {
            id: true,
            name: true,
            provider: true,
          },
        },
      },
    });

    return {
      updated: updatedTasks.length,
      tasks: updatedTasks,
    };
  }

  /**
   * Build WHERE clause for task queries
   */
  private buildWhereClause(workspaceId: string, query: GetTasksQueryDto): Prisma.TaskWhereInput {
    const where: Prisma.TaskWhereInput = {
      workspaceId,
    };

    if (query.status) {
      where.status = query.status;
    }

    if (query.assigneeId) {
      where.assigneeId = query.assigneeId;
    }

    if (query.projectName) {
      where.projectName = {
        contains: query.projectName,
        mode: 'insensitive',
      };
    }

    if (query.integrationId) {
      where.integrationId = query.integrationId;
    }

    if (query.priority) {
      where.priority = query.priority;
    }

    if (query.tags && query.tags.length > 0) {
      where.tags = {
        hasSome: query.tags,
      };
    }

    if (query.dueDateFrom || query.dueDateTo) {
      where.dueDate = {};
      if (query.dueDateFrom) {
        where.dueDate.gte = new Date(query.dueDateFrom);
      }
      if (query.dueDateTo) {
        where.dueDate.lte = new Date(query.dueDateTo);
      }
    }

    return where;
  }

  /**
   * Build ORDER BY clause for task queries
   */
  private buildOrderByClause(
    sortBy?: TaskSortField,
    sortOrder?: SortOrder,
  ): Prisma.TaskOrderByWithRelationInput[] {
    const field = sortBy || TaskSortField.PRIORITY_SCORE;
    const order = sortOrder || SortOrder.DESC;

    const orderBy: Prisma.TaskOrderByWithRelationInput[] = [
      { [field]: order },
    ];

    // Add secondary sorting
    if (field !== TaskSortField.PRIORITY_SCORE) {
      orderBy.push({ priorityScore: 'desc' });
    }
    if (field !== TaskSortField.DUE_DATE) {
      orderBy.push({ dueDate: 'asc' });
    }
    if (field !== TaskSortField.CREATED_AT) {
      orderBy.push({ createdAt: 'desc' });
    }

    return orderBy;
  }

  /**
   * Get workspace prioritization settings from workspace settings
   */
  async getWorkspacePrioritizationSettings(
    workspaceId: string,
  ): Promise<PrioritizationSettings> {
    const workspace = await this.databaseService.workspace.findUnique({
      where: { id: workspaceId },
      select: { settings: true },
    });

    if (!workspace) {
      throw new Error(`Workspace with id ${workspaceId} not found`);
    }

    const settings = workspace.settings as any;
    
    // Return custom settings if they exist, otherwise return defaults
    if (settings?.prioritization) {
      return {
        ...this.prioritizationService.getDefaultSettings(),
        ...settings.prioritization,
      };
    }

    return this.prioritizationService.getDefaultSettings();
  }

  /**
   * Update workspace prioritization settings
   */
  async updateWorkspacePrioritizationSettings(
    workspaceId: string,
    settings: Partial<PrioritizationSettings>,
  ): Promise<void> {
    const workspace = await this.databaseService.workspace.findUnique({
      where: { id: workspaceId },
      select: { settings: true },
    });

    if (!workspace) {
      throw new Error(`Workspace with id ${workspaceId} not found`);
    }

    const currentSettings = workspace.settings as any;
    const updatedSettings = {
      ...currentSettings,
      prioritization: {
        ...this.prioritizationService.getDefaultSettings(),
        ...currentSettings?.prioritization,
        ...settings,
      },
    };

    await this.databaseService.workspace.update({
      where: { id: workspaceId },
      data: { settings: updatedSettings },
    });

    // Recalculate priority scores for all tasks in the workspace
    await this.updateWorkspacePriorityScores(workspaceId, updatedSettings.prioritization);
  }
}