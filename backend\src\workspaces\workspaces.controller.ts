import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { WorkspacesService } from './workspaces.service';
import { CreateWorkspaceDto } from './dto/create-workspace.dto';
import { UpdateWorkspaceDto } from './dto/update-workspace.dto';
import { InviteMemberDto } from './dto/invite-member.dto';
import { UpdateMemberDto } from './dto/update-member.dto';

@Controller('workspaces')
@UseGuards(JwtAuthGuard)
export class WorkspacesController {
  constructor(private readonly workspacesService: WorkspacesService) {}

  @Post()
  create(@Body() createWorkspaceDto: CreateWorkspaceDto, @Request() req) {
    return this.workspacesService.create(createWorkspaceDto, req.user.id);
  }

  @Get()
  findAll(@Request() req) {
    return this.workspacesService.findAll(req.user.id);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    return this.workspacesService.findOne(id, req.user.id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateWorkspaceDto: UpdateWorkspaceDto,
    @Request() req,
  ) {
    return this.workspacesService.update(id, updateWorkspaceDto, req.user.id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string, @Request() req) {
    return this.workspacesService.remove(id, req.user.id);
  }

  @Post(':id/members')
  inviteMember(
    @Param('id') workspaceId: string,
    @Body() inviteMemberDto: InviteMemberDto,
    @Request() req,
  ) {
    return this.workspacesService.inviteMember(workspaceId, inviteMemberDto, req.user.id);
  }

  @Get(':id/members')
  getMembers(@Param('id') workspaceId: string, @Request() req) {
    return this.workspacesService.getMembers(workspaceId, req.user.id);
  }

  @Patch(':id/members/:memberId')
  updateMember(
    @Param('id') workspaceId: string,
    @Param('memberId') memberId: string,
    @Body() updateMemberDto: UpdateMemberDto,
    @Request() req,
  ) {
    return this.workspacesService.updateMember(workspaceId, memberId, updateMemberDto, req.user.id);
  }

  @Delete(':id/members/:memberId')
  @HttpCode(HttpStatus.NO_CONTENT)
  removeMember(
    @Param('id') workspaceId: string,
    @Param('memberId') memberId: string,
    @Request() req,
  ) {
    return this.workspacesService.removeMember(workspaceId, memberId, req.user.id);
  }

  @Post(':id/leave')
  @HttpCode(HttpStatus.NO_CONTENT)
  leaveWorkspace(@Param('id') workspaceId: string, @Request() req) {
    return this.workspacesService.leaveWorkspace(workspaceId, req.user.id);
  }

  @Post(':id/transfer-ownership')
  transferOwnership(
    @Param('id') workspaceId: string,
    @Body('newOwnerId') newOwnerId: string,
    @Request() req,
  ) {
    return this.workspacesService.transferOwnership(workspaceId, newOwnerId, req.user.id);
  }
}