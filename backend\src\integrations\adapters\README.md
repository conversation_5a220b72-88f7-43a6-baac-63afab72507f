# Integration Adapters

This directory contains integration adapters for various external services.

## Available Adapters

### Google Sheets Adapter
The Google Sheets adapter allows <PERSON><PERSON>nify to read and write tasks from Google Sheets with configurable column mapping.

### Asana Adapter
The Asana adapter provides comprehensive task synchronization with Asana projects and workspaces, including two-way sync and webhook support.

## Google Sheets Adapter

The Google Sheets adapter allows Task<PERSON>nify to read and write tasks from Google Sheets with configurable column mapping.

### Features

- **OAuth Authentication**: Secure authentication using Google OAuth2
- **Configurable Column Mapping**: Map spreadsheet columns to task fields
- **Two-way Sync**: Read tasks from sheets and write updates back
- **Quick Task Creation**: Create new tasks directly in configured sheets
- **Flexible Data Parsing**: Handles various date formats, tags, and data types

### Configuration

The adapter requires the following configuration:

```typescript
const config: IntegrationConfig = {
  syncInterval: 15, // minutes
  enableTwoWaySync: true,
  fieldMappings: [
    { localField: 'title', externalField: 'Task Name' },
    { localField: 'status', externalField: 'Status' },
    { localField: 'priority', externalField: 'Priority' },
    { localField: 'dueDate', externalField: 'Due Date' },
    // ... more mappings
  ],
  filters: [],
  customSettings: {
    spreadsheetId: 'your-spreadsheet-id',
    sheetName: 'Tasks', // optional, defaults to 'Tasks'
    credentials: {
      accessToken: 'oauth-access-token',
      refreshToken: 'oauth-refresh-token',
      // ... other OAuth credentials
    }
  }
};
```

### Environment Variables

For quick task creation, set these environment variables:

```bash
GOOGLE_SHEETS_DEFAULT_SPREADSHEET_ID=your-default-spreadsheet-id
GOOGLE_SHEETS_DEFAULT_SHEET_NAME=Tasks
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

### Column Mapping

The adapter supports automatic column detection based on common header names:

| Local Field | Possible Headers |
|-------------|------------------|
| title | title, task, name, summary, description |
| description | description, details, notes, comment |
| status | status, state, progress |
| priority | priority, importance, urgency |
| assignee | assignee, assigned to, owner, responsible |
| dueDate | due date, deadline, due, target date |
| estimatedMinutes | estimate, estimated time, duration, effort |
| tags | tags, labels, categories |
| project | project, category, area |

### Usage Example

```typescript
import { GoogleSheetsAdapter } from './google-sheets.adapter';

const adapter = new GoogleSheetsAdapter();

// Authenticate
const authResult = await adapter.authenticate(credentials);

// Fetch tasks
const tasks = await adapter.fetchTasks(config);

// Create a new task
const newTask = await adapter.createTask({
  title: 'New Task',
  description: 'Task description',
  priority: TaskPriority.HIGH,
  dueDate: new Date('2024-01-15'),
});

// Update a task
const updatedTask = await adapter.updateTask('task-id', {
  status: TaskStatus.DONE,
});
```

### Supported Data Formats

#### Dates
- YYYY-MM-DD (2024-01-15)
- MM/DD/YYYY (01/15/2024)
- MM-DD-YYYY (01-15-2024)

#### Tags
- Comma-separated values: "urgent, feature, bug"
- Single values: "enhancement"

#### Status Values
- Maps common status variations to standard TaskStatus enum
- Examples: "todo", "in progress", "done", "cancelled"

#### Priority Values
- Maps numeric (1-4) and text values to TaskPriority enum
- Examples: "low", "medium", "high", "urgent"

### Error Handling

The adapter handles various error scenarios:

- **Authentication Errors**: Invalid or expired OAuth tokens
- **Rate Limiting**: Google Sheets API rate limits
- **Network Errors**: Connection issues
- **Validation Errors**: Invalid spreadsheet data
- **Missing Configuration**: Required settings not provided

### Testing

The adapter includes comprehensive unit tests covering:

- Authentication flows
- Task CRUD operations
- Column mapping scenarios
- Error handling
- Data format parsing

Run tests with:
```bash
npm test -- google-sheets.adapter.spec.ts
```

### Limitations

- **No Webhooks**: Google Sheets doesn't support real-time webhooks
- **Rate Limits**: Subject to Google Sheets API quotas
- **Data Types**: Limited to text-based data in spreadsheet cells
- **Concurrent Access**: Multiple users editing the same sheet may cause conflicts
##
 Asana Adapter

The Asana adapter enables TaskUnify to synchronize tasks with Asana projects and workspaces.

### Features

- **OAuth Authentication**: Secure authentication using Asana OAuth2
- **Two-way Sync**: Read tasks from Asana and write updates back
- **Workspace & Project Filtering**: Sync from specific workspaces or projects
- **Real-time Webhooks**: Receive instant notifications for task changes
- **Custom Field Support**: Handle Asana custom fields for priority and estimates
- **Comprehensive Error Handling**: Robust error handling with retry logic

### Configuration

The adapter requires the following configuration:

```typescript
const config: IntegrationConfig = {
  syncInterval: 15, // minutes
  enableTwoWaySync: true,
  fieldMappings: [],
  filters: [],
  customSettings: {
    credentials: {
      accessToken: 'oauth-access-token',
      refreshToken: 'oauth-refresh-token',
      expiresAt: new Date('2024-12-31T23:59:59Z'),
      userId: 'asana-user-id',
      userEmail: '<EMAIL>',
    },
    workspaceIds: ['workspace-1'], // Optional: specific workspaces
    projectIds: ['project-1'], // Optional: specific projects
  }
};
```

### Environment Variables

For Asana integration, set these environment variables:

```bash
ASANA_CLIENT_ID=your-asana-client-id
ASANA_CLIENT_SECRET=your-asana-client-secret
ASANA_CALLBACK_URL=http://localhost:3001/integrations/asana/callback
```

### Field Mapping

The adapter maps TaskUnify fields to Asana fields:

| TaskUnify Field | Asana Field | Notes |
|----------------|-------------|-------|
| title | name | Task title |
| description | notes | Task description |
| status | completed | Boolean: true = done, false = todo |
| priority | custom_fields | From Priority custom field |
| assigneeId | assignee.gid | Asana user ID |
| assigneeName | assignee.name | Asana user name |
| dueDate | due_date | Date in YYYY-MM-DD format |
| tags | tags | Array of tag names |
| projectName | projects[0].name | Primary project name |

### Usage Example

```typescript
import { AsanaAdapter } from './asana.adapter';

const adapter = new AsanaAdapter();

// Authenticate
const authResult = await adapter.authenticate(credentials);

// Fetch tasks
const tasks = await adapter.fetchTasks(config);

// Create a new task
const newTask = await adapter.createTask({
  title: 'New Task',
  description: 'Task description',
  priority: TaskPriority.HIGH,
  dueDate: new Date('2024-01-15'),
  projectId: 'project-123',
});

// Update a task
const updatedTask = await adapter.updateTask('task-123', {
  status: TaskStatus.DONE,
});

// Setup webhook
const webhook = await adapter.setupWebhook('https://your-app.com/webhook');
```

### Supported Operations

- **Authentication**: OAuth-based authentication with Asana
- **Fetch Tasks**: Retrieve tasks from workspaces and projects
- **Create Tasks**: Create new tasks in Asana projects
- **Update Tasks**: Update task status, title, description, due date, and assignee
- **Webhook Setup**: Configure real-time task change notifications
- **Credential Validation**: Verify access token validity

### Error Handling

The adapter handles various error scenarios:

- **Authentication Errors (401, 403)**: Invalid or expired tokens
- **Rate Limiting (429)**: Automatic retry with exponential backoff
- **Network Errors**: Connection issues with retry logic
- **Validation Errors (400)**: Invalid request data
- **Server Errors (500+)**: Asana API server issues

### Webhook Support

The adapter supports webhooks for real-time updates:

- **Events**: task.added, task.changed, task.deleted
- **Setup**: Automatic webhook registration
- **Security**: Webhook signature verification (recommended)

### Testing

Run Asana adapter tests with:
```bash
npm test -- asana.adapter.spec.ts
```

### Limitations

- **Custom Fields**: Only enum and number fields are mapped to standard TaskUnify fields
- **Project Access**: User must have access to projects for sync
- **Rate Limits**: Subject to Asana API rate limits (1,500 requests/minute for standard accounts)
- **Webhooks**: Maximum 10 webhooks per workspace

For detailed implementation information, see [ASANA_IMPLEMENTATION.md](./ASANA_IMPLEMENTATION.md).