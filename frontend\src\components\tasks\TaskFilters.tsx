import { useState, useEffect } from 'react'
import { Search, Filter, X } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import { TaskStatus, TaskPriority, TaskSortField, SortOrder, type TaskFilters, type TaskQuery } from '@/types/task'
import { cn } from '@/lib/utils'

interface TaskFiltersProps {
  filters: TaskQuery
  onFiltersChange: (filters: TaskQuery) => void
  filterOptions?: {
    assignees: Array<{ id: string; name: string }>
    projects: string[]
    tags: string[]
    integrations: Array<{ id: string; name: string; provider: string }>
  }
  className?: string
}

export function TaskFilters({ 
  filters, 
  onFiltersChange, 
  filterOptions,
  className 
}: TaskFiltersProps) {
  const [searchValue, setSearchValue] = useState(filters.search || '')
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchValue !== filters.search) {
        onFiltersChange({ ...filters, search: searchValue || undefined })
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [searchValue, filters, onFiltersChange])

  const updateFilter = (key: keyof TaskFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value === 'all' ? undefined : value || undefined
    })
  }

  const clearFilters = () => {
    setSearchValue('')
    onFiltersChange({
      limit: filters.limit,
      offset: 0,
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder
    })
  }

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.status) count++
    if (filters.priority) count++
    if (filters.assigneeId) count++
    if (filters.projectName) count++
    if (filters.integrationId) count++
    if (filters.tags && filters.tags.length > 0) count++
    if (filters.dueDateFrom || filters.dueDateTo) count++
    return count
  }

  const activeFilterCount = getActiveFilterCount()

  return (
    <div className={cn('space-y-4', className)}>
      {/* Search and basic controls */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search tasks..."
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Button
          variant="outline"
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="flex items-center gap-2"
        >
          <Filter className="h-4 w-4" />
          Filters
          {activeFilterCount > 0 && (
            <Badge variant="secondary" className="ml-1">
              {activeFilterCount}
            </Badge>
          )}
        </Button>

        {activeFilterCount > 0 && (
          <Button
            variant="ghost"
            onClick={clearFilters}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Clear
          </Button>
        )}
      </div>

      {/* Sorting controls */}
      <div className="flex gap-2">
        <Select
          value={filters.sortBy || TaskSortField.PRIORITY_SCORE}
          onValueChange={(value) => updateFilter('sortBy', value as TaskSortField)}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={TaskSortField.PRIORITY_SCORE}>Priority Score</SelectItem>
            <SelectItem value={TaskSortField.DUE_DATE}>Due Date</SelectItem>
            <SelectItem value={TaskSortField.CREATED_AT}>Created Date</SelectItem>
            <SelectItem value={TaskSortField.UPDATED_AT}>Updated Date</SelectItem>
            <SelectItem value={TaskSortField.TITLE}>Title</SelectItem>
            <SelectItem value={TaskSortField.STATUS}>Status</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={filters.sortOrder || SortOrder.DESC}
          onValueChange={(value) => updateFilter('sortOrder', value as SortOrder)}
        >
          <SelectTrigger className="w-32">
            <SelectValue placeholder="Order" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={SortOrder.DESC}>Descending</SelectItem>
            <SelectItem value={SortOrder.ASC}>Ascending</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Advanced filters */}
      {showAdvancedFilters && (
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Status filter */}
              <div>
                <label className="text-sm font-medium mb-2 block">Status</label>
                <Select
                  value={filters.status || 'all'}
                  onValueChange={(value) => updateFilter('status', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All statuses</SelectItem>
                    <SelectItem value={TaskStatus.TODO}>To Do</SelectItem>
                    <SelectItem value={TaskStatus.IN_PROGRESS}>In Progress</SelectItem>
                    <SelectItem value={TaskStatus.DONE}>Done</SelectItem>
                    <SelectItem value={TaskStatus.CANCELLED}>Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Priority filter */}
              <div>
                <label className="text-sm font-medium mb-2 block">Priority</label>
                <Select
                  value={filters.priority || 'all'}
                  onValueChange={(value) => updateFilter('priority', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All priorities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All priorities</SelectItem>
                    <SelectItem value={TaskPriority.LOW}>Low</SelectItem>
                    <SelectItem value={TaskPriority.MEDIUM}>Medium</SelectItem>
                    <SelectItem value={TaskPriority.HIGH}>High</SelectItem>
                    <SelectItem value={TaskPriority.URGENT}>Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Assignee filter */}
              {filterOptions?.assignees && (
                <div>
                  <label className="text-sm font-medium mb-2 block">Assignee</label>
                  <Select
                    value={filters.assigneeId || 'all'}
                    onValueChange={(value) => updateFilter('assigneeId', value || undefined)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All assignees" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All assignees</SelectItem>
                      {filterOptions.assignees.map((assignee) => (
                        <SelectItem key={assignee.id} value={assignee.id}>
                          {assignee.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Project filter */}
              {filterOptions?.projects && (
                <div>
                  <label className="text-sm font-medium mb-2 block">Project</label>
                  <Select
                    value={filters.projectName || 'all'}
                    onValueChange={(value) => updateFilter('projectName', value || undefined)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All projects" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All projects</SelectItem>
                      {filterOptions.projects.map((project) => (
                        <SelectItem key={project} value={project}>
                          {project}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Integration filter */}
              {filterOptions?.integrations && (
                <div>
                  <label className="text-sm font-medium mb-2 block">Source</label>
                  <Select
                    value={filters.integrationId || 'all'}
                    onValueChange={(value) => updateFilter('integrationId', value || undefined)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All sources" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All sources</SelectItem>
                      {filterOptions.integrations.map((integration) => (
                        <SelectItem key={integration.id} value={integration.id}>
                          {integration.name} ({integration.provider})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Due date filters */}
              <div>
                <label className="text-sm font-medium mb-2 block">Due Date From</label>
                <Input
                  type="date"
                  value={filters.dueDateFrom ? filters.dueDateFrom.split('T')[0] : ''}
                  onChange={(e) => updateFilter('dueDateFrom', e.target.value ? `${e.target.value}T00:00:00Z` : undefined)}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Due Date To</label>
                <Input
                  type="date"
                  value={filters.dueDateTo ? filters.dueDateTo.split('T')[0] : ''}
                  onChange={(e) => updateFilter('dueDateTo', e.target.value ? `${e.target.value}T23:59:59Z` : undefined)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}