version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: taskunify-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-taskunify}
      POSTGRES_USER: ${POSTGRES_USER:-taskunify}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-taskunify} -d ${POSTGRES_DB:-taskunify}"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: taskunify-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: taskunify-backend
    ports:
      - "${BACKEND_PORT:-3001}:3001"
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://${POSTGRES_USER:-taskunify}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-taskunify}
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      PORT: 3001
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: taskunify-frontend
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    environment:
      VITE_API_URL: ${API_URL:-http://localhost:3001}
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped

  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: taskunify-worker
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://${POSTGRES_USER:-taskunify}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-taskunify}
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: npm run worker

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: taskunify-network
