import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { DatabaseService } from '../../database/database.service';
import { IntegrationService } from '../../integrations/services/integration.service';
import { ConflictResolutionService } from '../services/conflict-resolution.service';
import {
  SyncJobData,
  SyncJobResult,
  FullSyncJobData,
  IncrementalSyncJobData,
  WebhookSyncJobData,
  ConflictResolutionJobData,
  RetryFailedJobData,
} from '../interfaces/sync-job.interface';
import {
  SYNC_QUEUE,
  SyncJobType,
  SyncOperationType,
  ConflictResolutionStrategy,
} from '../constants/sync.constants';
import {
  SyncError,
  SyncConflict,
  ExternalTask,
  TaskStatus,
  TaskPriority,
  IntegrationStatus,
} from '../../integrations/types';

@Processor(SYNC_QUEUE)
export class SyncJobProcessor {
  private readonly logger = new Logger(SyncJobProcessor.name);

  constructor(
    private prisma: DatabaseService,
    private integrationsService: IntegrationService,
    private conflictResolution: ConflictResolutionService,
  ) {}

  @Process(SyncJobType.FULL_SYNC)
  async processFullSync(job: Job<FullSyncJobData>): Promise<SyncJobResult> {
    const { integrationId, workspaceId, forceSync, syncAllTasks } = job.data;
    const startTime = Date.now();

    this.logger.log(`Starting full sync for integration ${integrationId}`);

    // Create sync log
    const syncLog = await this.prisma.syncLog.create({
      data: {
        integrationId,
        operation: SyncOperationType.SYNC,
        status: 'running',
        startedAt: new Date(),
      },
    });

    try {
      const result = await this.performFullSync(
        integrationId,
        workspaceId,
        forceSync,
        syncAllTasks,
        (progress) => job.progress(progress)
      );

      // Update sync log
      await this.prisma.syncLog.update({
        where: { id: syncLog.id },
        data: {
          status: 'completed',
          tasksProcessed: result.tasksProcessed,
          completedAt: new Date(),
        },
      });

      // Update integration last sync time
      await this.prisma.integration.update({
        where: { id: integrationId },
        data: { lastSyncAt: new Date() },
      });

      this.logger.log(`Completed full sync for integration ${integrationId}`);
      return result;
    } catch (error) {
      // Update sync log with error
      await this.prisma.syncLog.update({
        where: { id: syncLog.id },
        data: {
          status: 'failed',
          errors: [this.createSyncError(error)] as any,
          completedAt: new Date(),
        },
      });

      this.logger.error(`Full sync failed for integration ${integrationId}:`, error);
      throw error;
    }
  }

  @Process(SyncJobType.INCREMENTAL_SYNC)
  async processIncrementalSync(job: Job<IncrementalSyncJobData>): Promise<SyncJobResult> {
    const { integrationId, workspaceId, lastSyncAt, modifiedSince } = job.data;
    const startTime = Date.now();

    this.logger.log(`Starting incremental sync for integration ${integrationId}`);

    // Create sync log
    const syncLog = await this.prisma.syncLog.create({
      data: {
        integrationId,
        operation: SyncOperationType.SYNC,
        status: 'running',
        startedAt: new Date(),
      },
    });

    try {
      const result = await this.performIncrementalSync(
        integrationId,
        workspaceId,
        lastSyncAt || modifiedSince,
        (progress) => job.progress(progress)
      );

      // Update sync log
      await this.prisma.syncLog.update({
        where: { id: syncLog.id },
        data: {
          status: 'completed',
          tasksProcessed: result.tasksProcessed,
          completedAt: new Date(),
        },
      });

      // Update integration last sync time
      await this.prisma.integration.update({
        where: { id: integrationId },
        data: { lastSyncAt: new Date() },
      });

      this.logger.log(`Completed incremental sync for integration ${integrationId}`);
      return result;
    } catch (error) {
      // Update sync log with error
      await this.prisma.syncLog.update({
        where: { id: syncLog.id },
        data: {
          status: 'failed',
          errors: [this.createSyncError(error)] as any,
          completedAt: new Date(),
        },
      });

      this.logger.error(`Incremental sync failed for integration ${integrationId}:`, error);
      throw error;
    }
  }

  @Process(SyncJobType.WEBHOOK_SYNC)
  async processWebhookSync(job: Job<WebhookSyncJobData>): Promise<SyncJobResult> {
    const { integrationId, workspaceId, webhookPayload, eventType, externalTaskId } = job.data;
    const startTime = Date.now();

    this.logger.log(`Processing webhook sync for integration ${integrationId}, event: ${eventType}`);

    try {
      const result = await this.performWebhookSync(
        integrationId,
        workspaceId,
        webhookPayload,
        eventType,
        externalTaskId
      );

      this.logger.log(`Completed webhook sync for integration ${integrationId}`);
      return result;
    } catch (error) {
      this.logger.error(`Webhook sync failed for integration ${integrationId}:`, error);
      throw error;
    }
  }

  @Process(SyncJobType.CONFLICT_RESOLUTION)
  async processConflictResolution(job: Job<ConflictResolutionJobData>): Promise<SyncJobResult> {
    const { integrationId, workspaceId, conflicts, strategy, resolvedBy } = job.data;
    const startTime = Date.now();

    this.logger.log(`Processing conflict resolution for integration ${integrationId}`);

    try {
      const { resolved, failed } = await this.conflictResolution.resolveConflicts(
        integrationId,
        conflicts,
        strategy,
        resolvedBy
      );

      const result: SyncJobResult = {
        success: failed.length === 0,
        integrationId,
        jobType: SyncJobType.CONFLICT_RESOLUTION,
        tasksProcessed: conflicts.length,
        tasksCreated: 0,
        tasksUpdated: resolved.length,
        tasksDeleted: 0,
        conflictsDetected: 0,
        conflictsResolved: resolved.length,
        errors: failed.map(f => f.error),
        conflicts: failed.map(f => f.conflict),
        duration: Date.now() - startTime,
        completedAt: new Date(),
      };

      this.logger.log(`Completed conflict resolution for integration ${integrationId}`);
      return result;
    } catch (error) {
      this.logger.error(`Conflict resolution failed for integration ${integrationId}:`, error);
      throw error;
    }
  }

  @Process(SyncJobType.RETRY_FAILED)
  async processRetryFailed(job: Job<RetryFailedJobData>): Promise<SyncJobResult> {
    const { integrationId, workspaceId, originalJobId, originalError, maxRetries } = job.data;
    const startTime = Date.now();

    this.logger.log(`Retrying failed job ${originalJobId} for integration ${integrationId}`);

    try {
      // Determine what type of sync to retry based on the original error
      let result: SyncJobResult;

      if (originalError.retryable) {
        // Perform incremental sync as a safe retry
        result = await this.performIncrementalSync(
          integrationId,
          workspaceId,
          undefined,
          (progress) => job.progress(progress)
        );
      } else {
        throw new Error(`Original error is not retryable: ${originalError.message}`);
      }

      this.logger.log(`Successfully retried job for integration ${integrationId}`);
      return result;
    } catch (error) {
      this.logger.error(`Retry failed for integration ${integrationId}:`, error);
      throw error;
    }
  }

  /**
   * Perform full sync operation
   */
  private async performFullSync(
    integrationId: string,
    workspaceId: string,
    forceSync: boolean = false,
    syncAllTasks: boolean = false,
    progressCallback?: (progress: number) => void
  ): Promise<SyncJobResult> {
    const startTime = Date.now();
    const result: SyncJobResult = {
      success: false,
      integrationId,
      jobType: SyncJobType.FULL_SYNC,
      tasksProcessed: 0,
      tasksCreated: 0,
      tasksUpdated: 0,
      tasksDeleted: 0,
      conflictsDetected: 0,
      conflictsResolved: 0,
      errors: [],
      conflicts: [],
      duration: 0,
      completedAt: new Date(),
    };

    // Get integration and adapter
    const integration = await this.prisma.integration.findUnique({
      where: { id: integrationId },
    });

    if (!integration) {
      throw new Error(`Integration ${integrationId} not found`);
    }

    const adapter = await this.integrationsService.getAdapter(integration.provider as any);
    if (!adapter) {
      throw new Error(`No adapter found for provider ${integration.provider}`);
    }

    progressCallback?.(10);

    // Fetch all tasks from external source
    const config = integration.config as any;
    const externalTasks = await adapter.fetchTasks(config);
    result.tasksProcessed = externalTasks.length;

    progressCallback?.(30);

    // Get existing tasks
    const existingTasks = await this.prisma.task.findMany({
      where: { integrationId },
    });

    const existingTasksMap = new Map(
      existingTasks.map(task => [task.externalId, task])
    );

    progressCallback?.(50);

    // Process each external task
    for (let i = 0; i < externalTasks.length; i++) {
      const externalTask = externalTasks[i];
      const existingTask = existingTasksMap.get(externalTask.id);

      try {
        if (existingTask) {
          // Update existing task
          const conflicts = await this.conflictResolution.detectConflicts(
            existingTask,
            externalTask,
            integration.lastSyncAt || new Date(0)
          );

          if (conflicts.length > 0) {
            result.conflictsDetected += conflicts.length;
            result.conflicts.push(...conflicts);

            // Mark task as conflicted
            await this.prisma.task.update({
              where: { id: existingTask.id },
              data: { syncStatus: 'conflict' },
            });
          } else {
            // Update task without conflicts
            await this.updateLocalTask(existingTask.id, externalTask);
            result.tasksUpdated++;
          }
        } else {
          // Create new task
          await this.createLocalTask(integrationId, workspaceId, externalTask);
          result.tasksCreated++;
        }
      } catch (error) {
        result.errors.push(this.createSyncError(error));
      }

      // Update progress
      const progress = 50 + (i / externalTasks.length) * 40;
      progressCallback?.(Math.round(progress));
    }

    // Handle deleted tasks (tasks that exist locally but not remotely)
    if (syncAllTasks) {
      const externalTaskIds = new Set(externalTasks.map(t => t.id));
      const deletedTasks = existingTasks.filter(t => !externalTaskIds.has(t.externalId));

      for (const deletedTask of deletedTasks) {
        await this.prisma.task.delete({
          where: { id: deletedTask.id },
        });
        result.tasksDeleted++;
      }
    }

    progressCallback?.(100);

    result.success = result.errors.length === 0;
    result.duration = Date.now() - startTime;

    return result;
  }

  /**
   * Perform incremental sync operation
   */
  private async performIncrementalSync(
    integrationId: string,
    workspaceId: string,
    modifiedSince?: Date,
    progressCallback?: (progress: number) => void
  ): Promise<SyncJobResult> {
    const startTime = Date.now();
    const result: SyncJobResult = {
      success: false,
      integrationId,
      jobType: SyncJobType.INCREMENTAL_SYNC,
      tasksProcessed: 0,
      tasksCreated: 0,
      tasksUpdated: 0,
      tasksDeleted: 0,
      conflictsDetected: 0,
      conflictsResolved: 0,
      errors: [],
      conflicts: [],
      duration: 0,
      completedAt: new Date(),
    };

    // Get integration and adapter
    const integration = await this.prisma.integration.findUnique({
      where: { id: integrationId },
    });

    if (!integration) {
      throw new Error(`Integration ${integrationId} not found`);
    }

    const adapter = await this.integrationsService.getAdapter(integration.provider as any);
    if (!adapter) {
      throw new Error(`No adapter found for provider ${integration.provider}`);
    }

    progressCallback?.(20);

    // Fetch modified tasks from external source
    const config = integration.config as any;
    const externalTasks = await adapter.fetchTasks({
      ...config,
      modifiedSince: modifiedSince || integration.lastSyncAt,
    });

    result.tasksProcessed = externalTasks.length;
    progressCallback?.(50);

    // Process each modified task
    for (let i = 0; i < externalTasks.length; i++) {
      const externalTask = externalTasks[i];

      try {
        const existingTask = await this.prisma.task.findUnique({
          where: {
            integrationId_externalId: {
              integrationId,
              externalId: externalTask.id,
            },
          },
        });

        if (existingTask) {
          // Check for conflicts
          const conflicts = await this.conflictResolution.detectConflicts(
            existingTask,
            externalTask,
            integration.lastSyncAt || new Date(0)
          );

          if (conflicts.length > 0) {
            result.conflictsDetected += conflicts.length;
            result.conflicts.push(...conflicts);

            await this.prisma.task.update({
              where: { id: existingTask.id },
              data: { syncStatus: 'conflict' },
            });
          } else {
            await this.updateLocalTask(existingTask.id, externalTask);
            result.tasksUpdated++;
          }
        } else {
          // Create new task
          await this.createLocalTask(integrationId, workspaceId, externalTask);
          result.tasksCreated++;
        }
      } catch (error) {
        result.errors.push(this.createSyncError(error));
      }

      // Update progress
      const progress = 50 + (i / externalTasks.length) * 50;
      progressCallback?.(Math.round(progress));
    }

    result.success = result.errors.length === 0;
    result.duration = Date.now() - startTime;

    return result;
  }

  /**
   * Perform webhook sync operation
   */
  private async performWebhookSync(
    integrationId: string,
    workspaceId: string,
    webhookPayload: any,
    eventType: string,
    externalTaskId?: string
  ): Promise<SyncJobResult> {
    const startTime = Date.now();
    const result: SyncJobResult = {
      success: false,
      integrationId,
      jobType: SyncJobType.WEBHOOK_SYNC,
      tasksProcessed: 1,
      tasksCreated: 0,
      tasksUpdated: 0,
      tasksDeleted: 0,
      conflictsDetected: 0,
      conflictsResolved: 0,
      errors: [],
      conflicts: [],
      duration: 0,
      completedAt: new Date(),
    };

    try {
      // Get integration and adapter
      const integration = await this.prisma.integration.findUnique({
        where: { id: integrationId },
      });

      if (!integration) {
        throw new Error(`Integration ${integrationId} not found`);
      }

      const adapter = await this.integrationsService.getAdapter(integration.provider as any);
      if (!adapter) {
        throw new Error(`No adapter found for provider ${integration.provider}`);
      }

      // Process webhook based on event type
      switch (eventType) {
        case 'task.created':
        case 'task.updated':
          if (externalTaskId) {
            // Fetch the specific task
            const config = integration.config as any;
            const tasks = await adapter.fetchTasks({
              ...config,
              taskIds: [externalTaskId],
            });

            if (tasks.length > 0) {
              const externalTask = tasks[0];
              const existingTask = await this.prisma.task.findUnique({
                where: {
                  integrationId_externalId: {
                    integrationId,
                    externalId: externalTask.id,
                  },
                },
              });

              if (existingTask) {
                await this.updateLocalTask(existingTask.id, externalTask);
                result.tasksUpdated++;
              } else {
                await this.createLocalTask(integrationId, workspaceId, externalTask);
                result.tasksCreated++;
              }
            }
          }
          break;

        case 'task.deleted':
          if (externalTaskId) {
            const deletedTask = await this.prisma.task.findUnique({
              where: {
                integrationId_externalId: {
                  integrationId,
                  externalId: externalTaskId,
                },
              },
            });

            if (deletedTask) {
              await this.prisma.task.delete({
                where: { id: deletedTask.id },
              });
              result.tasksDeleted++;
            }
          }
          break;

        default:
          this.logger.warn(`Unknown webhook event type: ${eventType}`);
      }

      result.success = true;
    } catch (error) {
      result.errors.push(this.createSyncError(error));
    }

    result.duration = Date.now() - startTime;
    return result;
  }

  /**
   * Create a new local task from external task
   */
  private async createLocalTask(
    integrationId: string,
    workspaceId: string,
    externalTask: ExternalTask
  ): Promise<void> {
    await this.prisma.task.create({
      data: {
        workspaceId,
        integrationId,
        externalId: externalTask.id,
        title: externalTask.title,
        description: externalTask.description,
        status: externalTask.status,
        priority: externalTask.priority,
        priorityScore: 0, // Will be calculated by priority service
        assigneeId: externalTask.assigneeId,
        assigneeName: externalTask.assigneeName,
        dueDate: externalTask.dueDate,
        estimatedMinutes: externalTask.estimatedMinutes,
        tags: externalTask.tags || [],
        projectName: externalTask.projectName,
        sourceUrl: externalTask.sourceUrl,
        metadata: externalTask.metadata || {},
        syncStatus: 'synced',
        lastSyncAt: new Date(),
      },
    });
  }

  /**
   * Update existing local task with external task data
   */
  private async updateLocalTask(taskId: string, externalTask: ExternalTask): Promise<void> {
    await this.prisma.task.update({
      where: { id: taskId },
      data: {
        title: externalTask.title,
        description: externalTask.description,
        status: externalTask.status,
        priority: externalTask.priority,
        assigneeId: externalTask.assigneeId,
        assigneeName: externalTask.assigneeName,
        dueDate: externalTask.dueDate,
        estimatedMinutes: externalTask.estimatedMinutes,
        tags: externalTask.tags || [],
        projectName: externalTask.projectName,
        sourceUrl: externalTask.sourceUrl,
        metadata: externalTask.metadata || {},
        syncStatus: 'synced',
        lastSyncAt: new Date(),
      },
    });
  }

  /**
   * Create a sync error from an exception
   */
  private createSyncError(error: any): SyncError {
    return {
      type: this.determineSyncErrorType(error),
      message: error.message || 'Unknown error',
      retryable: this.isRetryableError(error),
      timestamp: new Date(),
      details: {
        stack: error.stack,
        code: error.code,
      },
    };
  }

  /**
   * Determine sync error type from error
   */
  private determineSyncErrorType(error: any): SyncError['type'] {
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return 'NETWORK_ERROR';
    }
    if (error.status === 401 || error.status === 403) {
      return 'AUTH_ERROR';
    }
    if (error.status === 429) {
      return 'RATE_LIMIT_ERROR';
    }
    if (error.status >= 400 && error.status < 500) {
      return 'VALIDATION_ERROR';
    }
    return 'API_ERROR';
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: any): boolean {
    // Network errors are usually retryable
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return true;
    }
    // Rate limit errors are retryable
    if (error.status === 429) {
      return true;
    }
    // Server errors are retryable
    if (error.status >= 500) {
      return true;
    }
    // Auth and validation errors are usually not retryable
    return false;
  }
}