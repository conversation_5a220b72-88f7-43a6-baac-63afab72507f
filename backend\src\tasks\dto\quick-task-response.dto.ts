import { ApiProperty } from '@nestjs/swagger';
import { Task } from '@prisma/client';
import { QuickTaskDestination } from './create-quick-task.dto';

export class QuickTaskResponseDto {
  @ApiProperty({ 
    description: 'Success status',
    example: true
  })
  success: boolean;

  @ApiProperty({ 
    description: 'Success message',
    example: 'Quick task created successfully'
  })
  message: string;

  @ApiProperty({ 
    description: 'Created task details',
    type: 'object'
  })
  task: Task;

  @ApiProperty({ 
    description: 'Destination where the task was created',
    enum: QuickTaskDestination,
    example: QuickTaskDestination.PERSONAL_INBOX
  })
  destination: QuickTaskDestination;

  @ApiProperty({ 
    description: 'External URL if task was created in Google Sheets',
    example: 'https://docs.google.com/spreadsheets/d/abc123/edit#gid=0',
    required: false
  })
  externalUrl?: string;

  @ApiProperty({ 
    description: 'Whether user requested to add another task',
    example: false
  })
  addAnother: boolean;
}