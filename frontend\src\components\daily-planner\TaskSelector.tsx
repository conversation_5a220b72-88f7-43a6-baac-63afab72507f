import React, { useState, useMemo } from 'react'
import { Search, Clock, Calendar } from 'lucide-react'

import { Task } from '@/types/task'

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { TaskStatusBadge } from '@/components/tasks/TaskStatusBadge'
import { TaskPriorityBadge } from '@/components/tasks/TaskPriorityBadge'
import { TimeEstimationInput } from './TimeEstimationInput'

interface TaskSelectorProps {
  isOpen: boolean
  onClose: () => void
  onAddTask: (taskId: string, estimatedMinutes: number) => void
  availableTasks: Task[]
  selectedTask?: Task | null
}

export const TaskSelector: React.FC<TaskSelectorProps> = ({
  isOpen,
  onClose,
  onAddTask,
  availableTasks,
  selectedTask
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(
    selectedTask?.id || null
  )
  const [estimatedMinutes, setEstimatedMinutes] = useState(
    selectedTask?.estimatedMinutes || 30
  )

  // Filter tasks based on search query
  const filteredTasks = useMemo(() => {
    if (!searchQuery.trim()) return availableTasks

    const query = searchQuery.toLowerCase()
    return availableTasks.filter(task =>
      task.title.toLowerCase().includes(query) ||
      task.description?.toLowerCase().includes(query) ||
      task.projectName?.toLowerCase().includes(query) ||
      task.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }, [availableTasks, searchQuery])

  const handleAddTask = () => {
    if (selectedTaskId && estimatedMinutes > 0) {
      onAddTask(selectedTaskId, estimatedMinutes)
    }
  }

  const selectedTaskData = availableTasks.find(task => task.id === selectedTaskId)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Add Task to Daily Plan</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Task List */}
          <ScrollArea className="h-64 border rounded-lg">
            <div className="p-2 space-y-2">
              {filteredTasks.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No tasks found</p>
                </div>
              ) : (
                filteredTasks.map((task) => (
                  <div
                    key={task.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedTaskId === task.id
                        ? 'bg-blue-50 border-blue-200'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => {
                      setSelectedTaskId(task.id)
                      setEstimatedMinutes(task.estimatedMinutes || 30)
                    }}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm">{task.title}</h4>
                        {task.description && (
                          <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                            {task.description}
                          </p>
                        )}
                        
                        <div className="flex items-center space-x-2 mt-2">
                          <TaskStatusBadge status={task.status} />
                          {task.priority && <TaskPriorityBadge priority={task.priority} />}
                          {task.projectName && (
                            <Badge variant="outline" className="text-xs">
                              {task.projectName}
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 ml-2">
                        <div className="flex items-center text-xs text-gray-500">
                          <Clock className="h-3 w-3 mr-1" />
                          {task.estimatedMinutes || 30}m
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {Math.round(task.priorityScore)}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>

          {/* Time Estimation */}
          {selectedTaskData && (
            <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-sm">Selected Task</h4>
              <div className="text-sm text-gray-600">{selectedTaskData.title}</div>
              
              <TimeEstimationInput
                value={estimatedMinutes}
                onChange={setEstimatedMinutes}
                label="Estimated time for this task"
              />
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleAddTask}
              disabled={!selectedTaskId || estimatedMinutes <= 0}
            >
              Add to Plan
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}