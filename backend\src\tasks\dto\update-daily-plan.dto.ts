import { ApiProperty } from '@nestjs/swagger';
import { IsArray, ValidateNested, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { DailyPlanTaskDto } from './create-daily-plan.dto';

export class UpdateDailyPlanDto {
  @ApiProperty({ 
    description: 'Updated tasks for the daily plan',
    type: [DailyPlanTaskDto],
    required: false
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DailyPlanTaskDto)
  tasks?: DailyPlanTaskDto[];
}