/**
 * Queue names for Bull job processing
 */
export const SYNC_QUEUE = 'sync-queue';

/**
 * Job types for sync operations
 */
export enum SyncJobType {
  FULL_SYNC = 'full-sync',
  INCREMENTAL_SYNC = 'incremental-sync',
  WEBHOOK_SYNC = 'webhook-sync',
  CONFLICT_RESOLUTION = 'conflict-resolution',
  RETRY_FAILED = 'retry-failed',
}

/**
 * Job priorities for sync operations
 */
export enum SyncJobPriority {
  LOW = 1,
  NORMAL = 5,
  HIGH = 10,
  CRITICAL = 20,
}

/**
 * Default sync intervals in minutes
 */
export const DEFAULT_SYNC_INTERVALS = {
  FULL_SYNC: 60, // 1 hour
  INCREMENTAL_SYNC: 15, // 15 minutes
  HEALTH_CHECK: 5, // 5 minutes
  CLEANUP: 1440, // 24 hours
};

/**
 * Conflict resolution strategies
 */
export enum ConflictResolutionStrategy {
  LOCAL_WINS = 'local-wins',
  REMOTE_WINS = 'remote-wins',
  MANUAL = 'manual',
  LAST_MODIFIED_WINS = 'last-modified-wins',
}

/**
 * Sync operation types for audit trail
 */
export enum SyncOperationType {
  SYNC = 'SYNC',
  CONFLICT_RESOLUTION = 'CONFLICT_RESOLUTION',
  WEBHOOK_PROCESSING = 'WEBHOOK_PROCESSING',
  RETRY = 'RETRY',
  CLEANUP = 'CLEANUP',
}