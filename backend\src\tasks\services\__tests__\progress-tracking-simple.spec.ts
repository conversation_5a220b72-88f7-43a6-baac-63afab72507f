import { ProgressTrackingService } from '../progress-tracking.service';

describe('ProgressTrackingService - Simple Test', () => {
  let service: ProgressTrackingService;

  const mockPrismaService = {
    task: {
      count: jest.fn(),
      groupBy: jest.fn(),
      findMany: jest.fn(),
    },
    integration: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
  };

  beforeEach(() => {
    service = new ProgressTrackingService(mockPrismaService as any);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should have getCompletionRateReport method', () => {
    expect(service.getCompletionRateReport).toBeDefined();
    expect(typeof service.getCompletionRateReport).toBe('function');
  });

  it('should have getTaskAgingReport method', () => {
    expect(service.getTaskAgingReport).toBeDefined();
    expect(typeof service.getTaskAgingReport).toBe('function');
  });

  it('should have getVelocityReport method', () => {
    expect(service.getVelocityReport).toBeDefined();
    expect(typeof service.getVelocityReport).toBe('function');
  });

  it('should have getReportFilterOptions method', () => {
    expect(service.getReportFilterOptions).toBeDefined();
    expect(typeof service.getReportFilterOptions).toBe('function');
  });
});