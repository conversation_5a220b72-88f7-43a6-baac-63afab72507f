import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { ArrowRight, Users, Zap, Target, Mail, Star } from 'lucide-react'
import toast from 'react-hot-toast'
import { landingService } from '@/services/landing'

export function LandingPage() {
  const [email, setEmail] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleEarlyAccess = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) return

    setIsSubmitting(true)
    try {
      const response = await landingService.submitEarlyAccess({
        email,
        source: 'landing_page',
        utm_source: new URLSearchParams(window.location.search).get('utm_source') || undefined,
        utm_medium: new URLSearchParams(window.location.search).get('utm_medium') || undefined,
        utm_campaign: new URLSearchParams(window.location.search).get('utm_campaign') || undefined,
      })
      
      if (response.waitlistPosition) {
        toast.success(`Thanks! You're #${response.waitlistPosition} on the waitlist.`)
      } else {
        toast.success('Thanks! You\'re on the early access list.')
      }
      setEmail('')
    } catch (error) {
      console.error('Early access signup error:', error)
      toast.error('Something went wrong. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const features = [
    {
      icon: <Target className="h-6 w-6" />,
      title: 'Smart Prioritization',
      description: 'AI-powered task ranking based on deadlines, effort, and business impact'
    },
    {
      icon: <Zap className="h-6 w-6" />,
      title: 'Seamless Integration',
      description: 'Connect Asana, Trello, Jira, Google Sheets, and more in minutes'
    },
    {
      icon: <Users className="h-6 w-6" />,
      title: 'Team Collaboration',
      description: 'Multi-workspace support with role-based access control'
    }
  ]

  const integrations = [
    'Asana', 'Trello', 'Jira', 'ClickUp', 'Monday.com', 'Todoist', 
    'Google Sheets', 'Notion', 'Linear', 'GitHub Issues'
  ]

  const testimonials = [
    {
      name: 'Sarah Chen',
      role: 'Product Manager',
      company: 'TechCorp',
      content: 'Finally, all my scattered tasks in one intelligent view. TaskUnify saves me 2 hours daily.',
      rating: 5
    },
    {
      name: 'Marcus Rodriguez',
      role: 'Engineering Lead',
      company: 'StartupXYZ',
      content: 'The smart prioritization is a game-changer. No more decision fatigue about what to work on next.',
      rating: 5
    },
    {
      name: 'Emily Watson',
      role: 'Operations Director',
      company: 'ScaleUp Inc',
      content: 'Our team productivity increased 40% after consolidating our workflow tools with TaskUnify.',
      rating: 5
    }
  ]

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-[#3A2F77]">TaskUnify</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                to="/auth/login"
                className="text-gray-600 hover:text-[#3A2F77] px-3 py-2 text-sm font-medium"
              >
                Sign In
              </Link>
              <Link
                to="/auth/signup"
                className="bg-[#1DB5A3] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#17a085] transition-colors"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-[#3A2F77] to-[#2A1F5F] text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              Unify your tasks.
              <br />
              <span className="text-[#1DB5A3]">Simplify your day.</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
              Transform scattered workflows into one intelligent, prioritized hub. 
              Connect all your tools and let AI help you focus on what matters most.
            </p>
            
            {/* Early Access Form */}
            <form onSubmit={handleEarlyAccess} className="max-w-md mx-auto mb-8">
              <div className="flex gap-3">
                <div className="flex-1">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email for early access"
                    className="w-full px-4 py-3 rounded-md text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#1DB5A3]"
                    required
                  />
                </div>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-[#1DB5A3] text-white px-6 py-3 rounded-md font-medium hover:bg-[#17a085] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isSubmitting ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  ) : (
                    <>
                      Join Waitlist
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </button>
              </div>
            </form>

            <p className="text-sm text-gray-300">
              🚀 Join 2,500+ professionals already on the waitlist
            </p>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Tired of juggling multiple task tools?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              You're not alone. The average professional uses 6+ different tools to manage their work, 
              leading to scattered priorities, missed deadlines, and decision fatigue.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-[#FF5C5C] rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">6+</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Tools to Check Daily</h3>
              <p className="text-gray-600">Asana, Trello, Slack, Email, Sheets, Jira...</p>
            </div>
            <div className="text-center">
              <div className="bg-[#FF5C5C] rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">2h</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Lost Daily</h3>
              <p className="text-gray-600">Context switching between platforms</p>
            </div>
            <div className="text-center">
              <div className="bg-[#FF5C5C] rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">∞</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Decision Fatigue</h3>
              <p className="text-gray-600">"What should I work on next?"</p>
            </div>
          </div>
        </div>
      </section>

      {/* Solution Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              One hub. All your tasks. Smart priorities.
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              TaskUnify connects all your existing tools and uses AI to create a single, 
              prioritized view of everything you need to do.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                <div className="bg-[#1DB5A3] text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Integrations Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Works with tools you already love
            </h2>
            <p className="text-xl text-gray-600">
              Connect your existing workflow in minutes, not hours
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
            {integrations.map((integration, index) => (
              <div key={index} className="bg-white p-4 rounded-lg border border-gray-200 text-center hover:shadow-md transition-shadow">
                <span className="text-gray-700 font-medium">{integration}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Loved by productive teams
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-[#FFC857] fill-current" />
                  ))}
                </div>
                <p className="text-gray-700 mb-4">"{testimonial.content}"</p>
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-sm text-gray-600">{testimonial.role} at {testimonial.company}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-[#3A2F77] text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to unify your workflow?
          </h2>
          <p className="text-xl mb-8 text-gray-200">
            Join thousands of professionals who've already simplified their day with TaskUnify.
          </p>
          
          <form onSubmit={handleEarlyAccess} className="max-w-md mx-auto mb-8">
            <div className="flex gap-3">
              <div className="flex-1">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="w-full px-4 py-3 rounded-md text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#1DB5A3]"
                  required
                />
              </div>
              <button
                type="submit"
                disabled={isSubmitting}
                className="bg-[#1DB5A3] text-white px-6 py-3 rounded-md font-medium hover:bg-[#17a085] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isSubmitting ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                ) : (
                  <>
                    Get Early Access
                    <Mail className="ml-2 h-4 w-4" />
                  </>
                )}
              </button>
            </div>
          </form>

          <p className="text-sm text-gray-300">
            No spam, ever. Unsubscribe anytime.
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">TaskUnify</h3>
              <p className="text-gray-400">
                Unify your tasks. Simplify your day.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">Features</a></li>
                <li><a href="#" className="hover:text-white">Integrations</a></li>
                <li><a href="#" className="hover:text-white">Pricing</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">About</a></li>
                <li><a href="#" className="hover:text-white">Blog</a></li>
                <li><a href="#" className="hover:text-white">Careers</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">Help Center</a></li>
                <li><a href="#" className="hover:text-white">Contact</a></li>
                <li><a href="#" className="hover:text-white">Privacy</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 TaskUnify. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}