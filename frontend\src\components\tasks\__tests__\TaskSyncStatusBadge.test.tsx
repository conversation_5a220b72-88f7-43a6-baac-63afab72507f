import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { TaskSyncStatusBadge } from '../TaskSyncStatusBadge'
import { SyncStatus } from '@/types/task'

describe('TaskSyncStatusBadge', () => {
  it('should render SYNCED status correctly', () => {
    render(<TaskSyncStatusBadge syncStatus={SyncStatus.SYNCED} />)
    
    const badge = screen.getByText('Synced')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass('bg-green-100', 'text-green-800')
    expect(badge.querySelector('svg')).toBeInTheDocument() // CheckCircle icon
  })

  it('should render PENDING status correctly', () => {
    render(<TaskSyncStatusBadge syncStatus={SyncStatus.PENDING} />)
    
    const badge = screen.getByText('Pending')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass('bg-yellow-100', 'text-yellow-800')
    expect(badge.querySelector('svg')).toBeInTheDocument() // Clock icon
  })

  it('should render ERROR status correctly', () => {
    render(<TaskSyncStatusBadge syncStatus={SyncStatus.ERROR} />)
    
    const badge = screen.getByText('Error')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass('bg-red-100', 'text-red-800')
    expect(badge.querySelector('svg')).toBeInTheDocument() // AlertCircle icon
  })

  it('should render CONFLICT status correctly', () => {
    render(<TaskSyncStatusBadge syncStatus={SyncStatus.CONFLICT} />)
    
    const badge = screen.getByText('Conflict')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass('bg-orange-100', 'text-orange-800')
    expect(badge.querySelector('svg')).toBeInTheDocument() // AlertTriangle icon
  })

  it('should hide icon when showIcon is false', () => {
    render(<TaskSyncStatusBadge syncStatus={SyncStatus.SYNCED} showIcon={false} />)
    
    const badge = screen.getByText('Synced')
    expect(badge.querySelector('svg')).not.toBeInTheDocument()
  })

  it('should apply custom className', () => {
    render(<TaskSyncStatusBadge syncStatus={SyncStatus.SYNCED} className="custom-class" />)
    
    const badge = screen.getByText('Synced')
    expect(badge).toHaveClass('custom-class')
  })
})