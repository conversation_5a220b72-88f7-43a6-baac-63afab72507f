import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { IntegrationConnectionWizard } from '@/components/integrations/IntegrationConnectionWizard'
import { IntegrationManagement } from '@/components/integrations/IntegrationManagement'
import { IntegrationConfigModal } from '@/components/integrations/IntegrationConfigModal'
import { Integration } from '@/types/api'
import toast from 'react-hot-toast'

export function IntegrationsPage() {
  const [showConnectionWizard, setShowConnectionWizard] = useState(false)
  const [showConfigModal, setShowConfigModal] = useState(false)
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null)

  const handleAddIntegration = () => {
    setShowConnectionWizard(true)
  }

  const handleConnectionSuccess = (integration: Integration) => {
    setShowConnectionWizard(false)
    toast.success(`Successfully connected ${integration.name}`)
    // The IntegrationManagement component will automatically refresh
  }

  const handleEditIntegration = (integration: Integration) => {
    setSelectedIntegration(integration)
    setShowConfigModal(true)
  }

  const handleDeleteIntegration = (integration: Integration) => {
    // TODO: Implement delete confirmation dialog
    toast.info('Delete functionality will be implemented')
  }

  const handleConfigSave = (integration: Integration) => {
    setShowConfigModal(false)
    setSelectedIntegration(null)
    // The IntegrationManagement component will automatically refresh
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Integrations</h1>
          <p className="text-gray-600">Connect your favorite tools to unify your tasks</p>
        </div>
        
        <Button onClick={handleAddIntegration}>
          <Plus className="h-4 w-4 mr-2" />
          Add Integration
        </Button>
      </div>
      
      <IntegrationManagement
        onEditIntegration={handleEditIntegration}
        onDeleteIntegration={handleDeleteIntegration}
      />

      <IntegrationConnectionWizard
        isOpen={showConnectionWizard}
        onClose={() => setShowConnectionWizard(false)}
        onSuccess={handleConnectionSuccess}
      />

      <IntegrationConfigModal
        integration={selectedIntegration}
        isOpen={showConfigModal}
        onClose={() => {
          setShowConfigModal(false)
          setSelectedIntegration(null)
        }}
        onSave={handleConfigSave}
      />
    </div>
  )
}