import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { TaskStatusBadge } from '../TaskStatusBadge'
import { TaskStatus } from '@/types/task'

describe('TaskStatusBadge', () => {
  it('should render TODO status correctly', () => {
    render(<TaskStatusBadge status={TaskStatus.TODO} />)
    
    const badge = screen.getByText('To Do')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass('bg-gray-100', 'text-gray-800')
  })

  it('should render IN_PROGRESS status correctly', () => {
    render(<TaskStatusBadge status={TaskStatus.IN_PROGRESS} />)
    
    const badge = screen.getByText('In Progress')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass('bg-blue-100', 'text-blue-800')
  })

  it('should render DONE status correctly', () => {
    render(<TaskStatusBadge status={TaskStatus.DONE} />)
    
    const badge = screen.getByText('Done')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass('bg-green-100', 'text-green-800')
  })

  it('should render CANCELLED status correctly', () => {
    render(<TaskStatusBadge status={TaskStatus.CANCELLED} />)
    
    const badge = screen.getByText('Cancelled')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass('bg-red-100', 'text-red-800')
  })

  it('should apply custom className', () => {
    render(<TaskStatusBadge status={TaskStatus.TODO} className="custom-class" />)
    
    const badge = screen.getByText('To Do')
    expect(badge).toHaveClass('custom-class')
  })
})