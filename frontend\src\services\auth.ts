import { apiService } from './api'
import { 
  LoginRequest, 
  SignupRequest, 
  AuthResponse, 
  OAuthCallbackRequest 
} from '@/types/api'

export const authService = {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    return apiService.post<AuthResponse>('/auth/login', credentials)
  },

  async signup(userData: SignupRequest): Promise<AuthResponse> {
    return apiService.post<AuthResponse>('/auth/signup', userData)
  },

  async oauthCallback(data: OAuthCallbackRequest): Promise<AuthResponse> {
    return apiService.post<AuthResponse>(`/auth/oauth/${data.provider}/callback`, {
      code: data.code,
      state: data.state,
    })
  },

  async refreshToken(): Promise<AuthResponse> {
    return apiService.post<AuthResponse>('/auth/refresh')
  },

  async logout(): Promise<void> {
    return apiService.post<void>('/auth/logout')
  },

  async getProfile(): Promise<AuthResponse['user']> {
    return apiService.get<AuthResponse['user']>('/users/me')
  },
}