import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alog<PERSON>eader, <PERSON>alog<PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Plus, Trash2, Save, X } from 'lucide-react'
import { IntegrationService } from '@/services/integration'
import { Integration, IntegrationConfig, FieldMapping, SyncFilter } from '@/types/api'
import { useWorkspaceStore } from '@/store/workspace'
import toast from 'react-hot-toast'

interface IntegrationConfigModalProps {
  integration: Integration | null
  isOpen: boolean
  onClose: () => void
  onSave: (integration: Integration) => void
}

const FIELD_TRANSFORMS = [
  { value: 'uppercase', label: 'Uppercase' },
  { value: 'lowercase', label: 'Lowercase' },
  { value: 'date', label: 'Date Format' },
  { value: 'number', label: 'Number' },
  { value: 'boolean', label: 'Boolean' }
]

const FILTER_OPERATORS = [
  { value: 'equals', label: 'Equals' },
  { value: 'contains', label: 'Contains' },
  { value: 'startsWith', label: 'Starts With' },
  { value: 'endsWith', label: 'Ends With' },
  { value: 'in', label: 'In List' },
  { value: 'notIn', label: 'Not In List' }
]

const COMMON_FIELDS = [
  'title',
  'description',
  'status',
  'priority',
  'assignee',
  'dueDate',
  'tags',
  'project'
]

export function IntegrationConfigModal({
  integration,
  isOpen,
  onClose,
  onSave
}: IntegrationConfigModalProps) {
  const { currentWorkspace } = useWorkspaceStore()
  const [loading, setSaving] = useState(false)
  const [config, setConfig] = useState<IntegrationConfig>({
    syncInterval: 30,
    enableTwoWaySync: true,
    fieldMappings: [],
    filters: [],
    webhookEnabled: false,
    customSettings: {}
  })
  const [integrationName, setIntegrationName] = useState('')

  useEffect(() => {
    if (integration) {
      setConfig(integration.config)
      setIntegrationName(integration.name)
    }
  }, [integration])

  const handleSave = async () => {
    if (!integration || !currentWorkspace) return

    try {
      setSaving(true)
      const updatedIntegration = await IntegrationService.updateIntegration(
        currentWorkspace.id,
        integration.id,
        {
          name: integrationName,
          config
        }
      )
      
      toast.success('Integration settings saved')
      onSave(updatedIntegration)
      onClose()
    } catch (error) {
      toast.error('Failed to save integration settings')
      console.error('Save error:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleConfigUpdate = (key: keyof IntegrationConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const addFieldMapping = () => {
    const newMapping: FieldMapping = {
      localField: '',
      externalField: '',
      transform: undefined,
      defaultValue: undefined
    }
    
    setConfig(prev => ({
      ...prev,
      fieldMappings: [...prev.fieldMappings, newMapping]
    }))
  }

  const updateFieldMapping = (index: number, field: keyof FieldMapping, value: any) => {
    setConfig(prev => ({
      ...prev,
      fieldMappings: prev.fieldMappings.map((mapping, i) => 
        i === index ? { ...mapping, [field]: value } : mapping
      )
    }))
  }

  const removeFieldMapping = (index: number) => {
    setConfig(prev => ({
      ...prev,
      fieldMappings: prev.fieldMappings.filter((_, i) => i !== index)
    }))
  }

  const addFilter = () => {
    const newFilter: SyncFilter = {
      field: '',
      operator: 'equals',
      value: ''
    }
    
    setConfig(prev => ({
      ...prev,
      filters: [...prev.filters, newFilter]
    }))
  }

  const updateFilter = (index: number, field: keyof SyncFilter, value: any) => {
    setConfig(prev => ({
      ...prev,
      filters: prev.filters.map((filter, i) => 
        i === index ? { ...filter, [field]: value } : filter
      )
    }))
  }

  const removeFilter = (index: number) => {
    setConfig(prev => ({
      ...prev,
      filters: prev.filters.filter((_, i) => i !== index)
    }))
  }

  if (!integration) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Configure {integration.name}</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="sync">Sync Settings</TabsTrigger>
            <TabsTrigger value="mapping">Field Mapping</TabsTrigger>
            <TabsTrigger value="filters">Filters</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Basic Settings</CardTitle>
                <CardDescription>
                  Configure basic integration settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="integration-name">Integration Name</Label>
                  <Input
                    id="integration-name"
                    value={integrationName}
                    onChange={(e) => setIntegrationName(e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="sync-interval">Sync Interval</Label>
                  <Select
                    value={config.syncInterval.toString()}
                    onValueChange={(value) => handleConfigUpdate('syncInterval', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="15">15 minutes</SelectItem>
                      <SelectItem value="30">30 minutes</SelectItem>
                      <SelectItem value="60">1 hour</SelectItem>
                      <SelectItem value="240">4 hours</SelectItem>
                      <SelectItem value="1440">24 hours</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sync" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Synchronization Settings</CardTitle>
                <CardDescription>
                  Configure how tasks are synchronized between TaskUnify and {integration.name}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>Two-way Synchronization</Label>
                    <p className="text-sm text-gray-600">
                      Allow changes made in TaskUnify to sync back to {integration.name}
                    </p>
                  </div>
                  <Switch
                    checked={config.enableTwoWaySync}
                    onCheckedChange={(checked) => handleConfigUpdate('enableTwoWaySync', checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>Real-time Updates (Webhooks)</Label>
                    <p className="text-sm text-gray-600">
                      Receive instant updates when tasks change in {integration.name}
                    </p>
                  </div>
                  <Switch
                    checked={config.webhookEnabled}
                    onCheckedChange={(checked) => handleConfigUpdate('webhookEnabled', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="mapping" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Field Mapping</CardTitle>
                    <CardDescription>
                      Map fields between TaskUnify and {integration.name}
                    </CardDescription>
                  </div>
                  <Button onClick={addFieldMapping} size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Mapping
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {config.fieldMappings.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No field mappings configured. Click "Add Mapping" to create one.
                  </div>
                ) : (
                  <div className="space-y-4">
                    {config.fieldMappings.map((mapping, index) => (
                      <div key={index} className="flex items-center space-x-4 p-4 border rounded-lg">
                        <div className="flex-1">
                          <Label>TaskUnify Field</Label>
                          <Select
                            value={mapping.localField}
                            onValueChange={(value) => updateFieldMapping(index, 'localField', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select field" />
                            </SelectTrigger>
                            <SelectContent>
                              {COMMON_FIELDS.map(field => (
                                <SelectItem key={field} value={field}>
                                  {field.charAt(0).toUpperCase() + field.slice(1)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex-1">
                          <Label>External Field</Label>
                          <Input
                            value={mapping.externalField}
                            onChange={(e) => updateFieldMapping(index, 'externalField', e.target.value)}
                            placeholder="External field name"
                          />
                        </div>

                        <div className="flex-1">
                          <Label>Transform</Label>
                          <Select
                            value={mapping.transform || 'none'}
                            onValueChange={(value) => updateFieldMapping(index, 'transform', value === 'none' ? undefined : value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="No transform" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">No transform</SelectItem>
                              {FIELD_TRANSFORMS.map(transform => (
                                <SelectItem key={transform.value} value={transform.value}>
                                  {transform.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFieldMapping(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="filters" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Sync Filters</CardTitle>
                    <CardDescription>
                      Filter which tasks are synchronized from {integration.name}
                    </CardDescription>
                  </div>
                  <Button onClick={addFilter} size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Filter
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {config.filters.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No filters configured. All tasks will be synchronized.
                  </div>
                ) : (
                  <div className="space-y-4">
                    {config.filters.map((filter, index) => (
                      <div key={index} className="flex items-center space-x-4 p-4 border rounded-lg">
                        <div className="flex-1">
                          <Label>Field</Label>
                          <Select
                            value={filter.field}
                            onValueChange={(value) => updateFilter(index, 'field', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select field" />
                            </SelectTrigger>
                            <SelectContent>
                              {COMMON_FIELDS.map(field => (
                                <SelectItem key={field} value={field}>
                                  {field.charAt(0).toUpperCase() + field.slice(1)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex-1">
                          <Label>Operator</Label>
                          <Select
                            value={filter.operator}
                            onValueChange={(value) => updateFilter(index, 'operator', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {FILTER_OPERATORS.map(operator => (
                                <SelectItem key={operator.value} value={operator.value}>
                                  {operator.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex-1">
                          <Label>Value</Label>
                          <Input
                            value={filter.value}
                            onChange={(e) => updateFilter(index, 'value', e.target.value)}
                            placeholder="Filter value"
                          />
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFilter(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading ? (
              <>
                <Save className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}