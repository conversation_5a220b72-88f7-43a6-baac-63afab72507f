import { apiService } from './api'
import type {
  Task,
  TaskQuery,
  PaginatedTasksResponse,
  UpdateTaskRequest,
  BulkUpdateTasksRequest,
  BulkUpdateTasksResponse,
} from '@/types/task'

export class TaskService {
  /**
   * Get tasks with filtering, sorting, and pagination
   */
  static async getTasks(workspaceId: string, query: TaskQuery = {}): Promise<PaginatedTasksResponse> {
    const params = new URLSearchParams()
    
    // Add pagination params
    if (query.limit !== undefined) params.append('limit', query.limit.toString())
    if (query.offset !== undefined) params.append('offset', query.offset.toString())
    
    // Add sorting params
    if (query.sortBy) params.append('sortBy', query.sortBy)
    if (query.sortOrder) params.append('sortOrder', query.sortOrder)
    
    // Add filter params
    if (query.status) params.append('status', query.status)
    if (query.assigneeId) params.append('assigneeId', query.assigneeId)
    if (query.projectName) params.append('projectName', query.projectName)
    if (query.integrationId) params.append('integrationId', query.integrationId)
    if (query.priority) params.append('priority', query.priority)
    if (query.dueDateFrom) params.append('dueDateFrom', query.dueDateFrom)
    if (query.dueDateTo) params.append('dueDateTo', query.dueDateTo)
    if (query.search) params.append('search', query.search)
    if (query.tags && query.tags.length > 0) params.append('tags', query.tags.join(','))

    return await apiService.get(`/workspaces/${workspaceId}/tasks?${params.toString()}`)
  }

  /**
   * Get a single task by ID
   */
  static async getTask(workspaceId: string, taskId: string): Promise<Task> {
    return await apiService.get(`/workspaces/${workspaceId}/tasks/${taskId}`)
  }

  /**
   * Update a task
   */
  static async updateTask(
    workspaceId: string,
    taskId: string,
    updates: UpdateTaskRequest
  ): Promise<Task> {
    return await apiService.patch(`/workspaces/${workspaceId}/tasks/${taskId}`, updates)
  }

  /**
   * Delete a task
   */
  static async deleteTask(workspaceId: string, taskId: string): Promise<void> {
    await apiService.delete(`/workspaces/${workspaceId}/tasks/${taskId}`)
  }

  /**
   * Bulk update multiple tasks
   */
  static async bulkUpdateTasks(
    workspaceId: string,
    request: BulkUpdateTasksRequest
  ): Promise<BulkUpdateTasksResponse> {
    return await apiService.patch(`/workspaces/${workspaceId}/tasks/bulk`, request)
  }

  /**
   * Search tasks with full-text search
   */
  static async searchTasks(
    workspaceId: string,
    searchQuery: string,
    filters: Omit<TaskQuery, 'search'> = {}
  ): Promise<PaginatedTasksResponse> {
    return this.getTasks(workspaceId, { ...filters, search: searchQuery })
  }

  /**
   * Get unique values for filtering
   */
  static async getFilterOptions(workspaceId: string): Promise<{
    assignees: Array<{ id: string; name: string }>
    projects: string[]
    tags: string[]
    integrations: Array<{ id: string; name: string; provider: string }>
  }> {
    return await apiService.get(`/workspaces/${workspaceId}/tasks/filter-options`)
  }
}