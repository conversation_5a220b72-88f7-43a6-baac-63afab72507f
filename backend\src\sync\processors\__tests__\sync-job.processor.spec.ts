import { Test, TestingModule } from '@nestjs/testing';
import { Job } from 'bull';
import { SyncJobProcessor } from '../sync-job.processor';
import { DatabaseService } from '../../../database/database.service';
import { IntegrationService } from '../../../integrations/services/integration.service';
import { ConflictResolutionService } from '../../services/conflict-resolution.service';
import {
  SyncJobType,
  SyncOperationType,
  ConflictResolutionStrategy,
} from '../../constants/sync.constants';
import {
  FullSyncJobData,
  IncrementalSyncJobData,
  WebhookSyncJobData,
  ConflictResolutionJobData,
  RetryFailedJobData,
} from '../../interfaces/sync-job.interface';
import {
  ExternalTask,
  TaskStatus,
  TaskPriority,
  IntegrationStatus,
  SyncConflict,
  SyncError,
} from '../../../integrations/types';

describe('SyncJobProcessor', () => {
  let processor: SyncJobProcessor;
  let mockPrisma: any;
  let mockIntegrationsService: any;
  let mockConflictResolution: any;

  const mockIntegrationId = 'integration-123';
  const mockWorkspaceId = 'workspace-123';

  beforeEach(async () => {
    mockPrisma = {
      syncLog: {
        create: jest.fn(),
        update: jest.fn(),
      },
      integration: {
        findUnique: jest.fn(),
        update: jest.fn(),
      },
      task: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
    };

    mockIntegrationsService = {
      getAdapter: jest.fn(),
    };

    mockConflictResolution = {
      detectConflicts: jest.fn(),
      resolveConflicts: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SyncJobProcessor,
        {
          provide: DatabaseService,
          useValue: mockPrisma,
        },
        {
          provide: IntegrationService,
          useValue: mockIntegrationsService,
        },
        {
          provide: ConflictResolutionService,
          useValue: mockConflictResolution,
        },
      ],
    }).compile();

    processor = module.get<SyncJobProcessor>(SyncJobProcessor);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('processFullSync', () => {
    it('should successfully process a full sync job', async () => {
      const jobData: FullSyncJobData = {
        integrationId: mockIntegrationId,
        workspaceId: mockWorkspaceId,
        jobType: SyncJobType.FULL_SYNC,
        forceSync: false,
        syncAllTasks: false,
      };

      const mockJob = {
        data: jobData,
        progress: jest.fn(),
      } as unknown as Job<FullSyncJobData>;

      const mockIntegration = {
        id: mockIntegrationId,
        provider: 'asana',
        config: {},
        lastSyncAt: new Date('2023-01-01'),
      };

      const mockExternalTasks: ExternalTask[] = [
        {
          id: 'external-1',
          title: 'Task 1',
          status: TaskStatus.TODO,
          sourceUrl: 'https://example.com/task1',
          createdAt: new Date('2023-01-01'),
          updatedAt: new Date('2023-01-02'),
        },
        {
          id: 'external-2',
          title: 'Task 2',
          status: TaskStatus.IN_PROGRESS,
          sourceUrl: 'https://example.com/task2',
          createdAt: new Date('2023-01-01'),
          updatedAt: new Date('2023-01-02'),
        },
      ];

      const mockAdapter = {
        fetchTasks: jest.fn().mockResolvedValue(mockExternalTasks),
      };

      const mockSyncLog = { id: 'sync-log-123' };

      mockPrisma.syncLog.create.mockResolvedValue(mockSyncLog);
      mockPrisma.integration.findUnique.mockResolvedValue(mockIntegration);
      mockIntegrationsService.getAdapter.mockReturnValue(mockAdapter);
      mockPrisma.task.findMany.mockResolvedValue([]);
      mockConflictResolution.detectConflicts.mockResolvedValue([]);

      const result = await processor.processFullSync(mockJob);

      expect(result.success).toBe(true);
      expect(result.tasksProcessed).toBe(2);
      expect(result.tasksCreated).toBe(2);
      expect(result.tasksUpdated).toBe(0);
      expect(result.errors).toHaveLength(0);
      expect(mockPrisma.task.create).toHaveBeenCalledTimes(2);
      expect(mockPrisma.syncLog.update).toHaveBeenCalledWith({
        where: { id: mockSyncLog.id },
        data: {
          status: 'completed',
          tasksProcessed: 2,
          completedAt: expect.any(Date),
        },
      });
    });

    it('should handle sync errors gracefully', async () => {
      const jobData: FullSyncJobData = {
        integrationId: mockIntegrationId,
        workspaceId: mockWorkspaceId,
        jobType: SyncJobType.FULL_SYNC,
        forceSync: false,
        syncAllTasks: false,
      };

      const mockJob = {
        data: jobData,
        progress: jest.fn(),
      } as unknown as Job<FullSyncJobData>;

      const mockSyncLog = { id: 'sync-log-123' };
      const error = new Error('Sync failed');

      mockPrisma.syncLog.create.mockResolvedValue(mockSyncLog);
      mockPrisma.integration.findUnique.mockRejectedValue(error);

      await expect(processor.processFullSync(mockJob)).rejects.toThrow('Sync failed');

      expect(mockPrisma.syncLog.update).toHaveBeenCalledWith({
        where: { id: mockSyncLog.id },
        data: {
          status: 'failed',
          errors: [expect.objectContaining({
            type: expect.any(String),
            message: 'Sync failed',
            retryable: expect.any(Boolean),
          })],
          completedAt: expect.any(Date),
        },
      });
    });
  });

  describe('processIncrementalSync', () => {
    it('should successfully process an incremental sync job', async () => {
      const jobData: IncrementalSyncJobData = {
        integrationId: mockIntegrationId,
        workspaceId: mockWorkspaceId,
        jobType: SyncJobType.INCREMENTAL_SYNC,
        lastSyncAt: new Date('2023-01-01'),
      };

      const mockJob = {
        data: jobData,
        progress: jest.fn(),
      } as unknown as Job<IncrementalSyncJobData>;

      const mockIntegration = {
        id: mockIntegrationId,
        provider: 'asana',
        config: {},
        lastSyncAt: new Date('2023-01-01'),
      };

      const mockExternalTasks: ExternalTask[] = [
        {
          id: 'external-1',
          title: 'Updated Task',
          status: TaskStatus.DONE,
          sourceUrl: 'https://example.com/task1',
          createdAt: new Date('2023-01-01'),
          updatedAt: new Date('2023-01-02'),
        },
      ];

      const mockExistingTask = {
        id: 'task-123',
        externalId: 'external-1',
        title: 'Old Title',
        status: TaskStatus.TODO,
      };

      const mockAdapter = {
        fetchTasks: jest.fn().mockResolvedValue(mockExternalTasks),
      };

      const mockSyncLog = { id: 'sync-log-123' };

      mockPrisma.syncLog.create.mockResolvedValue(mockSyncLog);
      mockPrisma.integration.findUnique.mockResolvedValue(mockIntegration);
      mockIntegrationsService.getAdapter.mockReturnValue(mockAdapter);
      mockPrisma.task.findUnique.mockResolvedValue(mockExistingTask);
      mockConflictResolution.detectConflicts.mockResolvedValue([]);

      const result = await processor.processIncrementalSync(mockJob);

      expect(result.success).toBe(true);
      expect(result.tasksProcessed).toBe(1);
      expect(result.tasksCreated).toBe(0);
      expect(result.tasksUpdated).toBe(1);
      expect(mockPrisma.task.update).toHaveBeenCalledWith({
        where: { id: mockExistingTask.id },
        data: expect.objectContaining({
          title: 'Updated Task',
          status: TaskStatus.DONE,
          syncStatus: 'synced',
        }),
      });
    });
  });

  describe('processWebhookSync', () => {
    it('should process task.updated webhook event', async () => {
      const jobData: WebhookSyncJobData = {
        integrationId: mockIntegrationId,
        workspaceId: mockWorkspaceId,
        jobType: SyncJobType.WEBHOOK_SYNC,
        webhookPayload: { taskId: 'external-1' },
        eventType: 'task.updated',
        externalTaskId: 'external-1',
      };

      const mockJob = {
        data: jobData,
      } as unknown as Job<WebhookSyncJobData>;

      const mockIntegration = {
        id: mockIntegrationId,
        provider: 'asana',
        config: {},
      };

      const mockExternalTask: ExternalTask = {
        id: 'external-1',
        title: 'Updated via Webhook',
        status: TaskStatus.DONE,
        sourceUrl: 'https://example.com/task1',
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-02'),
      };

      const mockExistingTask = {
        id: 'task-123',
        externalId: 'external-1',
      };

      const mockAdapter = {
        fetchTasks: jest.fn().mockResolvedValue([mockExternalTask]),
      };

      mockPrisma.integration.findUnique.mockResolvedValue(mockIntegration);
      mockIntegrationsService.getAdapter.mockReturnValue(mockAdapter);
      mockPrisma.task.findUnique.mockResolvedValue(mockExistingTask);

      const result = await processor.processWebhookSync(mockJob);

      expect(result.success).toBe(true);
      expect(result.tasksUpdated).toBe(1);
      expect(mockPrisma.task.update).toHaveBeenCalled();
    });

    it('should process task.deleted webhook event', async () => {
      const jobData: WebhookSyncJobData = {
        integrationId: mockIntegrationId,
        workspaceId: mockWorkspaceId,
        jobType: SyncJobType.WEBHOOK_SYNC,
        webhookPayload: { taskId: 'external-1' },
        eventType: 'task.deleted',
        externalTaskId: 'external-1',
      };

      const mockJob = {
        data: jobData,
      } as unknown as Job<WebhookSyncJobData>;

      const mockIntegration = {
        id: mockIntegrationId,
        provider: 'asana',
        config: {},
      };

      const mockExistingTask = {
        id: 'task-123',
        externalId: 'external-1',
      };

      mockPrisma.integration.findUnique.mockResolvedValue(mockIntegration);
      mockPrisma.task.findUnique.mockResolvedValue(mockExistingTask);

      const result = await processor.processWebhookSync(mockJob);

      expect(result.success).toBe(true);
      expect(result.tasksDeleted).toBe(1);
      expect(mockPrisma.task.delete).toHaveBeenCalledWith({
        where: { id: mockExistingTask.id },
      });
    });
  });

  describe('processConflictResolution', () => {
    it('should resolve conflicts successfully', async () => {
      const conflicts: SyncConflict[] = [
        {
          taskId: 'task-123',
          field: 'title',
          localValue: 'Local Title',
          remoteValue: 'Remote Title',
          lastSyncAt: new Date('2023-01-01'),
          conflictedAt: new Date('2023-01-02'),
        },
      ];

      const jobData: ConflictResolutionJobData = {
        integrationId: mockIntegrationId,
        workspaceId: mockWorkspaceId,
        jobType: SyncJobType.CONFLICT_RESOLUTION,
        conflicts,
        strategy: ConflictResolutionStrategy.LOCAL_WINS,
        resolvedBy: 'user-123',
      };

      const mockJob = {
        data: jobData,
      } as unknown as Job<ConflictResolutionJobData>;

      mockConflictResolution.resolveConflicts.mockResolvedValue({
        resolved: conflicts,
        failed: [],
      });

      const result = await processor.processConflictResolution(mockJob);

      expect(result.success).toBe(true);
      expect(result.conflictsResolved).toBe(1);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('processRetryFailed', () => {
    it('should retry a failed job successfully', async () => {
      const originalError: SyncError = {
        type: 'NETWORK_ERROR',
        message: 'Connection failed',
        retryable: true,
        timestamp: new Date(),
      };

      const jobData: RetryFailedJobData = {
        integrationId: mockIntegrationId,
        workspaceId: mockWorkspaceId,
        jobType: SyncJobType.RETRY_FAILED,
        originalJobId: 'original-job-123',
        originalError,
        maxRetries: 3,
      };

      const mockJob = {
        data: jobData,
        progress: jest.fn(),
      } as unknown as Job<RetryFailedJobData>;

      const mockIntegration = {
        id: mockIntegrationId,
        provider: 'asana',
        config: {},
        lastSyncAt: new Date('2023-01-01'),
      };

      const mockAdapter = {
        fetchTasks: jest.fn().mockResolvedValue([]),
      };

      mockPrisma.integration.findUnique.mockResolvedValue(mockIntegration);
      mockIntegrationsService.getAdapter.mockReturnValue(mockAdapter);

      const result = await processor.processRetryFailed(mockJob);

      expect(result.success).toBe(true);
      expect(result.jobType).toBe(SyncJobType.RETRY_FAILED);
    });

    it('should fail retry for non-retryable errors', async () => {
      const originalError: SyncError = {
        type: 'AUTH_ERROR',
        message: 'Invalid credentials',
        retryable: false,
        timestamp: new Date(),
      };

      const jobData: RetryFailedJobData = {
        integrationId: mockIntegrationId,
        workspaceId: mockWorkspaceId,
        jobType: SyncJobType.RETRY_FAILED,
        originalJobId: 'original-job-123',
        originalError,
        maxRetries: 3,
      };

      const mockJob = {
        data: jobData,
        progress: jest.fn(),
      } as unknown as Job<RetryFailedJobData>;

      await expect(processor.processRetryFailed(mockJob)).rejects.toThrow(
        'Original error is not retryable: Invalid credentials'
      );
    });
  });
});