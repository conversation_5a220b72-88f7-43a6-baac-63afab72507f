import { useState, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import { Plus, Settings, Users, Trash2, Edit } from 'lucide-react'
import { useWorkspaceStore, Workspace } from '@/store/workspace'
import { workspaceService } from '@/services/workspace'
import { CreateWorkspaceModal } from '@/components/workspace/CreateWorkspaceModal'
import { WorkspaceSettingsModal } from '@/components/workspace/WorkspaceSettingsModal'
import { MemberManagementModal } from '@/components/workspace/MemberManagementModal'
import { DeleteWorkspaceModal } from '@/components/workspace/DeleteWorkspaceModal'
import toast from 'react-hot-toast'

export function WorkspacesPage() {
  const [searchParams, setSearchParams] = useSearchParams()
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [selectedWorkspace, setSelectedWorkspace] = useState<Workspace | null>(null)
  const [settingsModalOpen, setSettingsModalOpen] = useState(false)
  const [membersModalOpen, setMembersModalOpen] = useState(false)
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const { 
    workspaces, 
    currentWorkspace, 
    setWorkspaces, 
    addWorkspace, 
    updateWorkspace, 
    removeWorkspace,
    switchWorkspace 
  } = useWorkspaceStore()

  useEffect(() => {
    loadWorkspaces()
    
    // Check if we should open create modal from URL params
    if (searchParams.get('action') === 'create') {
      setIsCreateModalOpen(true)
      setSearchParams({}) // Clear the action param
    }
  }, [searchParams, setSearchParams])

  const loadWorkspaces = async () => {
    try {
      setIsLoading(true)
      const workspacesData = await workspaceService.getWorkspaces()
      setWorkspaces(workspacesData)
    } catch (error) {
      toast.error('Failed to load workspaces')
      console.error('Error loading workspaces:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateWorkspace = async (data: { name: string; slug?: string }) => {
    try {
      const newWorkspace = await workspaceService.createWorkspace(data)
      addWorkspace(newWorkspace)
      setIsCreateModalOpen(false)
      toast.success('Workspace created successfully')
    } catch (error) {
      toast.error('Failed to create workspace')
      console.error('Error creating workspace:', error)
    }
  }

  const handleUpdateWorkspace = async (id: string, data: any) => {
    try {
      const updatedWorkspace = await workspaceService.updateWorkspace(id, data)
      updateWorkspace(id, updatedWorkspace)
      setSettingsModalOpen(false)
      setSelectedWorkspace(null)
      toast.success('Workspace updated successfully')
    } catch (error) {
      toast.error('Failed to update workspace')
      console.error('Error updating workspace:', error)
    }
  }

  const handleDeleteWorkspace = async (id: string) => {
    try {
      await workspaceService.deleteWorkspace(id)
      removeWorkspace(id)
      setDeleteModalOpen(false)
      setSelectedWorkspace(null)
      toast.success('Workspace deleted successfully')
    } catch (error) {
      toast.error('Failed to delete workspace')
      console.error('Error deleting workspace:', error)
    }
  }

  const openSettingsModal = (workspace: Workspace) => {
    setSelectedWorkspace(workspace)
    setSettingsModalOpen(true)
  }

  const openMembersModal = (workspace: Workspace) => {
    setSelectedWorkspace(workspace)
    setMembersModalOpen(true)
  }

  const openDeleteModal = (workspace: Workspace) => {
    setSelectedWorkspace(workspace)
    setDeleteModalOpen(true)
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Workspaces</h1>
            <p className="text-gray-600">Manage your workspaces and team members</p>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 text-center text-gray-500">
            <p>Loading workspaces...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Workspaces</h1>
          <p className="text-gray-600">Manage your workspaces and team members</p>
        </div>
        <button 
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center gap-2"
          data-testid="create-workspace-button"
        >
          <Plus className="h-4 w-4" />
          Create Workspace
        </button>
      </div>
      
      {workspaces.length === 0 ? (
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 text-center text-gray-500">
            <p>No workspaces found. Create your first workspace to get started.</p>
          </div>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {workspaces.map((workspace) => (
            <div key={workspace.id} className="bg-white rounded-lg shadow border border-gray-200">
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">
                      {workspace.name}
                    </h3>
                    <p className="text-sm text-gray-500 mb-4">
                      Created {new Date(workspace.createdAt).toLocaleDateString()}
                    </p>
                    {currentWorkspace?.id === workspace.id && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Current
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center justify-between mt-4">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => openSettingsModal(workspace)}
                      className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
                      title="Workspace Settings"
                      data-testid={`settings-button-${workspace.id}`}
                    >
                      <Settings className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => openMembersModal(workspace)}
                      className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
                      title="Manage Members"
                      data-testid={`members-button-${workspace.id}`}
                    >
                      <Users className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => openDeleteModal(workspace)}
                      className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md"
                      title="Delete Workspace"
                      data-testid={`delete-button-${workspace.id}`}
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                  
                  {currentWorkspace?.id !== workspace.id && (
                    <button
                      onClick={() => switchWorkspace(workspace.id)}
                      className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                      data-testid={`switch-button-${workspace.id}`}
                    >
                      Switch to
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modals */}
      <CreateWorkspaceModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateWorkspace}
      />

      {selectedWorkspace && (
        <>
          <WorkspaceSettingsModal
            isOpen={settingsModalOpen}
            onClose={() => {
              setSettingsModalOpen(false)
              setSelectedWorkspace(null)
            }}
            workspace={selectedWorkspace}
            onSubmit={(data) => handleUpdateWorkspace(selectedWorkspace.id, data)}
          />

          <MemberManagementModal
            isOpen={membersModalOpen}
            onClose={() => {
              setMembersModalOpen(false)
              setSelectedWorkspace(null)
            }}
            workspace={selectedWorkspace}
          />

          <DeleteWorkspaceModal
            isOpen={deleteModalOpen}
            onClose={() => {
              setDeleteModalOpen(false)
              setSelectedWorkspace(null)
            }}
            workspace={selectedWorkspace}
            onConfirm={() => handleDeleteWorkspace(selectedWorkspace.id)}
          />
        </>
      )}
    </div>
  )
}