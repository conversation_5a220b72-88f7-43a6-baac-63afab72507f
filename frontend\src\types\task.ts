export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  DONE = 'done',
  CANCELLED = 'cancelled'
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

export enum SyncStatus {
  SYNCED = 'synced',
  PENDING = 'pending',
  ERROR = 'error',
  CONFLICT = 'conflict'
}

export enum TaskSortField {
  PRIORITY_SCORE = 'priorityScore',
  DUE_DATE = 'dueDate',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  TITLE = 'title',
  STATUS = 'status',
}

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

export interface Task {
  id: string
  workspaceId: string
  integrationId: string
  externalId: string
  title: string
  description?: string
  status: TaskStatus
  priority?: TaskPriority
  priorityScore: number
  assigneeId?: string
  assigneeName?: string
  dueDate?: string
  estimatedMinutes?: number
  tags: string[]
  projectName?: string
  sourceUrl: string
  metadata: Record<string, any>
  syncStatus: SyncStatus
  createdAt: string
  updatedAt: string
  lastSyncAt: string
  integration?: {
    id: string
    name: string
    provider: string
  }
}

export interface TaskFilters {
  status?: TaskStatus
  assigneeId?: string
  projectName?: string
  integrationId?: string
  priority?: TaskPriority
  tags?: string[]
  dueDateFrom?: string
  dueDateTo?: string
  search?: string
}

export interface TaskQuery extends TaskFilters {
  limit?: number
  offset?: number
  sortBy?: TaskSortField
  sortOrder?: SortOrder
}

export interface PaginatedTasksResponse {
  tasks: Task[]
  total: number
  count: number
  offset: number
  limit: number
  hasMore: boolean
}

export interface UpdateTaskRequest {
  title?: string
  description?: string
  status?: TaskStatus
  priority?: TaskPriority
  assigneeId?: string
  assigneeName?: string
  dueDate?: string
  estimatedMinutes?: number
  tags?: string[]
  projectName?: string
}

export interface BulkUpdateTasksRequest {
  taskIds: string[]
  updates: {
    status?: TaskStatus
    assigneeId?: string
    assigneeName?: string
    dueDate?: string
    priority?: TaskPriority
    tags?: string[]
  }
}

export interface BulkUpdateTasksResponse {
  updatedCount: number
  errors: Array<{
    taskId: string
    error: string
  }>
}

export interface DailyPlanTask {
  id: string
  taskId: string
  estimatedMinutes: number
  actualMinutes: number | null
  orderIndex: number
  completedAt: Date | null
  task: {
    id: string
    title: string
    description: string | null
    status: TaskStatus
    priority: TaskPriority | null
    priorityScore: number
    dueDate: string | null
    tags: string[]
    projectName: string | null
    sourceUrl: string
  }
}

export interface DailyPlan {
  id: string
  workspaceId: string
  userId: string
  planDate: Date
  totalEstimatedMinutes: number
  totalCompletedMinutes: number
  completionProgress: number
  exceedsRecommendedTime: boolean
  tasks: DailyPlanTask[]
  createdAt: Date
  updatedAt: Date
}

export interface CreateDailyPlanRequest {
  planDate: string
  tasks: Array<{
    taskId: string
    estimatedMinutes: number
    orderIndex: number
  }>
}

export interface UpdateDailyPlanTaskRequest {
  estimatedMinutes?: number
  actualMinutes?: number
  orderIndex?: number
  completedAt?: Date | null
}