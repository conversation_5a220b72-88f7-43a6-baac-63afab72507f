{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@/common/*": ["src/common/*"], "@/auth/*": ["src/auth/*"], "@/tasks/*": ["src/tasks/*"], "@/workspaces/*": ["src/workspaces/*"], "@/integrations/*": ["src/integrations/*"], "@/users/*": ["src/users/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}