import React from 'react'
import { Clock, Minus, Plus } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface TimeEstimationInputProps {
  value: number
  onChange: (value: number) => void
  label?: string
  min?: number
  max?: number
  step?: number
}

export const TimeEstimationInput: React.FC<TimeEstimationInputProps> = ({
  value,
  onChange,
  label = "Estimated time",
  min = 5,
  max = 480, // 8 hours
  step = 5
}) => {
  const handleIncrement = () => {
    const newValue = Math.min(value + step, max)
    onChange(newValue)
  }

  const handleDecrement = () => {
    const newValue = Math.max(value - step, min)
    onChange(newValue)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseInt(e.target.value) || 0
    if (newValue >= min && newValue <= max) {
      onChange(newValue)
    }
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0 && mins > 0) {
      return `${hours}h ${mins}m`
    } else if (hours > 0) {
      return `${hours}h`
    }
    return `${mins}m`
  }

  // Quick preset buttons
  const presets = [15, 30, 60, 90, 120]

  return (
    <div className="space-y-3">
      <Label className="text-sm font-medium flex items-center space-x-2">
        <Clock className="h-4 w-4" />
        <span>{label}</span>
      </Label>

      {/* Manual Input with Controls */}
      <div className="flex items-center space-x-2">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleDecrement}
          disabled={value <= min}
          className="h-8 w-8 p-0"
          aria-label="Decrement time"
        >
          <Minus className="h-4 w-4" />
        </Button>

        <div className="flex items-center space-x-2">
          <Input
            type="number"
            value={value}
            onChange={handleInputChange}
            min={min}
            max={max}
            step={step}
            className="w-20 text-center h-8"
          />
          <span className="text-sm text-gray-500">minutes</span>
        </div>

        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleIncrement}
          disabled={value >= max}
          className="h-8 w-8 p-0"
          aria-label="Increment time"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Duration Display */}
      <div className="text-sm text-gray-600">
        Duration: <span className="font-medium">{formatDuration(value)}</span>
      </div>

      {/* Quick Presets */}
      <div className="flex flex-wrap gap-2">
        <span className="text-xs text-gray-500 self-center">Quick select:</span>
        {presets.map((preset) => (
          <Button
            key={preset}
            type="button"
            variant={value === preset ? "default" : "outline"}
            size="sm"
            onClick={() => onChange(preset)}
            className="h-6 px-2 text-xs"
          >
            {formatDuration(preset)}
          </Button>
        ))}
      </div>

      {/* Validation Messages */}
      {value > 240 && (
        <div className="text-xs text-amber-600">
          ⚠️ This task is estimated to take over 4 hours. Consider breaking it down.
        </div>
      )}
      
      {value < 15 && (
        <div className="text-xs text-blue-600">
          💡 Very short tasks can be batched together for better focus.
        </div>
      )}
    </div>
  )
}