import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { QuickTaskService } from '../quick-task.service';
import { DatabaseService } from '../../../database/database.service';
import { IntegrationService } from '../../../integrations/services/integration.service';
import { TasksService } from '../tasks.service';
import { CreateQuickTaskDto, QuickTaskDestination } from '../../dto/create-quick-task.dto';
import { IntegrationProvider } from '../../../integrations/types';

describe('QuickTaskService', () => {
  let service: QuickTaskService;
  let databaseService: jest.Mocked<DatabaseService>;
  let integrationService: jest.Mocked<IntegrationService>;
  let tasksService: jest.Mocked<TasksService>;

  const mockWorkspaceId = 'workspace-123';
  const mockUserId = 'user-123';

  const mockTask = {
    id: 'task-123',
    workspaceId: mockWorkspaceId,
    integrationId: 'personal',
    externalId: 'personal-123',
    title: 'Test Quick Task',
    description: 'Test description',
    status: 'todo',
    priority: 'medium',
    assigneeId: mockUserId,
    assigneeName: null,
    dueDate: new Date('2024-12-31'),
    estimatedMinutes: null,
    tags: ['quick-task'],
    projectName: null,
    sourceUrl: 'http://localhost:3000/workspaces/workspace-123/tasks',
    metadata: {},
    syncStatus: 'synced',
    priorityScore: 50.0,
    createdAt: new Date(),
    updatedAt: new Date(),
    lastSyncAt: new Date(),
  };

  const mockGoogleSheetsIntegration = {
    id: 'integration-123',
    workspaceId: mockWorkspaceId,
    provider: IntegrationProvider.GOOGLE_SHEETS,
    name: 'Google Sheets Integration',
    config: {},
    encryptedCredentials: 'encrypted-creds',
    status: 'ACTIVE' as const,
    lastSyncAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockExternalTask = {
    id: 'gs_sheet123_Tasks_5',
    title: 'Test Quick Task',
    description: 'Test description',
    status: 'todo' as const,
    priority: 'medium' as const,
    dueDate: new Date('2024-12-31'),
    assigneeId: mockUserId,
    assigneeName: null,
    tags: ['quick-task'],
    projectName: 'Quick Tasks',
    sourceUrl: 'https://docs.google.com/spreadsheets/d/sheet123/edit#gid=0',
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockDatabaseService = {
      integration: {
        findFirst: jest.fn(),
      },
      task: {
        create: jest.fn(),
      },
    };

    const mockIntegrationService = {
      getAdapter: jest.fn(),
    };

    const mockTasksService = {
      createTask: jest.fn(),
      getTask: jest.fn(),
      getWorkspacePrioritizationSettings: jest.fn(),
      updateTaskPriorityScore: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuickTaskService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
        {
          provide: IntegrationService,
          useValue: mockIntegrationService,
        },
        {
          provide: TasksService,
          useValue: mockTasksService,
        },
      ],
    }).compile();

    service = module.get<QuickTaskService>(QuickTaskService);
    databaseService = module.get(DatabaseService);
    integrationService = module.get(IntegrationService);
    tasksService = module.get(TasksService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createQuickTask', () => {
    it('should create quick task in personal inbox by default', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
        description: 'Test description',
        dueDate: '2024-12-31T23:59:59Z',
      };

      tasksService.createTask.mockResolvedValue(mockTask);

      const result = await service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Quick task created successfully in personal inbox');
      expect(result.task).toEqual(mockTask);
      expect(result.destination).toBe(QuickTaskDestination.PERSONAL_INBOX);
      expect(result.addAnother).toBe(false);

      expect(tasksService.createTask).toHaveBeenCalledWith(mockWorkspaceId, {
        title: createQuickTaskDto.title,
        description: createQuickTaskDto.description,
        dueDate: createQuickTaskDto.dueDate,
        status: 'todo',
        priority: 'medium',
        assigneeId: mockUserId,
        tags: ['quick-task'],
        sourceUrl: 'http://localhost:3000/workspaces/workspace-123/tasks',
      });
    });

    it('should create quick task in Google Sheets when specified', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
        description: 'Test description',
        dueDate: '2024-12-31T23:59:59Z',
        destination: QuickTaskDestination.GOOGLE_SHEETS,
      };

      const mockAdapter = {
        createTask: jest.fn().mockResolvedValue(mockExternalTask),
      };

      databaseService.integration.findFirst.mockResolvedValue(mockGoogleSheetsIntegration);
      integrationService.getAdapter.mockReturnValue(mockAdapter);
      databaseService.task.create.mockResolvedValue(mockTask);
      tasksService.getWorkspacePrioritizationSettings.mockResolvedValue({});
      tasksService.updateTaskPriorityScore.mockResolvedValue(mockTask);
      tasksService.getTask.mockResolvedValue(mockTask);

      const result = await service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Quick task created successfully in Google Sheets');
      expect(result.task).toEqual(mockTask);
      expect(result.destination).toBe(QuickTaskDestination.GOOGLE_SHEETS);
      expect(result.externalUrl).toBe(mockExternalTask.sourceUrl);

      expect(databaseService.integration.findFirst).toHaveBeenCalledWith({
        where: {
          workspaceId: mockWorkspaceId,
          provider: IntegrationProvider.GOOGLE_SHEETS,
          status: 'ACTIVE',
        },
      });

      expect(integrationService.getAdapter).toHaveBeenCalledWith(IntegrationProvider.GOOGLE_SHEETS);
      expect(mockAdapter.createTask).toHaveBeenCalledWith({
        title: createQuickTaskDto.title,
        description: createQuickTaskDto.description,
        status: 'todo',
        priority: 'medium',
        dueDate: new Date(createQuickTaskDto.dueDate),
        assigneeId: mockUserId,
        tags: ['quick-task'],
        projectName: 'Quick Tasks',
      });
    });

    it('should throw error when Google Sheets integration not found', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
        destination: QuickTaskDestination.GOOGLE_SHEETS,
      };

      databaseService.integration.findFirst.mockResolvedValue(null);

      await expect(
        service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto)
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto)
      ).rejects.toThrow('No active Google Sheets integration found for this workspace');
    });

    it('should handle addAnother flag correctly', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
        addAnother: true,
      };

      tasksService.createTask.mockResolvedValue(mockTask);

      const result = await service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto);

      expect(result.addAnother).toBe(true);
    });

    it('should handle errors gracefully', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
      };

      tasksService.createTask.mockRejectedValue(new Error('Database error'));

      await expect(
        service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto)
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto)
      ).rejects.toThrow('Failed to create quick task: Database error');
    });
  });

  describe('getQuickTaskPreferences', () => {
    it('should return preferences with Google Sheets integration available', async () => {
      databaseService.integration.findFirst.mockResolvedValue(mockGoogleSheetsIntegration);

      const result = await service.getQuickTaskPreferences(mockWorkspaceId);

      expect(result.hasGoogleSheetsIntegration).toBe(true);
      expect(result.defaultDestination).toBe(QuickTaskDestination.GOOGLE_SHEETS);
      expect(result.keyboardShortcuts).toEqual({
        openQuickTask: 'Ctrl+Shift+T',
        submitAndClose: 'Ctrl+Enter',
        submitAndAddAnother: 'Ctrl+Shift+Enter',
      });
    });

    it('should return preferences without Google Sheets integration', async () => {
      databaseService.integration.findFirst.mockResolvedValue(null);

      const result = await service.getQuickTaskPreferences(mockWorkspaceId);

      expect(result.hasGoogleSheetsIntegration).toBe(false);
      expect(result.defaultDestination).toBe(QuickTaskDestination.PERSONAL_INBOX);
      expect(result.keyboardShortcuts).toEqual({
        openQuickTask: 'Ctrl+Shift+T',
        submitAndClose: 'Ctrl+Enter',
        submitAndAddAnother: 'Ctrl+Shift+Enter',
      });
    });
  });
});