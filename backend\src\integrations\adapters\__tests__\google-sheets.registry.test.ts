/**
 * Test to verify Google Sheets adapter is properly registered
 * 
 * Run with: npx ts-node src/integrations/adapters/__tests__/google-sheets.registry.test.ts
 */

import { Test, TestingModule } from '@nestjs/testing';
import { IntegrationsModule } from '../../integrations.module';
import { IntegrationAdapterRegistry } from '../../services/integration-adapter.registry';
import { GoogleSheetsAdapter } from '../google-sheets.adapter';
import { IntegrationProvider } from '../../types';

async function testAdapterRegistration() {
  console.log('🔧 Google Sheets Adapter Registration Test');
  console.log('==========================================');

  try {
    // Create a test module with the IntegrationsModule
    const module: TestingModule = await Test.createTestingModule({
      imports: [IntegrationsModule],
    }).compile();

    const registry = module.get<IntegrationAdapterRegistry>(IntegrationAdapterRegistry);
    const googleSheetsAdapter = module.get<GoogleSheetsAdapter>(GoogleSheetsAdapter);

    console.log('✅ Module compiled successfully');
    console.log('✅ Registry service available');
    console.log('✅ Google Sheets adapter service available');

    // Test adapter registration
    const hasAdapter = registry.hasAdapter(IntegrationProvider.GOOGLE_SHEETS);
    console.log(`✅ Google Sheets adapter registered: ${hasAdapter}`);

    if (hasAdapter) {
      const adapter = registry.getAdapter(IntegrationProvider.GOOGLE_SHEETS);
      console.log(`✅ Adapter retrieved from registry: ${adapter.constructor.name}`);
      
      // Test adapter capabilities
      const capabilities = registry.getAdapterCapabilities(IntegrationProvider.GOOGLE_SHEETS);
      console.log(`✅ Two-way sync supported: ${capabilities.supportsTwoWaySync}`);
      console.log(`✅ Webhooks supported: ${capabilities.supportsWebhooks}`);
    }

    // Test registry statistics
    const stats = registry.getStats();
    console.log(`✅ Total adapters registered: ${stats.totalAdapters}`);
    console.log(`✅ Providers: [${stats.providers.join(', ')}]`);

    await module.close();
    
    console.log('\n🎉 All registration tests passed!');
    return true;

  } catch (error) {
    console.error('❌ Registration test failed:', error.message);
    return false;
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testAdapterRegistration()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

export { testAdapterRegistration };