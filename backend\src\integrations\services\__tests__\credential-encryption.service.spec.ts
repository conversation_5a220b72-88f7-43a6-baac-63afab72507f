import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { CredentialEncryptionService } from '../credential-encryption.service';
import { OAuthCredentials } from '../../types';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { beforeEach } from 'node:test';
import { describe } from 'node:test';

describe('CredentialEncryptionService', () => {
  let service: CredentialEncryptionService;
  let configService: ConfigService;

  const mockCredentials: OAuthCredentials = {
    accessToken: 'test-access-token',
    refreshToken: 'test-refresh-token',
    expiresAt: new Date('2024-12-31T23:59:59Z'),
    scope: ['read', 'write'],
    tokenType: 'Bearer',
    userId: 'user123',
    userEmail: '<EMAIL>',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CredentialEncryptionService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test-encryption-key-32-characters'),
          },
        },
      ],
    }).compile();

    service = module.get<CredentialEncryptionService>(CredentialEncryptionService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('encrypt', () => {
    it('should encrypt credentials successfully', () => {
      const encrypted = service.encrypt(mockCredentials);
      
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');
      expect(encrypted.length).toBeGreaterThan(0);
      expect(encrypted).not.toContain(mockCredentials.accessToken);
    });

    it('should produce different encrypted strings for same input', () => {
      const encrypted1 = service.encrypt(mockCredentials);
      const encrypted2 = service.encrypt(mockCredentials);
      
      expect(encrypted1).not.toBe(encrypted2);
    });

    it('should handle credentials without optional fields', () => {
      const minimalCredentials: OAuthCredentials = {
        accessToken: 'test-token',
      };

      const encrypted = service.encrypt(minimalCredentials);
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');
    });
  });

  describe('decrypt', () => {
    it('should decrypt credentials successfully', () => {
      const encrypted = service.encrypt(mockCredentials);
      const decrypted = service.decrypt(encrypted);
      
      expect(decrypted).toEqual(mockCredentials);
    });

    it('should handle credentials without optional fields', () => {
      const minimalCredentials: OAuthCredentials = {
        accessToken: 'test-token',
      };

      const encrypted = service.encrypt(minimalCredentials);
      const decrypted = service.decrypt(encrypted);
      
      expect(decrypted).toEqual(minimalCredentials);
    });

    it('should convert date strings back to Date objects', () => {
      const encrypted = service.encrypt(mockCredentials);
      const decrypted = service.decrypt(encrypted);
      
      expect(decrypted.expiresAt).toBeInstanceOf(Date);
      expect(decrypted.expiresAt?.getTime()).toBe(mockCredentials.expiresAt?.getTime());
    });

    it('should throw error for invalid encrypted data', () => {
      expect(() => service.decrypt('invalid-encrypted-data')).toThrow('Credential decryption failed');
    });

    it('should throw error for corrupted encrypted data', () => {
      const encrypted = service.encrypt(mockCredentials);
      const corrupted = encrypted.slice(0, -10) + 'corrupted';
      
      expect(() => service.decrypt(corrupted)).toThrow('Credential decryption failed');
    });
  });

  describe('validate', () => {
    it('should return true for valid encrypted data', () => {
      const encrypted = service.encrypt(mockCredentials);
      const isValid = service.validate(encrypted);
      
      expect(isValid).toBe(true);
    });

    it('should return false for invalid encrypted data', () => {
      const isValid = service.validate('invalid-encrypted-data');
      
      expect(isValid).toBe(false);
    });

    it('should return false for corrupted encrypted data', () => {
      const encrypted = service.encrypt(mockCredentials);
      const corrupted = encrypted.slice(0, -10) + 'corrupted';
      
      const isValid = service.validate(corrupted);
      expect(isValid).toBe(false);
    });
  });

  describe('maskCredentials', () => {
    it('should mask sensitive credential data', () => {
      const masked = service.maskCredentials(mockCredentials);
      
      expect(masked.accessToken).toBe('test-acc...');
      expect(masked.refreshToken).toBe('test-ref...');
      expect(masked.tokenType).toBe(mockCredentials.tokenType);
      expect(masked.scope).toEqual(mockCredentials.scope);
      expect(masked.expiresAt).toBe(mockCredentials.expiresAt);
      expect(masked.userId).toBe(mockCredentials.userId);
      expect(masked.userEmail).toBe(mockCredentials.userEmail);
    });

    it('should handle credentials without tokens', () => {
      const credentialsWithoutTokens: OAuthCredentials = {
        accessToken: '',
        tokenType: 'Bearer',
        scope: ['read'],
      };

      const masked = service.maskCredentials(credentialsWithoutTokens);
      
      expect(masked.accessToken).toBeUndefined();
      expect(masked.refreshToken).toBeUndefined();
      expect(masked.tokenType).toBe('Bearer');
      expect(masked.scope).toEqual(['read']);
    });
  });

  describe('generateEncryptionKey', () => {
    it('should generate a valid encryption key', () => {
      const key = CredentialEncryptionService.generateEncryptionKey();
      
      expect(key).toBeDefined();
      expect(typeof key).toBe('string');
      expect(key.length).toBe(64); // 32 bytes as hex string
      expect(/^[0-9a-f]+$/i.test(key)).toBe(true);
    });

    it('should generate different keys each time', () => {
      const key1 = CredentialEncryptionService.generateEncryptionKey();
      const key2 = CredentialEncryptionService.generateEncryptionKey();
      
      expect(key1).not.toBe(key2);
    });
  });

  describe('error handling', () => {
    it('should throw error if encryption key is not provided', async () => {
      await expect(async () => {
        const module: TestingModule = await Test.createTestingModule({
          providers: [
            CredentialEncryptionService,
            {
              provide: ConfigService,
              useValue: {
                get: jest.fn().mockReturnValue(undefined),
              },
            },
          ],
        }).compile();

        module.get<CredentialEncryptionService>(CredentialEncryptionService);
      }).rejects.toThrow('ENCRYPTION_KEY environment variable is required');
    });
  });
});