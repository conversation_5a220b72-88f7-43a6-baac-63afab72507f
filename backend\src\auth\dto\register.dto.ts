import { ApiProperty } from '@nestjs/swagger';
import { IsE<PERSON>, IsString, <PERSON><PERSON>ength, <PERSON><PERSON>ength } from 'class-validator';

export class RegisterDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User full name',
    example: '<PERSON>',
    minLength: 2,
    maxLength: 255,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'User password',
    example: 'password123',
    minLength: 6,
  })
  @IsString()
  @MinLength(6)
  password: string;
}