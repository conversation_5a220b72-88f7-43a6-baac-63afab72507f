import { ApiPropertyOptional } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Transform } from 'class-transformer';

export class VelocityReportQueryDto {
  @ApiPropertyOptional({
    description: 'Number of weeks to include in the velocity report',
    example: 12,
    minimum: 1,
    maximum: 52,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(52)
  @Transform(({ value }) => parseInt(value, 10))
  weeksBack?: number = 12;
}