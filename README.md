# TaskUnify

TaskUnify is a lightweight SaaS platform that aggregates tasks from multiple project management tools and spreadsheets into a single prioritized workspace with smart prioritization, two-way sync, and minimal setup.

## 🚀 Features

- **Unified Task Inbox**: Aggregate tasks from <PERSON><PERSON>, <PERSON>rell<PERSON>, <PERSON>ra, Click<PERSON>p, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ion, and Google Sheets
- **Smart Prioritization**: AI-powered and rule-based task prioritization
- **Two-way Sync**: Keep your source tools updated while managing everything in one place
- **Daily Planner**: Drag-and-drop task scheduling with time estimates
- **Team Collaboration**: Multi-workspace support with role-based access
- **Real-time Updates**: Webhook and polling-based synchronization

## 🏗️ Tech Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **TailwindCSS** for styling
- **Radix UI** for accessible components
- **React Query** for data fetching and caching
- **Zustand** for state management

### Backend
- **Node.js** with TypeScript
- **NestJS** framework
- **PostgreSQL** for primary database
- **Redis** for caching and job queues
- **BullMQ** for background job processing
- **Prisma** ORM for database management

### Infrastructure
- **Docker** for containerization
- **AWS/Google Cloud** for hosting
- **OAuth 2.0** for secure integrations
- **JWT** for authentication

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Docker (optional)

### Quick Start

1. **Clone and install dependencies**
   ```bash
   git clone <repository-url>
   cd TaskUnify
   npm run setup
   ```

2. **Set up environment variables**
   ```bash
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env
   ```

3. **Start development servers**
   ```bash
   npm run dev
   ```

4. **Using Docker (alternative)**
   ```bash
   npm run docker:dev
   ```

### Environment Configuration

Create `.env` files in both `backend/` and `frontend/` directories. See `.env.example` files for required variables.

## 📁 Project Structure

```
TaskUnify/
├── backend/                 # NestJS API server
│   ├── src/
│   │   ├── auth/           # Authentication module
│   │   ├── integrations/   # Third-party integrations
│   │   ├── tasks/          # Task management
│   │   ├── workspaces/     # Workspace management
│   │   └── common/         # Shared utilities
│   ├── prisma/             # Database schema and migrations
│   └── test/               # Backend tests
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
│   └── public/             # Static assets
├── docs/                   # Documentation
├── docker-compose.yml      # Production Docker setup
├── docker-compose.dev.yml  # Development Docker setup
└── README.md
```

## 🧪 Testing

```bash
# Run all tests
npm run test

# Run backend tests only
npm run test:backend

# Run frontend tests only
npm run test:frontend
```

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Docker Deployment
```bash
npm run docker:prod
```

## 📖 API Documentation

API documentation is available at `/api/docs` when running the development server.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, email <EMAIL> or join our [Discord community](https://discord.gg/taskunify).
