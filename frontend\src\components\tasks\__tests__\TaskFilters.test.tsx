import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { TaskFilters } from '../TaskFilters'
import { TaskStatus, TaskPriority, TaskSortField, SortOrder } from '@/types/task'

describe('TaskFilters', () => {
  const mockOnFiltersChange = vi.fn()
  const mockFilterOptions = {
    assignees: [
      { id: 'user-1', name: '<PERSON>' },
      { id: 'user-2', name: '<PERSON>' }
    ],
    projects: ['Project A', 'Project B'],
    tags: ['urgent', 'bug', 'feature'],
    integrations: [
      { id: 'int-1', name: '<PERSON><PERSON>', provider: 'asana' },
      { id: 'int-2', name: 'Trell<PERSON>', provider: 'trello' }
    ]
  }

  const defaultFilters = {
    limit: 50,
    offset: 0,
    sortBy: TaskSortField.PRIORITY_SCORE,
    sortOrder: SortOrder.DESC
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render search input', () => {
    render(
      <TaskFilters
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    expect(screen.getByPlaceholderText('Search tasks...')).toBeInTheDocument()
  })

  it('should render sorting controls', () => {
    render(
      <TaskFilters
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    expect(screen.getByDisplayValue('Priority Score')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Descending')).toBeInTheDocument()
  })

  it('should show filters button with count when filters are active', () => {
    const filtersWithActive = {
      ...defaultFilters,
      search: 'test',
      status: TaskStatus.TODO,
      priority: TaskPriority.HIGH
    }

    render(
      <TaskFilters
        filters={filtersWithActive}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    expect(screen.getByText('Filters')).toBeInTheDocument()
    expect(screen.getByText('3')).toBeInTheDocument() // Filter count badge
  })

  it('should show clear button when filters are active', () => {
    const filtersWithActive = {
      ...defaultFilters,
      search: 'test'
    }

    render(
      <TaskFilters
        filters={filtersWithActive}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    expect(screen.getByText('Clear')).toBeInTheDocument()
  })

  it('should handle search input with debounce', async () => {
    const user = userEvent.setup()
    
    render(
      <TaskFilters
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    const searchInput = screen.getByPlaceholderText('Search tasks...')
    await user.type(searchInput, 'test query')

    // Should not call immediately
    expect(mockOnFiltersChange).not.toHaveBeenCalled()

    // Should call after debounce delay
    await waitFor(() => {
      expect(mockOnFiltersChange).toHaveBeenCalledWith({
        ...defaultFilters,
        search: 'test query'
      })
    }, { timeout: 500 })
  })

  it('should handle sort field change', async () => {
    const user = userEvent.setup()
    
    render(
      <TaskFilters
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    const sortSelect = screen.getByDisplayValue('Priority Score')
    await user.click(sortSelect)
    
    const dueDateOption = screen.getByText('Due Date')
    await user.click(dueDateOption)

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      ...defaultFilters,
      sortBy: TaskSortField.DUE_DATE
    })
  })

  it('should handle sort order change', async () => {
    const user = userEvent.setup()
    
    render(
      <TaskFilters
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    const orderSelect = screen.getByDisplayValue('Descending')
    await user.click(orderSelect)
    
    const ascOption = screen.getByText('Ascending')
    await user.click(ascOption)

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      ...defaultFilters,
      sortOrder: SortOrder.ASC
    })
  })

  it('should toggle advanced filters', async () => {
    const user = userEvent.setup()
    
    render(
      <TaskFilters
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
        filterOptions={mockFilterOptions}
      />
    )

    const filtersButton = screen.getByText('Filters')
    await user.click(filtersButton)

    // Should show advanced filter options
    expect(screen.getByText('Status')).toBeInTheDocument()
    expect(screen.getByText('Priority')).toBeInTheDocument()
    expect(screen.getByText('Assignee')).toBeInTheDocument()
    expect(screen.getByText('Project')).toBeInTheDocument()
    expect(screen.getByText('Source')).toBeInTheDocument()
  })

  it('should handle status filter change', async () => {
    const user = userEvent.setup()
    
    render(
      <TaskFilters
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
        filterOptions={mockFilterOptions}
      />
    )

    // Open advanced filters
    await user.click(screen.getByText('Filters'))

    // Change status filter
    const statusSelect = screen.getByDisplayValue('All statuses')
    await user.click(statusSelect)
    
    const todoOption = screen.getByText('To Do')
    await user.click(todoOption)

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      ...defaultFilters,
      status: TaskStatus.TODO
    })
  })

  it('should handle assignee filter change', async () => {
    const user = userEvent.setup()
    
    render(
      <TaskFilters
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
        filterOptions={mockFilterOptions}
      />
    )

    // Open advanced filters
    await user.click(screen.getByText('Filters'))

    // Change assignee filter
    const assigneeSelect = screen.getByDisplayValue('All assignees')
    await user.click(assigneeSelect)
    
    const johnOption = screen.getByText('John Doe')
    await user.click(johnOption)

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      ...defaultFilters,
      assigneeId: 'user-1'
    })
  })

  it('should handle date range filters', async () => {
    const user = userEvent.setup()
    
    render(
      <TaskFilters
        filters={defaultFilters}
        onFiltersChange={mockOnFiltersChange}
        filterOptions={mockFilterOptions}
      />
    )

    // Open advanced filters
    await user.click(screen.getByText('Filters'))

    // Set due date from
    const dueDateFromInput = screen.getByLabelText('Due Date From')
    await user.type(dueDateFromInput, '2024-01-01')

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      ...defaultFilters,
      dueDateFrom: '2024-01-01T00:00:00Z'
    })
  })

  it('should clear all filters', async () => {
    const user = userEvent.setup()
    const filtersWithActive = {
      ...defaultFilters,
      search: 'test',
      status: TaskStatus.TODO
    }

    render(
      <TaskFilters
        filters={filtersWithActive}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    const clearButton = screen.getByText('Clear')
    await user.click(clearButton)

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      limit: defaultFilters.limit,
      offset: 0,
      sortBy: defaultFilters.sortBy,
      sortOrder: defaultFilters.sortOrder
    })
  })

  it('should preserve existing search value in input', () => {
    const filtersWithSearch = {
      ...defaultFilters,
      search: 'existing search'
    }

    render(
      <TaskFilters
        filters={filtersWithSearch}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    const searchInput = screen.getByDisplayValue('existing search')
    expect(searchInput).toBeInTheDocument()
  })
})