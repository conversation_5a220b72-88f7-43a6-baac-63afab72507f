import { PrioritizationService } from '../prioritization.service';
import { TaskPriorityData, DEFAULT_PRIORITIZATION_SETTINGS } from '../../interfaces/prioritization.interface';

describe('PrioritizationService Integration', () => {
  let service: PrioritizationService;

  beforeAll(() => {
    service = new PrioritizationService();
  });

  it('should calculate priority scores correctly for real scenarios', () => {
    // High priority overdue task
    const overdueTask: TaskPriorityData = {
      id: 'overdue-1',
      dueDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
      estimatedMinutes: 30,
      priority: 'critical',
      tags: ['urgent', 'client-facing'],
      projectName: 'Client Project',
      assigneeId: 'user-1',
      status: 'todo',
    };

    // Low priority future task
    const futureTask: TaskPriorityData = {
      id: 'future-1',
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      estimatedMinutes: 480,
      priority: 'low',
      tags: ['enhancement'],
      projectName: 'Internal Project',
      assigneeId: 'user-2',
      status: 'todo',
    };

    const overdueScore = service.calculatePriorityScore(overdueTask);
    const futureScore = service.calculatePriorityScore(futureTask);

    console.log('Overdue task score:', overdueScore);
    console.log('Future task score:', futureScore);

    expect(overdueScore).toBeGreaterThan(futureScore);
    expect(overdueScore).toBeGreaterThan(80); // Should be high priority
    expect(futureScore).toBeLessThan(40); // Should be low priority
  });

  it('should validate and normalize weights correctly', () => {
    const validWeights = {
      dueDateProximity: 0.4,
      effortEstimate: 0.2,
      businessImpact: 0.3,
      contextSwitching: 0.1,
    };

    const invalidWeights = {
      dueDateProximity: 0.5,
      effortEstimate: 0.5,
      businessImpact: 0.5,
      contextSwitching: 0.5,
    };

    expect(service.validateWeights(validWeights)).toBe(true);
    expect(service.validateWeights(invalidWeights)).toBe(false);

    const normalized = service.normalizeWeights(invalidWeights);
    expect(service.validateWeights(normalized)).toBe(true);
  });

  it('should return consistent default settings', () => {
    const settings1 = service.getDefaultSettings();
    const settings2 = service.getDefaultSettings();

    expect(settings1).toEqual(settings2);
    expect(settings1).toEqual(DEFAULT_PRIORITIZATION_SETTINGS);
  });
});