import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { UsersService } from './users.service';
import { DatabaseService } from '../database/database.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { afterEach } from 'node:test';
import { beforeEach } from 'node:test';
import { describe } from 'node:test';

describe('UsersService', () => {
  let service: UsersService;
  let mockDatabaseService: {
    user: {
      create: jest.Mock;
      findMany: jest.Mock;
      findUnique: jest.Mock;
      update: jest.Mock;
      delete: jest.Mock;
    };
  };

  const mockUser = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedPassword123',
    avatarUrl: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockUserEntity = {
    id: mockUser.id,
    email: mockUser.email,
    name: mockUser.name,
    avatarUrl: mockUser.avatarUrl,
    createdAt: mockUser.createdAt,
    updatedAt: mockUser.updatedAt,
  };

  beforeEach(async () => {
    mockDatabaseService = {
      user: {
        create: jest.fn(),
        findMany: jest.fn(),
        findUnique: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        { provide: DatabaseService, useValue: mockDatabaseService },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new user', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        name: 'Test User',
        password: 'hashedPassword123',
      };
      mockDatabaseService.user.create.mockResolvedValue(mockUser);

      // Act
      const result = await service.create(createUserDto);

      // Assert
      expect(mockDatabaseService.user.create).toHaveBeenCalledWith({
        data: {
          email: createUserDto.email,
          name: createUserDto.name,
          password: createUserDto.password,
          avatarUrl: createUserDto.avatarUrl,
        },
      });
      expect(result).toEqual(mockUserEntity);
    });

    it('should create a user with avatar URL', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        name: 'Test User',
        avatarUrl: 'https://example.com/avatar.jpg',
      };
      const userWithAvatar = { ...mockUser, avatarUrl: createUserDto.avatarUrl };
      mockDatabaseService.user.create.mockResolvedValue(userWithAvatar);

      // Act
      const result = await service.create(createUserDto);

      // Assert
      expect(mockDatabaseService.user.create).toHaveBeenCalledWith({
        data: {
          email: createUserDto.email,
          name: createUserDto.name,
          password: createUserDto.password,
          avatarUrl: createUserDto.avatarUrl,
        },
      });
      expect(result.avatarUrl).toBe(createUserDto.avatarUrl);
    });
  });

  describe('findAll', () => {
    it('should return all users', async () => {
      // Arrange
      const users = [mockUser, { ...mockUser, id: 'another-id', email: '<EMAIL>' }];
      mockDatabaseService.user.findMany.mockResolvedValue(users);

      // Act
      const result = await service.findAll();

      // Assert
      expect(mockDatabaseService.user.findMany).toHaveBeenCalledWith({
        orderBy: { createdAt: 'desc' },
      });
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(mockUserEntity);
    });
  });

  describe('findById', () => {
    it('should return user if found', async () => {
      // Arrange
      mockDatabaseService.user.findUnique.mockResolvedValue(mockUser);

      // Act
      const result = await service.findById(mockUser.id);

      // Assert
      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({
        where: { id: mockUser.id },
      });
      expect(result).toEqual(mockUserEntity);
    });

    it('should return null if user not found', async () => {
      // Arrange
      mockDatabaseService.user.findUnique.mockResolvedValue(null);

      // Act
      const result = await service.findById('non-existent-id');

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('findByEmail', () => {
    it('should return user if found', async () => {
      // Arrange
      mockDatabaseService.user.findUnique.mockResolvedValue(mockUser);

      // Act
      const result = await service.findByEmail(mockUser.email);

      // Assert
      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({
        where: { email: mockUser.email },
      });
      expect(result).toEqual(mockUserEntity);
    });

    it('should return null if user not found', async () => {
      // Arrange
      mockDatabaseService.user.findUnique.mockResolvedValue(null);

      // Act
      const result = await service.findByEmail('<EMAIL>');

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('findByEmailWithPassword', () => {
    it('should return user with password if found', async () => {
      // Arrange
      mockDatabaseService.user.findUnique.mockResolvedValue(mockUser);

      // Act
      const result = await service.findByEmailWithPassword(mockUser.email);

      // Assert
      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({
        where: { email: mockUser.email },
      });
      expect(result).toEqual({
        ...mockUserEntity,
        password: mockUser.password,
      });
    });

    it('should return null if user not found', async () => {
      // Arrange
      mockDatabaseService.user.findUnique.mockResolvedValue(null);

      // Act
      const result = await service.findByEmailWithPassword('<EMAIL>');

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should update user successfully', async () => {
      // Arrange
      const updateUserDto: UpdateUserDto = {
        name: 'Updated Name',
        avatarUrl: 'https://example.com/new-avatar.jpg',
      };
      const updatedUser = { ...mockUser, ...updateUserDto };
      
      mockDatabaseService.user.findUnique.mockResolvedValue(mockUser);
      mockDatabaseService.user.update.mockResolvedValue(updatedUser);

      // Act
      const result = await service.update(mockUser.id, updateUserDto);

      // Assert
      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({
        where: { id: mockUser.id },
      });
      expect(mockDatabaseService.user.update).toHaveBeenCalledWith({
        where: { id: mockUser.id },
        data: {
          name: updateUserDto.name,
          avatarUrl: updateUserDto.avatarUrl,
        },
      });
      expect(result.name).toBe(updateUserDto.name);
      expect(result.avatarUrl).toBe(updateUserDto.avatarUrl);
    });

    it('should throw NotFoundException if user not found', async () => {
      // Arrange
      const updateUserDto: UpdateUserDto = { name: 'Updated Name' };
      mockDatabaseService.user.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.update('non-existent-id', updateUserDto)).rejects.toThrow(
        NotFoundException,
      );
      expect(mockDatabaseService.user.update).not.toHaveBeenCalled();
    });
  });

  describe('remove', () => {
    it('should delete user successfully', async () => {
      // Arrange
      mockDatabaseService.user.findUnique.mockResolvedValue(mockUser);
      mockDatabaseService.user.delete.mockResolvedValue(mockUser);

      // Act
      await service.remove(mockUser.id);

      // Assert
      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({
        where: { id: mockUser.id },
      });
      expect(mockDatabaseService.user.delete).toHaveBeenCalledWith({
        where: { id: mockUser.id },
      });
    });

    it('should throw NotFoundException if user not found', async () => {
      // Arrange
      mockDatabaseService.user.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.remove('non-existent-id')).rejects.toThrow(
        NotFoundException,
      );
      expect(mockDatabaseService.user.delete).not.toHaveBeenCalled();
    });
  });
});