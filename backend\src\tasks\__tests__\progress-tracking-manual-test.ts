/**
 * Manual test script to verify progress tracking functionality
 * Run with: npx ts-node src/tasks/__tests__/progress-tracking-manual-test.ts
 */

import { ProgressTrackingService } from '../services/progress-tracking.service';
import { ReportExportService } from '../services/report-export.service';

// Simple mock database service with predefined responses
const mockDatabaseService = {
  task: {
    count: async (...args: any[]) => {
      // Return different values based on call order
      if (mockDatabaseService.task.count._callCount === undefined) {
        mockDatabaseService.task.count._callCount = 0;
      }
      mockDatabaseService.task.count._callCount++;
      
      if (mockDatabaseService.task.count._callCount === 1) return 100; // total tasks
      if (mockDatabaseService.task.count._callCount === 2) return 75;  // completed tasks
      return 0;
    },
    groupBy: async (...args: any[]) => {
      return []; // Empty array for simplicity
    },
    findMany: async (...args: any[]) => {
      // Return mock tasks for aging report
      return [
        {
          id: 'task-1',
          title: 'Old Task',
          createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000), // 45 days ago
          dueDate: null,
          status: 'todo',
          projectName: 'Test Project',
          assigneeName: 'John Doe',
          integration: { provider: 'asana', name: 'Asana Integration' },
        },
        {
          id: 'task-2',
          title: 'Recent Task',
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
          dueDate: null,
          status: 'in_progress',
          projectName: 'Test Project',
          assigneeName: 'Jane Smith',
          integration: { provider: 'trello', name: 'Trello Integration' },
        },
      ];
    },
  },
  integration: {
    findUnique: async (...args: any[]) => {
      return {
        provider: 'asana',
        name: 'Asana Integration',
      };
    },
    findMany: async (...args: any[]) => {
      return [
        { provider: 'asana', name: 'Asana Integration' },
        { provider: 'trello', name: 'Trello Integration' },
      ];
    },
  },
} as any;

async function testProgressTracking() {
  console.log('🧪 Testing Progress Tracking Functionality...\n');

  try {
    // Initialize services
    const progressTrackingService = new ProgressTrackingService(mockDatabaseService as any);
    const reportExportService = new ReportExportService();

    console.log('✅ Services initialized successfully');

    // Test 1: Service instantiation
    console.log('\n📊 Test 1: Service Methods');
    console.log('- getCompletionRateReport:', typeof progressTrackingService.getCompletionRateReport);
    console.log('- getTaskAgingReport:', typeof progressTrackingService.getTaskAgingReport);
    console.log('- getVelocityReport:', typeof progressTrackingService.getVelocityReport);
    console.log('- getReportFilterOptions:', typeof progressTrackingService.getReportFilterOptions);
    console.log('✅ All methods are available');

    // Test 2: Mock completion rate report
    console.log('\n📈 Test 2: Completion Rate Report');
    
    // Reset call count for this test
    mockDatabaseService.task.count._callCount = 0;

    const filters = {
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
    };

    const completionReport = await progressTrackingService.getCompletionRateReport('workspace-1', filters);
    
    console.log('- Overall completion rate:', completionReport.overall.completionRate + '%');
    console.log('- Total tasks:', completionReport.overall.totalTasks);
    console.log('- Completed tasks:', completionReport.overall.completedTasks);
    console.log('✅ Completion rate report generated successfully');

    // Test 3: Mock task aging report
    console.log('\n⏰ Test 3: Task Aging Report');

    const agingReport = await progressTrackingService.getTaskAgingReport('workspace-1', filters);
    
    console.log('- Total active tasks:', agingReport.totalActiveTasks);
    console.log('- Age ranges:', agingReport.tasksByAgeRange.length);
    agingReport.tasksByAgeRange.forEach(range => {
      console.log(`  - ${range.ageRange.label}: ${range.taskCount} tasks`);
    });
    console.log('✅ Task aging report generated successfully');

    // Test 4: Mock velocity report
    console.log('\n🚀 Test 4: Velocity Report');

    const velocityReport = await progressTrackingService.getVelocityReport('workspace-1', filters, 4);
    
    console.log('- Weekly data points:', velocityReport.weeklyData.length);
    console.log('- Average tasks per week:', velocityReport.averageTasksPerWeek.toFixed(2));
    console.log('- Trend direction:', velocityReport.trendAnalysis.direction);
    console.log('- Velocity by source:', velocityReport.velocityBySource.length, 'sources');
    console.log('✅ Velocity report generated successfully');

    // Test 5: Report export
    console.log('\n📄 Test 5: Report Export');
    
    const exportResult = await reportExportService.exportReport(
      completionReport,
      'completion' as any,
      'csv' as any
    );
    
    console.log('- Export filename:', exportResult.filename);
    console.log('- Export mime type:', exportResult.mimeType);
    console.log('- Export data size:', exportResult.data.length, 'bytes');
    console.log('- CSV preview (first 100 chars):', exportResult.data.toString('utf-8').substring(0, 100) + '...');
    console.log('✅ Report export successful');

    // Test 6: Filter options
    console.log('\n🔍 Test 6: Filter Options');

    const filterOptions = await progressTrackingService.getReportFilterOptions('workspace-1');
    
    console.log('- Available sources:', filterOptions.sources.length);
    console.log('- Available projects:', filterOptions.projects.length);
    console.log('- Available assignees:', filterOptions.assignees.length);
    console.log('✅ Filter options retrieved successfully');

    console.log('\n🎉 All tests passed! Progress tracking functionality is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// No need for global jest mock anymore

// Run the test
testProgressTracking();