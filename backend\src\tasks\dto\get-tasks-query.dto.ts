import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, IsString, IsArray, IsEnum, IsDateString } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export enum TaskSortField {
  PRIORITY_SCORE = 'priorityScore',
  DUE_DATE = 'dueDate',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  TITLE = 'title',
  STATUS = 'status',
}

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

export class GetTasksQueryDto {
  @ApiProperty({ 
    description: 'Number of tasks to return', 
    example: 20,
    required: false,
    default: 50,
    maximum: 100
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 50;

  @ApiProperty({ 
    description: 'Number of tasks to skip', 
    example: 0,
    required: false,
    default: 0
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  offset?: number = 0;

  @ApiProperty({ 
    description: 'Filter by task status', 
    example: 'todo',
    required: false 
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({ 
    description: 'Filter by assignee ID', 
    example: 'user-123',
    required: false 
  })
  @IsOptional()
  @IsString()
  assigneeId?: string;

  @ApiProperty({ 
    description: 'Filter by project name', 
    example: 'Project Alpha',
    required: false 
  })
  @IsOptional()
  @IsString()
  projectName?: string;

  @ApiProperty({ 
    description: 'Filter by integration ID', 
    example: 'integration-123',
    required: false 
  })
  @IsOptional()
  @IsString()
  integrationId?: string;

  @ApiProperty({ 
    description: 'Filter by priority level', 
    example: 'high',
    required: false 
  })
  @IsOptional()
  @IsString()
  priority?: string;

  @ApiProperty({ 
    description: 'Filter by tags (comma-separated)', 
    example: 'urgent,bug',
    required: false 
  })
  @IsOptional()
  @Transform(({ value }) => typeof value === 'string' ? value.split(',').map(tag => tag.trim()) : value)
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ 
    description: 'Filter by due date from (ISO string)', 
    example: '2024-01-01T00:00:00Z',
    required: false 
  })
  @IsOptional()
  @IsDateString()
  dueDateFrom?: string;

  @ApiProperty({ 
    description: 'Filter by due date to (ISO string)', 
    example: '2024-12-31T23:59:59Z',
    required: false 
  })
  @IsOptional()
  @IsDateString()
  dueDateTo?: string;

  @ApiProperty({ 
    description: 'Search query for full-text search in title and description', 
    example: 'bug fix authentication',
    required: false 
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ 
    description: 'Field to sort by', 
    enum: TaskSortField,
    example: TaskSortField.PRIORITY_SCORE,
    required: false,
    default: TaskSortField.PRIORITY_SCORE
  })
  @IsOptional()
  @IsEnum(TaskSortField)
  sortBy?: TaskSortField = TaskSortField.PRIORITY_SCORE;

  @ApiProperty({ 
    description: 'Sort order', 
    enum: SortOrder,
    example: SortOrder.DESC,
    required: false,
    default: SortOrder.DESC
  })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.DESC;
}