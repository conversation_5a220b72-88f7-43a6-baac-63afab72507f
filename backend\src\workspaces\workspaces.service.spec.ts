import { Test, TestingModule } from '@nestjs/testing';
import {
  NotFoundException,
  ForbiddenException,
  ConflictException,
} from '@nestjs/common';
import { WorkspacesService } from './workspaces.service';
import { DatabaseService } from '../database/database.service';
import { CreateWorkspaceDto } from './dto/create-workspace.dto';
import { UpdateWorkspaceDto } from './dto/update-workspace.dto';
import { InviteMemberDto } from './dto/invite-member.dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import { WorkspaceRole } from './entities/workspace.entity';

describe('WorkspacesService', () => {
  let service: WorkspacesService;
  let mockDatabaseService: {
    workspace: {
      create: jest.Mock;
      findMany: jest.Mock;
      findUnique: jest.Mock;
      update: jest.Mock;
      delete: jest.Mock;
    };
    workspaceMember: {
      create: jest.Mock;
      update: jest.Mock;
      delete: jest.Mock;
      updateMany: jest.Mock;
    };
    user: {
      findUnique: jest.Mock;
    };
    $transaction: jest.Mock;
  };

  const mockUser = {
    id: 'user-1',
    email: '<EMAIL>',
    name: 'Test User',
    avatarUrl: null,
  };

  const mockOwner = {
    id: 'owner-1',
    email: '<EMAIL>',
    name: 'Owner User',
    avatarUrl: null,
  };

  const mockWorkspace = {
    id: 'workspace-1',
    name: 'Test Workspace',
    slug: 'test-workspace',
    ownerId: 'owner-1',
    settings: {},
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockWorkspaceMember = {
    id: 'member-1',
    userId: 'user-1',
    workspaceId: 'workspace-1',
    role: WorkspaceRole.MEMBER,
    permissions: [],
    joinedAt: new Date(),
  };

  const mockOwnerMember = {
    id: 'owner-member-1',
    userId: 'owner-1',
    workspaceId: 'workspace-1',
    role: WorkspaceRole.OWNER,
    permissions: [],
    joinedAt: new Date(),
  };

  const mockWorkspaceWithMembers = {
    ...mockWorkspace,
    members: [
      {
        ...mockOwnerMember,
        user: mockOwner,
      },
      {
        ...mockWorkspaceMember,
        user: mockUser,
      },
    ],
    _count: { members: 2 },
  };

  beforeEach(async () => {
    mockDatabaseService = {
      workspace: {
        create: jest.fn(),
        findMany: jest.fn(),
        findUnique: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
      workspaceMember: {
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
        updateMany: jest.fn(),
      },
      user: {
        findUnique: jest.fn(),
      },
      $transaction: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspacesService,
        { provide: DatabaseService, useValue: mockDatabaseService },
      ],
    }).compile();

    service = module.get<WorkspacesService>(WorkspacesService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new workspace with owner as first member', async () => {
      const createWorkspaceDto: CreateWorkspaceDto = {
        name: 'Test Workspace',
        slug: 'test-workspace',
        settings: { theme: 'dark' },
      };

      mockDatabaseService.workspace.findUnique.mockResolvedValue(null);
      mockDatabaseService.workspace.create.mockResolvedValue(mockWorkspace);

      const result = await service.create(createWorkspaceDto, 'owner-1');

      expect(mockDatabaseService.workspace.findUnique).toHaveBeenCalledWith({
        where: { slug: 'test-workspace' },
      });
      expect(mockDatabaseService.workspace.create).toHaveBeenCalledWith({
        data: {
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: 'owner-1',
          settings: { theme: 'dark' },
          members: {
            create: {
              userId: 'owner-1',
              role: WorkspaceRole.OWNER,
              permissions: [],
            },
          },
        },
      });
      expect(result).toEqual(mockWorkspace);
    });

    it('should generate slug if not provided', async () => {
      const createWorkspaceDto: CreateWorkspaceDto = {
        name: 'Test Workspace With Spaces',
      };

      mockDatabaseService.workspace.findUnique.mockResolvedValue(null);
      mockDatabaseService.workspace.create.mockResolvedValue(mockWorkspace);

      await service.create(createWorkspaceDto, 'owner-1');

      expect(mockDatabaseService.workspace.create).toHaveBeenCalledWith({
        data: {
          name: 'Test Workspace With Spaces',
          slug: 'test-workspace-with-spaces',
          ownerId: 'owner-1',
          settings: {},
          members: {
            create: {
              userId: 'owner-1',
              role: WorkspaceRole.OWNER,
              permissions: [],
            },
          },
        },
      });
    });

    it('should throw ConflictException if slug already exists', async () => {
      const createWorkspaceDto: CreateWorkspaceDto = {
        name: 'Test Workspace',
        slug: 'existing-slug',
      };

      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspace);

      await expect(service.create(createWorkspaceDto, 'owner-1')).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('findAll', () => {
    it('should return all workspaces for user', async () => {
      mockDatabaseService.workspace.findMany.mockResolvedValue([mockWorkspaceWithMembers]);

      const result = await service.findAll('user-1');

      expect(mockDatabaseService.workspace.findMany).toHaveBeenCalledWith({
        where: {
          members: {
            some: {
              userId: 'user-1',
            },
          },
        },
        include: {
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatarUrl: true,
                },
              },
            },
          },
          _count: {
            select: {
              members: true,
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
      });
      expect(result).toHaveLength(1);
      expect(result[0].memberCount).toBe(2);
    });
  });

  describe('findOne', () => {
    it('should return workspace if user has access', async () => {
      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);

      const result = await service.findOne('workspace-1', 'user-1');

      expect(result).toEqual({
        ...mockWorkspaceWithMembers,
        memberCount: 2,
      });
    });

    it('should throw NotFoundException if workspace not found', async () => {
      mockDatabaseService.workspace.findUnique.mockResolvedValue(null);

      await expect(service.findOne('non-existent', 'user-1')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw ForbiddenException if user has no access', async () => {
      const workspaceWithoutUser = {
        ...mockWorkspaceWithMembers,
        members: [{ ...mockOwnerMember, user: mockOwner }],
      };
      mockDatabaseService.workspace.findUnique.mockResolvedValue(workspaceWithoutUser);

      await expect(service.findOne('workspace-1', 'user-1')).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe('update', () => {
    it('should update workspace if user has admin permissions', async () => {
      const updateDto: UpdateWorkspaceDto = {
        name: 'Updated Workspace',
        settings: { theme: 'light' },
      };

      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);
      mockDatabaseService.workspace.update.mockResolvedValue({
        ...mockWorkspace,
        ...updateDto,
      });

      const result = await service.update('workspace-1', updateDto, 'owner-1');

      expect(mockDatabaseService.workspace.update).toHaveBeenCalledWith({
        where: { id: 'workspace-1' },
        data: updateDto,
      });
      expect(result.name).toBe('Updated Workspace');
    });

    it('should throw ForbiddenException if user lacks admin permissions', async () => {
      const updateDto: UpdateWorkspaceDto = { name: 'Updated Workspace' };

      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);

      await expect(service.update('workspace-1', updateDto, 'user-1')).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should check for slug conflicts when updating slug', async () => {
      const updateDto: UpdateWorkspaceDto = { slug: 'existing-slug' };

      mockDatabaseService.workspace.findUnique
        .mockResolvedValueOnce(mockWorkspaceWithMembers)
        .mockResolvedValueOnce(mockWorkspace); // Existing workspace with same slug

      await expect(service.update('workspace-1', updateDto, 'owner-1')).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('remove', () => {
    it('should delete workspace if user is owner', async () => {
      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);
      mockDatabaseService.workspace.delete.mockResolvedValue(mockWorkspace);

      await service.remove('workspace-1', 'owner-1');

      expect(mockDatabaseService.workspace.delete).toHaveBeenCalledWith({
        where: { id: 'workspace-1' },
      });
    });

    it('should throw ForbiddenException if user is not owner', async () => {
      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);

      await expect(service.remove('workspace-1', 'user-1')).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe('inviteMember', () => {
    it('should invite member if user has admin permissions', async () => {
      const inviteDto: InviteMemberDto = {
        email: '<EMAIL>',
        role: WorkspaceRole.MEMBER,
      };

      const newUser = { id: 'new-user-1', email: '<EMAIL>' };
      const newMember = {
        id: 'new-member-1',
        userId: 'new-user-1',
        workspaceId: 'workspace-1',
        role: WorkspaceRole.MEMBER,
        permissions: [],
        joinedAt: new Date(),
      };

      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);
      mockDatabaseService.user.findUnique.mockResolvedValue(newUser);
      mockDatabaseService.workspaceMember.create.mockResolvedValue(newMember);

      const result = await service.inviteMember('workspace-1', inviteDto, 'owner-1');

      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
      expect(mockDatabaseService.workspaceMember.create).toHaveBeenCalledWith({
        data: {
          userId: 'new-user-1',
          workspaceId: 'workspace-1',
          role: WorkspaceRole.MEMBER,
          permissions: [],
        },
      });
      expect(result).toEqual(newMember);
    });

    it('should throw NotFoundException if user email not found', async () => {
      const inviteDto: InviteMemberDto = {
        email: '<EMAIL>',
        role: WorkspaceRole.MEMBER,
      };

      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);
      mockDatabaseService.user.findUnique.mockResolvedValue(null);

      await expect(service.inviteMember('workspace-1', inviteDto, 'owner-1')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw ConflictException if user is already a member', async () => {
      const inviteDto: InviteMemberDto = {
        email: '<EMAIL>', // Already a member
        role: WorkspaceRole.MEMBER,
      };

      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);
      mockDatabaseService.user.findUnique.mockResolvedValue(mockUser);

      await expect(service.inviteMember('workspace-1', inviteDto, 'owner-1')).rejects.toThrow(
        ConflictException,
      );
    });

    it('should throw ForbiddenException if non-owner tries to invite admin', async () => {
      const inviteDto: InviteMemberDto = {
        email: '<EMAIL>',
        role: WorkspaceRole.ADMIN,
      };

      const newUser = { id: 'new-user-1', email: '<EMAIL>' };

      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);
      mockDatabaseService.user.findUnique.mockResolvedValue(newUser);

      await expect(service.inviteMember('workspace-1', inviteDto, 'user-1')).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe('updateMember', () => {
    it('should update member if user has admin permissions', async () => {
      const updateDto: UpdateMemberDto = {
        role: WorkspaceRole.ADMIN,
        permissions: ['read', 'write'],
      };

      const updatedMember = { ...mockWorkspaceMember, ...updateDto };

      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);
      mockDatabaseService.workspaceMember.update.mockResolvedValue(updatedMember);

      const result = await service.updateMember('workspace-1', 'member-1', updateDto, 'owner-1');

      expect(mockDatabaseService.workspaceMember.update).toHaveBeenCalledWith({
        where: { id: 'member-1' },
        data: updateDto,
      });
      expect(result).toEqual(updatedMember);
    });

    it('should throw ForbiddenException if non-owner tries to promote to admin', async () => {
      const updateDto: UpdateMemberDto = { role: WorkspaceRole.ADMIN };

      // Create admin member trying to promote someone
      const adminMember = { ...mockWorkspaceMember, role: WorkspaceRole.ADMIN };
      const workspaceWithAdmin = {
        ...mockWorkspaceWithMembers,
        members: [
          { ...mockOwnerMember, user: mockOwner },
          { ...adminMember, user: mockUser },
        ],
      };

      mockDatabaseService.workspace.findUnique.mockResolvedValue(workspaceWithAdmin);

      await expect(
        service.updateMember('workspace-1', 'member-1', updateDto, 'user-1'),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('removeMember', () => {
    it('should remove member if user has admin permissions', async () => {
      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);
      mockDatabaseService.workspaceMember.delete.mockResolvedValue(mockWorkspaceMember);

      await service.removeMember('workspace-1', 'member-1', 'owner-1');

      expect(mockDatabaseService.workspaceMember.delete).toHaveBeenCalledWith({
        where: { id: 'member-1' },
      });
    });

    it('should throw ForbiddenException when trying to remove owner', async () => {
      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);

      await expect(
        service.removeMember('workspace-1', 'owner-member-1', 'owner-1'),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('leaveWorkspace', () => {
    it('should allow member to leave workspace', async () => {
      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);
      mockDatabaseService.workspaceMember.delete.mockResolvedValue(mockWorkspaceMember);

      await service.leaveWorkspace('workspace-1', 'user-1');

      expect(mockDatabaseService.workspaceMember.delete).toHaveBeenCalledWith({
        where: { id: 'member-1' },
      });
    });

    it('should throw ForbiddenException if owner tries to leave', async () => {
      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);

      await expect(service.leaveWorkspace('workspace-1', 'owner-1')).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe('transferOwnership', () => {
    it('should transfer ownership successfully', async () => {
      const updatedWorkspace = { ...mockWorkspace, ownerId: 'user-1' };

      mockDatabaseService.workspace.findUnique
        .mockResolvedValueOnce(mockWorkspaceWithMembers)
        .mockResolvedValueOnce(updatedWorkspace);
      mockDatabaseService.$transaction.mockResolvedValue([
        updatedWorkspace,
        mockWorkspaceMember,
        mockOwnerMember,
      ]);

      const result = await service.transferOwnership('workspace-1', 'user-1', 'owner-1');

      expect(mockDatabaseService.$transaction).toHaveBeenCalled();
      expect(result).toEqual(updatedWorkspace);
    });

    it('should throw ForbiddenException if non-owner tries to transfer', async () => {
      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);

      await expect(
        service.transferOwnership('workspace-1', 'user-1', 'user-1'),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should throw NotFoundException if new owner is not a member', async () => {
      mockDatabaseService.workspace.findUnique.mockResolvedValue(mockWorkspaceWithMembers);

      await expect(
        service.transferOwnership('workspace-1', 'non-member', 'owner-1'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('permission methods', () => {
    it('should correctly identify admin permissions', () => {
      expect(service.hasAdminPermissions(WorkspaceRole.OWNER)).toBe(true);
      expect(service.hasAdminPermissions(WorkspaceRole.ADMIN)).toBe(true);
      expect(service.hasAdminPermissions(WorkspaceRole.MEMBER)).toBe(false);
    });

    it('should correctly identify owner permissions', () => {
      expect(service.hasOwnerPermissions(WorkspaceRole.OWNER)).toBe(true);
      expect(service.hasOwnerPermissions(WorkspaceRole.ADMIN)).toBe(false);
      expect(service.hasOwnerPermissions(WorkspaceRole.MEMBER)).toBe(false);
    });

    it('should correctly validate workspace access', () => {
      expect(service.canAccessWorkspace(mockWorkspaceMember)).toBe(true);
      expect(service.canAccessWorkspace(undefined)).toBe(false);
    });
  });
});