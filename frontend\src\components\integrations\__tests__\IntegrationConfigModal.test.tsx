import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { IntegrationConfigModal } from '../IntegrationConfigModal'
import { IntegrationService } from '@/services/integration'
import { useWorkspaceStore } from '@/store/workspace'
import { IntegrationProvider, IntegrationStatus } from '@/types/api'

// Mock dependencies
vi.mock('@/services/integration')
vi.mock('@/store/workspace')
vi.mock('react-hot-toast', () => ({
  default: {
    error: vi.fn(),
    success: vi.fn()
  }
}))

const mockIntegrationService = IntegrationService as any
const mockUseWorkspaceStore = useWorkspaceStore as any

const mockIntegration = {
  id: 'integration-1',
  workspaceId: 'workspace-1',
  provider: IntegrationProvider.ASANA,
  name: 'My Asana Integration',
  config: {
    syncInterval: 30,
    enableTwoWaySync: true,
    fieldMappings: [
      {
        localField: 'title',
        externalField: 'name',
        transform: 'uppercase' as const,
        defaultValue: undefined
      }
    ],
    filters: [
      {
        field: 'status',
        operator: 'equals' as const,
        value: 'active'
      }
    ],
    webhookEnabled: false,
    customSettings: {}
  },
  status: IntegrationStatus.ACTIVE,
  lastSyncAt: '2024-01-01T12:00:00Z',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

describe('IntegrationConfigModal', () => {
  const mockOnClose = vi.fn()
  const mockOnSave = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    
    mockUseWorkspaceStore.mockReturnValue({
      currentWorkspace: {
        id: 'workspace-1',
        name: 'Test Workspace'
      }
    })

    mockIntegrationService.updateIntegration.mockResolvedValue({
      ...mockIntegration,
      name: 'Updated Integration'
    })
  })

  it('should render modal with integration details', () => {
    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    expect(screen.getByText('Configure My Asana Integration')).toBeInTheDocument()
    expect(screen.getByDisplayValue('My Asana Integration')).toBeInTheDocument()
  })

  it('should not render when integration is null', () => {
    render(
      <IntegrationConfigModal
        integration={null}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    expect(screen.queryByText('Configure')).not.toBeInTheDocument()
  })

  it('should show all configuration tabs', () => {
    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    expect(screen.getByText('General')).toBeInTheDocument()
    expect(screen.getByText('Sync Settings')).toBeInTheDocument()
    expect(screen.getByText('Field Mapping')).toBeInTheDocument()
    expect(screen.getByText('Filters')).toBeInTheDocument()
  })

  it('should allow updating integration name', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    const nameInput = screen.getByLabelText('Integration Name')
    await user.clear(nameInput)
    await user.type(nameInput, 'Updated Integration Name')

    expect(nameInput).toHaveValue('Updated Integration Name')
  })

  it('should allow updating sync interval', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    const syncIntervalSelect = screen.getByLabelText('Sync Interval')
    await user.click(syncIntervalSelect)
    await user.click(screen.getByText('1 hour'))

    // The select should now show the new value
    expect(screen.getByText('1 hour')).toBeInTheDocument()
  })

  it('should show sync settings tab with toggles', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    // Click on sync settings tab
    await user.click(screen.getByText('Sync Settings'))

    expect(screen.getByText('Two-way Synchronization')).toBeInTheDocument()
    expect(screen.getByText('Real-time Updates (Webhooks)')).toBeInTheDocument()

    // Two-way sync should be enabled by default
    const twoWaySyncSwitch = screen.getByRole('switch', { name: /two-way/i })
    expect(twoWaySyncSwitch).toBeChecked()

    // Webhooks should be disabled by default
    const webhookSwitch = screen.getByRole('switch', { name: /webhooks/i })
    expect(webhookSwitch).not.toBeChecked()
  })

  it('should show existing field mappings', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    // Click on field mapping tab
    await user.click(screen.getByText('Field Mapping'))

    expect(screen.getByDisplayValue('title')).toBeInTheDocument()
    expect(screen.getByDisplayValue('name')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Uppercase')).toBeInTheDocument()
  })

  it('should allow adding new field mapping', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    // Click on field mapping tab
    await user.click(screen.getByText('Field Mapping'))

    // Click add mapping button
    const addButton = screen.getByRole('button', { name: /Add Mapping/i })
    await user.click(addButton)

    // Should have 2 mapping rows now (1 existing + 1 new)
    const localFieldSelects = screen.getAllByText('TaskUnify Field')
    expect(localFieldSelects).toHaveLength(2)
  })

  it('should allow removing field mapping', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    // Click on field mapping tab
    await user.click(screen.getByText('Field Mapping'))

    // Click remove button (trash icon)
    const removeButton = screen.getByRole('button', { name: '' }) // Trash icon button
    await user.click(removeButton)

    // Should show empty state
    expect(screen.getByText('No field mappings configured')).toBeInTheDocument()
  })

  it('should show existing filters', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    // Click on filters tab
    await user.click(screen.getByText('Filters'))

    expect(screen.getByDisplayValue('status')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Equals')).toBeInTheDocument()
    expect(screen.getByDisplayValue('active')).toBeInTheDocument()
  })

  it('should allow adding new filter', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    // Click on filters tab
    await user.click(screen.getByText('Filters'))

    // Click add filter button
    const addButton = screen.getByRole('button', { name: /Add Filter/i })
    await user.click(addButton)

    // Should have 2 filter rows now (1 existing + 1 new)
    const fieldLabels = screen.getAllByText('Field')
    expect(fieldLabels).toHaveLength(2)
  })

  it('should save configuration changes', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    // Update integration name
    const nameInput = screen.getByLabelText('Integration Name')
    await user.clear(nameInput)
    await user.type(nameInput, 'Updated Integration')

    // Click save button
    const saveButton = screen.getByRole('button', { name: /Save Changes/i })
    await user.click(saveButton)

    await waitFor(() => {
      expect(mockIntegrationService.updateIntegration).toHaveBeenCalledWith(
        'workspace-1',
        'integration-1',
        expect.objectContaining({
          name: 'Updated Integration'
        })
      )
    })

    expect(mockOnSave).toHaveBeenCalled()
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('should handle save error gracefully', async () => {
    const user = userEvent.setup()
    
    mockIntegrationService.updateIntegration.mockRejectedValue(new Error('Save failed'))

    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    // Click save button
    const saveButton = screen.getByRole('button', { name: /Save Changes/i })
    await user.click(saveButton)

    await waitFor(() => {
      expect(mockIntegrationService.updateIntegration).toHaveBeenCalled()
    })

    // Should not close modal on error
    expect(mockOnClose).not.toHaveBeenCalled()
    expect(mockOnSave).not.toHaveBeenCalled()
  })

  it('should show loading state during save', async () => {
    const user = userEvent.setup()
    
    // Mock slow save operation
    mockIntegrationService.updateIntegration.mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve(mockIntegration), 1000))
    )

    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    // Click save button
    const saveButton = screen.getByRole('button', { name: /Save Changes/i })
    await user.click(saveButton)

    // Should show saving state
    expect(screen.getByText('Saving...')).toBeInTheDocument()
    expect(saveButton).toBeDisabled()
  })

  it('should close modal when cancel is clicked', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationConfigModal
        integration={mockIntegration}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    const cancelButton = screen.getByRole('button', { name: 'Cancel' })
    await user.click(cancelButton)

    expect(mockOnClose).toHaveBeenCalled()
  })

  it('should show empty state for field mappings when none exist', () => {
    const integrationWithoutMappings = {
      ...mockIntegration,
      config: {
        ...mockIntegration.config,
        fieldMappings: []
      }
    }

    render(
      <IntegrationConfigModal
        integration={integrationWithoutMappings}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    // Click on field mapping tab
    fireEvent.click(screen.getByText('Field Mapping'))

    expect(screen.getByText('No field mappings configured. Click "Add Mapping" to create one.')).toBeInTheDocument()
  })

  it('should show empty state for filters when none exist', () => {
    const integrationWithoutFilters = {
      ...mockIntegration,
      config: {
        ...mockIntegration.config,
        filters: []
      }
    }

    render(
      <IntegrationConfigModal
        integration={integrationWithoutFilters}
        isOpen={true}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    )

    // Click on filters tab
    fireEvent.click(screen.getByText('Filters'))

    expect(screen.getByText('No filters configured. All tasks will be synchronized.')).toBeInTheDocument()
  })
})