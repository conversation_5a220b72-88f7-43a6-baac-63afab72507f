import { useState } from 'react'
import { X, AlertTriangle } from 'lucide-react'
import { Workspace } from '@/store/workspace'

interface DeleteWorkspaceModalProps {
  isOpen: boolean
  onClose: () => void
  workspace: Workspace
  onConfirm: () => Promise<void>
}

export function DeleteWorkspaceModal({ isOpen, onClose, workspace, onConfirm }: DeleteWorkspaceModalProps) {
  const [confirmationText, setConfirmationText] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)

  const isConfirmationValid = confirmationText === workspace.name

  const handleConfirm = async () => {
    if (!isConfirmationValid) return

    setIsDeleting(true)
    try {
      await onConfirm()
      setConfirmationText('')
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setIsDeleting(false)
    }
  }

  const handleClose = () => {
    if (!isDeleting) {
      setConfirmationText('')
      onClose()
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <h2 className="text-lg font-semibold text-gray-900">Delete Workspace</h2>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
              disabled={isDeleting}
              data-testid="close-modal-button"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="p-6">
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-4">
                Are you sure you want to delete the workspace <strong>"{workspace.name}"</strong>?
              </p>
              
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                <div className="text-sm text-red-800">
                  <p className="font-medium mb-2">This action cannot be undone. This will permanently:</p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Delete all workspace data and settings</li>
                    <li>Remove all team members from the workspace</li>
                    <li>Disconnect all integrations</li>
                    <li>Delete all tasks and sync history</li>
                  </ul>
                </div>
              </div>

              <p className="text-sm text-gray-600 mb-2">
                Please type <strong>{workspace.name}</strong> to confirm:
              </p>
              
              <input
                type="text"
                value={confirmationText}
                onChange={(e) => setConfirmationText(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder={workspace.name}
                disabled={isDeleting}
                data-testid="confirmation-input"
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50"
                disabled={isDeleting}
                data-testid="cancel-button"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirm}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 disabled:opacity-50"
                disabled={!isConfirmationValid || isDeleting}
                data-testid="delete-button"
              >
                {isDeleting ? 'Deleting...' : 'Delete Workspace'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}