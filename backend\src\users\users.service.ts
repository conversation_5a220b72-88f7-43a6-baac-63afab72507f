import { Injectable, NotFoundException } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';

@Injectable()
export class UsersService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = await this.databaseService.user.create({
      data: {
        email: createUserDto.email,
        name: createUserDto.name,
        password: createUserDto.password, // Hashed password from auth service
        avatarUrl: createUserDto.avatarUrl,
      },
    });

    return this.mapToUserEntity(user);
  }

  async findAll(): Promise<User[]> {
    const users = await this.databaseService.user.findMany({
      orderBy: { createdAt: 'desc' },
    });

    return users.map(this.mapToUserEntity);
  }

  async findById(id: string): Promise<User | null> {
    const user = await this.databaseService.user.findUnique({
      where: { id },
    });

    return user ? this.mapToUserEntity(user) : null;
  }

  async findByEmail(email: string): Promise<User | null> {
    const user = await this.databaseService.user.findUnique({
      where: { email },
    });

    return user ? this.mapToUserEntity(user) : null;
  }

  async findByEmailWithPassword(email: string): Promise<(User & { password?: string }) | null> {
    const user = await this.databaseService.user.findUnique({
      where: { email },
    });

    if (!user) {
      return null;
    }

    return {
      ...this.mapToUserEntity(user),
      password: user.password,
    };
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const existingUser = await this.findById(id);
    if (!existingUser) {
      throw new NotFoundException('User not found');
    }

    const updatedUser = await this.databaseService.user.update({
      where: { id },
      data: {
        name: updateUserDto.name,
        avatarUrl: updateUserDto.avatarUrl,
      },
    });

    return this.mapToUserEntity(updatedUser);
  }

  async remove(id: string): Promise<void> {
    const existingUser = await this.findById(id);
    if (!existingUser) {
      throw new NotFoundException('User not found');
    }

    await this.databaseService.user.delete({
      where: { id },
    });
  }

  private mapToUserEntity(user: any): User {
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      avatarUrl: user.avatarUrl,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }
}