# Development Guide

This guide covers the development setup and workflow for TaskUnify.

## Prerequisites

- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Docker (optional but recommended)

## Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd TaskUnify
npm run setup
```

### 2. Environment Configuration

Copy the environment files and configure them:

```bash
# Backend environment
cp backend/.env.example backend/.env

# Frontend environment
cp frontend/.env.example frontend/.env

# Root environment (for Docker)
cp .env.example .env
```

### 3. Database Setup

If using Docker:
```bash
npm run docker:dev
```

If using local PostgreSQL:
```bash
cd backend
npm run prisma:migrate
npm run prisma:seed
```

### 4. Start Development

```bash
# Start both frontend and backend
npm run dev

# Or start individually
npm run dev:backend
npm run dev:frontend
```

## Project Structure

```
TaskUnify/
├── backend/                 # NestJS API server
│   ├── src/
│   │   ├── auth/           # Authentication module
│   │   ├── integrations/   # Third-party integrations
│   │   ├── tasks/          # Task management
│   │   ├── workspaces/     # Workspace management
│   │   ├── users/          # User management
│   │   └── common/         # Shared utilities
│   ├── prisma/             # Database schema and migrations
│   └── test/               # Backend tests
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API services
│   │   ├── store/          # State management
│   │   └── utils/          # Utility functions
│   └── public/             # Static assets
└── docs/                   # Documentation
```

## Development Workflow

### 1. Feature Development

1. Create a feature branch from `main`
2. Implement the feature following our coding standards
3. Write tests for your changes
4. Update documentation if needed
5. Submit a pull request

### 2. Code Quality

We use several tools to maintain code quality:

- **ESLint**: Linting for TypeScript/JavaScript
- **Prettier**: Code formatting
- **Husky**: Git hooks for pre-commit checks
- **Jest/Vitest**: Testing frameworks

Run quality checks:
```bash
npm run lint
npm run test
```

### 3. Database Changes

When making database changes:

1. Update the Prisma schema in `backend/prisma/schema.prisma`
2. Generate a migration: `npm run prisma:migrate`
3. Update seed data if needed in `backend/prisma/seed.ts`

### 4. API Development

- All API endpoints should be documented with Swagger decorators
- Use DTOs for request/response validation
- Implement proper error handling
- Add unit and integration tests

### 5. Frontend Development

- Use TypeScript for all components
- Follow the component structure in `src/components`
- Use React Query for data fetching
- Implement responsive design with Tailwind CSS

## Testing

### Backend Testing

```bash
cd backend

# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

### Frontend Testing

```bash
cd frontend

# Unit tests
npm run test

# UI tests
npm run test:ui

# Coverage
npm run test:coverage
```

## Debugging

### Backend Debugging

1. Start the backend in debug mode:
   ```bash
   cd backend
   npm run start:debug
   ```

2. Attach your debugger to port 9229

### Frontend Debugging

1. Use browser developer tools
2. React Developer Tools extension
3. Redux DevTools for state management

## Environment Variables

### Backend Environment Variables

Key variables to configure:

- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `JWT_SECRET`: Secret for JWT tokens
- `GOOGLE_CLIENT_ID/SECRET`: Google OAuth credentials
- Integration API keys for Asana, Trello, etc.

### Frontend Environment Variables

Key variables to configure:

- `VITE_API_URL`: Backend API URL
- `VITE_GOOGLE_CLIENT_ID`: Google OAuth client ID

## Common Issues

### Database Connection Issues

1. Ensure PostgreSQL is running
2. Check DATABASE_URL format
3. Verify database exists and user has permissions

### Redis Connection Issues

1. Ensure Redis is running
2. Check REDIS_URL format
3. Verify Redis is accessible

### Build Issues

1. Clear node_modules and reinstall:
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

2. Clear build cache:
   ```bash
   npm run build -- --clean
   ```

## Performance Tips

### Backend Performance

- Use database indexes for frequently queried fields
- Implement caching with Redis
- Use background jobs for heavy operations
- Monitor API response times

### Frontend Performance

- Use React.memo for expensive components
- Implement virtual scrolling for large lists
- Optimize bundle size with code splitting
- Use React Query for efficient data fetching

## Deployment

See [DEPLOYMENT.md](./DEPLOYMENT.md) for production deployment instructions.

## Contributing

See [CONTRIBUTING.md](./CONTRIBUTING.md) for contribution guidelines.
