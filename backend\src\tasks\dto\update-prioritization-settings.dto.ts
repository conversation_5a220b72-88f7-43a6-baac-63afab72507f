import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class PriorityWeightsDto {
  @ApiProperty({ 
    description: 'Weight for due date proximity (0.0 - 1.0)', 
    minimum: 0, 
    maximum: 1,
    example: 0.4 
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  dueDateProximity: number;

  @ApiProperty({ 
    description: 'Weight for effort estimate (0.0 - 1.0)', 
    minimum: 0, 
    maximum: 1,
    example: 0.2 
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  effortEstimate: number;

  @ApiProperty({ 
    description: 'Weight for business impact (0.0 - 1.0)', 
    minimum: 0, 
    maximum: 1,
    example: 0.3 
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  businessImpact: number;

  @ApiProperty({ 
    description: 'Weight for context switching (0.0 - 1.0)', 
    minimum: 0, 
    maximum: 1,
    example: 0.1 
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  contextSwitching: number;
}

export class EffortThresholdsDto {
  @ApiProperty({ 
    description: 'Quick task threshold in minutes', 
    example: 30 
  })
  @IsNumber()
  @Min(1)
  quick: number;

  @ApiProperty({ 
    description: 'Medium task threshold in minutes', 
    example: 120 
  })
  @IsNumber()
  @Min(1)
  medium: number;

  @ApiProperty({ 
    description: 'Large task threshold in minutes', 
    example: 480 
  })
  @IsNumber()
  @Min(1)
  large: number;
}

export class UpdatePrioritizationSettingsDto {
  @ApiProperty({ 
    description: 'Priority calculation weights',
    type: PriorityWeightsDto,
    required: false 
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PriorityWeightsDto)
  weights?: PriorityWeightsDto;

  @ApiProperty({ 
    description: 'Maximum priority score', 
    example: 100,
    required: false 
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxScore?: number;

  @ApiProperty({ 
    description: 'Number of days to consider for due date urgency', 
    example: 7,
    required: false 
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  dueDateUrgencyDays?: number;

  @ApiProperty({ 
    description: 'Effort estimation thresholds',
    type: EffortThresholdsDto,
    required: false 
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => EffortThresholdsDto)
  effortThresholds?: EffortThresholdsDto;

  @ApiProperty({ 
    description: 'Tags that indicate high business impact',
    type: [String],
    example: ['urgent', 'critical', 'high-priority', 'client-facing'],
    required: false 
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  businessImpactTags?: string[];

  @ApiProperty({ 
    description: 'Penalty for context switching (0.0 - 1.0)', 
    minimum: 0, 
    maximum: 1,
    example: 0.1,
    required: false 
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  contextSwitchingPenalty?: number;
}