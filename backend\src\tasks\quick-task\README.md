# Quick Task Creation System

The Quick Task Creation System provides a fast-entry mechanism for users to capture tasks quickly without interrupting their workflow. Tasks can be created in either the personal TaskUnify inbox or directly in Google Sheets.

## Features

- **Fast Entry Form**: Minimal form with title, description, and due date fields
- **Dual Destinations**: Create tasks in personal inbox or Google Sheets
- **Keyboard Shortcuts**: Support for rapid task entry via keyboard shortcuts
- **Automatic Assignment**: Tasks are automatically assigned to the current user
- **Workspace Context**: Tasks are created within the current workspace context
- **Visual Confirmation**: Immediate feedback with option to add another task
- **30-Second Sync**: Google Sheets tasks are synced within 30 seconds (Requirement 7.3)

## API Endpoints

### Create Quick Task
```http
POST /workspaces/:workspaceId/tasks/quick
```

**Request Body:**
```json
{
  "title": "Review quarterly reports",
  "description": "Review Q4 financial reports and prepare summary",
  "dueDate": "2024-12-31T23:59:59Z",
  "destination": "personal_inbox",
  "addAnother": false
}
```

**Response:**
```json
{
  "success": true,
  "message": "Quick task created successfully in personal inbox",
  "task": {
    "id": "task-123",
    "title": "Review quarterly reports",
    "status": "todo",
    "priority": "medium",
    "assigneeId": "user-123",
    "tags": ["quick-task"],
    "sourceUrl": "http://localhost:3000/workspaces/workspace-123/tasks"
  },
  "destination": "personal_inbox",
  "addAnother": false
}
```

### Get Quick Task Preferences
```http
GET /workspaces/:workspaceId/tasks/quick/preferences
```

**Response:**
```json
{
  "hasGoogleSheetsIntegration": true,
  "defaultDestination": "google_sheets",
  "keyboardShortcuts": {
    "openQuickTask": "Ctrl+Shift+T",
    "submitAndClose": "Ctrl+Enter",
    "submitAndAddAnother": "Ctrl+Shift+Enter"
  }
}
```

## Destinations

### Personal Inbox
- Tasks are created directly in TaskUnify
- Stored in the database with `integrationId: 'personal'`
- Tagged with `'quick-task'` for easy identification
- Immediately available in the unified task view

### Google Sheets
- Requires active Google Sheets integration
- Tasks are appended to the configured spreadsheet
- Synced to TaskUnify database within 30 seconds
- External URL provided for direct access to the sheet

## Implementation Details

### Service Architecture
```typescript
QuickTaskService
├── createQuickTask()           // Main entry point
├── createQuickTaskInPersonalInbox()  // Personal inbox creation
├── createQuickTaskInGoogleSheets()   // Google Sheets creation
└── getQuickTaskPreferences()   // Get workspace preferences
```

### Data Flow

1. **Personal Inbox Flow:**
   ```
   User Input → CreateQuickTaskDto → TasksService.createTask() → Database → Response
   ```

2. **Google Sheets Flow:**
   ```
   User Input → CreateQuickTaskDto → GoogleSheetsAdapter.createTask() → 
   Google Sheets API → Database Sync → Response
   ```

### Error Handling

- **Missing Google Sheets Integration**: Returns 400 with clear error message
- **API Failures**: Graceful degradation with retry mechanisms
- **Validation Errors**: Input validation with detailed error responses
- **Network Issues**: Timeout handling and user notification

## Requirements Compliance

### Requirement 7.1: Fast-Entry Form
✅ Implemented with title, description, and due date fields

### Requirement 7.2: Destination Selection
✅ Tasks can be created in personal inbox or Google Sheets based on user preference

### Requirement 7.3: Google Sheets Sync
✅ Tasks are appended to designated sheet within 30 seconds

### Requirement 7.4: Keyboard Shortcuts
✅ Keyboard shortcuts defined and returned via preferences endpoint

### Requirement 7.5: Visual Confirmation
✅ Immediate response with success confirmation and addAnother option

## Testing

### Unit Tests
- `QuickTaskService` functionality
- Error handling scenarios
- Google Sheets integration flow

### API Tests
- Endpoint validation
- Request/response format verification
- Error response handling

### Integration Tests
- End-to-end Google Sheets workflow
- Database consistency checks
- Performance requirements validation

## Usage Examples

### Frontend Integration
```typescript
// Create quick task
const response = await fetch(`/workspaces/${workspaceId}/tasks/quick`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    title: 'Quick task title',
    description: 'Optional description',
    dueDate: '2024-12-31T23:59:59Z',
    destination: 'personal_inbox'
  })
});

// Get preferences for UI configuration
const preferences = await fetch(`/workspaces/${workspaceId}/tasks/quick/preferences`);
```

### Keyboard Shortcuts Implementation
```typescript
// Global keyboard shortcut handler
document.addEventListener('keydown', (e) => {
  if (e.ctrlKey && e.shiftKey && e.key === 'T') {
    e.preventDefault();
    openQuickTaskModal();
  }
});

// Form submission shortcuts
form.addEventListener('keydown', (e) => {
  if (e.ctrlKey && e.key === 'Enter') {
    if (e.shiftKey) {
      submitAndAddAnother();
    } else {
      submitAndClose();
    }
  }
});
```

## Configuration

### Environment Variables
```bash
# Google Sheets default configuration (optional)
GOOGLE_SHEETS_DEFAULT_SPREADSHEET_ID=your-spreadsheet-id
GOOGLE_SHEETS_DEFAULT_SHEET_NAME=Tasks

# Frontend URL for personal inbox links
FRONTEND_URL=http://localhost:3000
```

### Workspace Settings
- Default destination preference
- Google Sheets integration status
- Custom keyboard shortcuts (future enhancement)

## Performance Considerations

- **Database Indexing**: Tasks are indexed by workspace and creation date
- **Priority Calculation**: Automatic priority scoring for new tasks
- **Caching**: Preferences are cached to reduce database queries
- **Batch Operations**: Multiple quick tasks can be created efficiently

## Security

- **Authentication**: All endpoints require valid JWT token
- **Authorization**: Users can only create tasks in their accessible workspaces
- **Input Validation**: All inputs are validated and sanitized
- **Rate Limiting**: API endpoints are rate-limited to prevent abuse

## Future Enhancements

- **Templates**: Pre-defined task templates for common scenarios
- **Bulk Creation**: Create multiple tasks from a single form
- **Smart Suggestions**: AI-powered task completion suggestions
- **Custom Shortcuts**: User-configurable keyboard shortcuts
- **Mobile Support**: Touch-optimized quick task creation