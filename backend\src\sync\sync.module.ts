import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';

import { DatabaseModule } from '../database/database.module';
import { IntegrationsModule } from '../integrations/integrations.module';

// Services
import { SyncEngineService } from './services/sync-engine.service';
import { ConflictResolutionService } from './services/conflict-resolution.service';
import { SyncSchedulerService } from './services/sync-scheduler.service';

// Processors
import { SyncJobProcessor } from './processors/sync-job.processor';

// Constants
import { SYNC_QUEUE } from './constants/sync.constants';

/**
 * Sync module that provides task synchronization functionality
 * including background jobs, conflict resolution, and scheduling
 */
@Module({
  imports: [
    ConfigModule,
    DatabaseModule,
    IntegrationsModule,
    ScheduleModule.forRoot(),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        redis: {
          host: configService.get('REDIS_HOST', 'localhost'),
          port: configService.get('REDIS_PORT', 6379),
          password: configService.get('REDIS_PASSWORD'),
          db: configService.get('REDIS_DB', 0),
        },
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      name: SYNC_QUEUE,
    }),
  ],
  providers: [
    SyncEngineService,
    ConflictResolutionService,
    SyncSchedulerService,
    SyncJobProcessor,
  ],
  exports: [
    SyncEngineService,
    ConflictResolutionService,
    SyncSchedulerService,
  ],
})
export class SyncModule {}