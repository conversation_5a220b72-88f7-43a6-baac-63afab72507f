# TaskUnify Tech Stack & Development Guide

## Tech Stack

### Backend
- **Node.js 18+** with TypeScript
- **NestJS** framework for API development
- **PostgreSQL 14+** for primary database
- **Redis 6+** for caching and job queues
- **Prisma** ORM for database management
- **BullMQ** for background job processing
- **JWT** for authentication
- **OAuth 2.0** for secure integrations

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **TailwindCSS** for styling with custom design system
- **Radix UI** for accessible components
- **React Query (@tanstack/react-query)** for data fetching and caching
- **Zustand** for state management
- **React Router DOM** for routing
- **React Hook Form** with Zod validation

### Infrastructure
- **Docker** for containerization
- **Docker Compose** for local development
- **PostgreSQL** and **Redis** as containerized services

## Common Commands

### Development Setup
```bash
# Install all dependencies
npm run setup

# Start development servers (both frontend and backend)
npm run dev

# Start individual services
npm run dev:backend
npm run dev:frontend

# Using Docker for development
npm run docker:dev
```

### Backend Commands
```bash
cd backend

# Development
npm run dev              # Start with hot reload
npm run start:debug      # Start with debugger

# Database
npm run prisma:generate  # Generate Prisma client
npm run prisma:migrate   # Run migrations
npm run prisma:studio    # Open Prisma Studio
npm run db:reset         # Reset database

# Testing
npm run test            # Run unit tests
npm run test:watch      # Run tests in watch mode
npm run test:cov        # Run with coverage
npm run test:e2e        # Run e2e tests

# Build & Production
npm run build           # Build for production
npm run start:prod      # Start production server
npm run worker          # Start background worker
```

### Frontend Commands
```bash
cd frontend

# Development
npm run dev             # Start dev server (port 3000)
npm run preview         # Preview production build

# Testing
npm run test            # Run Vitest tests
npm run test:ui         # Run tests with UI
npm run test:coverage   # Run with coverage

# Build
npm run build           # Build for production
```

### Docker Commands
```bash
# Development environment
npm run docker:dev      # Start all services in dev mode

# Production environment
npm run docker:prod     # Start production containers

# Individual services
docker-compose up postgres redis  # Start only database services
```

## Build System

- **Backend**: NestJS CLI with Webpack bundling
- **Frontend**: Vite with TypeScript and React plugins
- **Database**: Prisma for schema management and migrations
- **Containerization**: Multi-stage Docker builds for production optimization

## Development Ports

- **Frontend**: 3000 (Vite dev server)
- **Backend**: 3001 (NestJS API)
- **PostgreSQL**: 5432
- **Redis**: 6379
- **Prisma Studio**: 5555