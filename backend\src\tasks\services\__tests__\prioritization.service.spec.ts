import { Test, TestingModule } from '@nestjs/testing';
import { PrioritizationService } from '../prioritization.service';
import {
  PriorityWeights,
  TaskPriorityData,
  PrioritizationSettings,
  DEFAULT_PRIORITIZATION_SETTINGS,
} from '../../interfaces/prioritization.interface';

describe('PrioritizationService', () => {
  let service: PrioritizationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PrioritizationService],
    }).compile();

    service = module.get<PrioritizationService>(PrioritizationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('calculatePriorityScore', () => {
    const mockTask: TaskPriorityData = {
      id: 'task-1',
      dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      estimatedMinutes: 60,
      priority: 'high',
      tags: ['urgent'],
      projectName: 'Project A',
      assigneeId: 'user-1',
      status: 'todo',
    };

    it('should calculate priority score with default settings', () => {
      const score = service.calculatePriorityScore(mockTask);
      
      expect(score).toBeGreaterThan(0);
      expect(score).toBeLessThanOrEqual(100);
    });

    it('should return higher score for tasks with closer due dates', () => {
      const taskTomorrow = { ...mockTask, dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000) };
      const taskNextWeek = { ...mockTask, dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) };
      
      const scoreTomorrow = service.calculatePriorityScore(taskTomorrow);
      const scoreNextWeek = service.calculatePriorityScore(taskNextWeek);
      
      expect(scoreTomorrow).toBeGreaterThan(scoreNextWeek);
    });

    it('should return maximum score for overdue tasks', () => {
      const overdueTask = { ...mockTask, dueDate: new Date(Date.now() - 24 * 60 * 60 * 1000) };
      const futureTask = { ...mockTask, dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) };
      
      const overdueScore = service.calculatePriorityScore(overdueTask);
      const futureScore = service.calculatePriorityScore(futureTask);
      
      expect(overdueScore).toBeGreaterThan(futureScore);
    });

    it('should return higher score for smaller effort estimates', () => {
      const quickTask = { ...mockTask, estimatedMinutes: 15 };
      const longTask = { ...mockTask, estimatedMinutes: 300 };
      
      const quickScore = service.calculatePriorityScore(quickTask);
      const longScore = service.calculatePriorityScore(longTask);
      
      expect(quickScore).toBeGreaterThan(longScore);
    });

    it('should return higher score for high priority tasks', () => {
      const highPriorityTask = { ...mockTask, priority: 'critical' };
      const lowPriorityTask = { ...mockTask, priority: 'low' };
      
      const highScore = service.calculatePriorityScore(highPriorityTask);
      const lowScore = service.calculatePriorityScore(lowPriorityTask);
      
      expect(highScore).toBeGreaterThan(lowScore);
    });

    it('should boost score for tasks with business impact tags', () => {
      const urgentTask = { ...mockTask, tags: ['urgent', 'client-facing'] };
      const normalTask = { ...mockTask, tags: ['feature'] };
      
      const urgentScore = service.calculatePriorityScore(urgentTask);
      const normalScore = service.calculatePriorityScore(normalTask);
      
      expect(urgentScore).toBeGreaterThan(normalScore);
    });

    it('should handle tasks with missing data gracefully', () => {
      const incompleteTask: TaskPriorityData = {
        id: 'task-2',
        tags: [],
        status: 'todo',
      };
      
      const score = service.calculatePriorityScore(incompleteTask);
      
      expect(score).toBeGreaterThanOrEqual(0);
      expect(score).toBeLessThanOrEqual(100);
    });

    it('should respect custom weights', () => {
      const customSettings: PrioritizationSettings = {
        ...DEFAULT_PRIORITIZATION_SETTINGS,
        weights: {
          dueDateProximity: 1.0,
          effortEstimate: 0.0,
          businessImpact: 0.0,
          contextSwitching: 0.0,
        },
      };

      const taskTomorrow = { ...mockTask, dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000) };
      const taskNextMonth = { ...mockTask, dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) };
      
      const scoreTomorrow = service.calculatePriorityScore(taskTomorrow, customSettings);
      const scoreNextMonth = service.calculatePriorityScore(taskNextMonth, customSettings);
      
      // With 100% weight on due date, the difference should be significant
      expect(scoreTomorrow).toBeGreaterThan(scoreNextMonth);
    });

    it('should ensure score is within bounds', () => {
      const extremeTask: TaskPriorityData = {
        id: 'task-extreme',
        dueDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days overdue
        estimatedMinutes: 5, // Very quick
        priority: 'critical',
        tags: ['urgent', 'critical', 'high-priority', 'client-facing'],
        projectName: 'Critical Project',
        assigneeId: 'user-1',
        status: 'todo',
      };
      
      const score = service.calculatePriorityScore(extremeTask);
      
      expect(score).toBeGreaterThanOrEqual(0);
      expect(score).toBeLessThanOrEqual(100);
    });
  });

  describe('validateWeights', () => {
    it('should return true for valid weights that sum to 1.0', () => {
      const validWeights: PriorityWeights = {
        dueDateProximity: 0.4,
        effortEstimate: 0.2,
        businessImpact: 0.3,
        contextSwitching: 0.1,
      };
      
      expect(service.validateWeights(validWeights)).toBe(true);
    });

    it('should return false for weights that do not sum to 1.0', () => {
      const invalidWeights: PriorityWeights = {
        dueDateProximity: 0.5,
        effortEstimate: 0.5,
        businessImpact: 0.5,
        contextSwitching: 0.5,
      };
      
      expect(service.validateWeights(invalidWeights)).toBe(false);
    });

    it('should handle floating point precision errors', () => {
      const almostValidWeights: PriorityWeights = {
        dueDateProximity: 0.33333333,
        effortEstimate: 0.33333333,
        businessImpact: 0.33333333,
        contextSwitching: 0.00000001,
      };
      
      expect(service.validateWeights(almostValidWeights)).toBe(true);
    });
  });

  describe('normalizeWeights', () => {
    it('should normalize weights to sum to 1.0', () => {
      const unnormalizedWeights: PriorityWeights = {
        dueDateProximity: 2.0,
        effortEstimate: 1.0,
        businessImpact: 1.5,
        contextSwitching: 0.5,
      };
      
      const normalized = service.normalizeWeights(unnormalizedWeights);
      const sum = normalized.dueDateProximity + normalized.effortEstimate + 
                  normalized.businessImpact + normalized.contextSwitching;
      
      expect(Math.abs(sum - 1.0)).toBeLessThan(0.001);
    });

    it('should return equal weights when all input weights are zero', () => {
      const zeroWeights: PriorityWeights = {
        dueDateProximity: 0,
        effortEstimate: 0,
        businessImpact: 0,
        contextSwitching: 0,
      };
      
      const normalized = service.normalizeWeights(zeroWeights);
      
      expect(normalized.dueDateProximity).toBe(0.25);
      expect(normalized.effortEstimate).toBe(0.25);
      expect(normalized.businessImpact).toBe(0.25);
      expect(normalized.contextSwitching).toBe(0.25);
    });

    it('should preserve relative proportions', () => {
      const weights: PriorityWeights = {
        dueDateProximity: 4.0,
        effortEstimate: 2.0,
        businessImpact: 3.0,
        contextSwitching: 1.0,
      };
      
      const normalized = service.normalizeWeights(weights);
      
      // Due date should be twice as important as effort
      expect(normalized.dueDateProximity / normalized.effortEstimate).toBeCloseTo(2.0);
      // Business impact should be 1.5 times as important as effort
      expect(normalized.businessImpact / normalized.effortEstimate).toBeCloseTo(1.5);
    });
  });

  describe('getDefaultSettings', () => {
    it('should return default prioritization settings', () => {
      const settings = service.getDefaultSettings();
      
      expect(settings).toEqual(DEFAULT_PRIORITIZATION_SETTINGS);
      expect(settings.weights.dueDateProximity).toBe(0.4);
      expect(settings.weights.effortEstimate).toBe(0.2);
      expect(settings.weights.businessImpact).toBe(0.3);
      expect(settings.weights.contextSwitching).toBe(0.1);
      expect(settings.maxScore).toBe(100);
    });

    it('should return a copy of default settings', () => {
      const settings1 = service.getDefaultSettings();
      const settings2 = service.getDefaultSettings();
      
      expect(settings1).not.toBe(settings2); // Different objects
      expect(settings1).toEqual(settings2); // Same content
    });
  });

  describe('edge cases', () => {
    it('should handle tasks with very far future due dates', () => {
      const farFutureTask: TaskPriorityData = {
        id: 'task-future',
        dueDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        estimatedMinutes: 60,
        priority: 'medium',
        tags: [],
        projectName: 'Future Project',
        assigneeId: 'user-1',
        status: 'todo',
      };
      
      const score = service.calculatePriorityScore(farFutureTask);
      
      expect(score).toBeGreaterThan(0);
      expect(score).toBeLessThan(50); // Should be relatively low priority
    });

    it('should handle tasks with very large effort estimates', () => {
      const massiveTask: TaskPriorityData = {
        id: 'task-massive',
        dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        estimatedMinutes: 10000, // Very large task
        priority: 'high',
        tags: ['urgent'],
        projectName: 'Big Project',
        assigneeId: 'user-1',
        status: 'todo',
      };
      
      const score = service.calculatePriorityScore(massiveTask);
      
      expect(score).toBeGreaterThan(0);
      expect(score).toBeLessThanOrEqual(100);
    });

    it('should handle empty and null values consistently', () => {
      const emptyTask: TaskPriorityData = {
        id: 'task-empty',
        tags: [],
        status: 'todo',
      };
      
      const score = service.calculatePriorityScore(emptyTask);
      
      expect(score).toBeGreaterThan(0);
      expect(score).toBeLessThanOrEqual(100);
    });
  });
});