import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useAuthStore } from '../auth'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

describe('Auth Store', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset store state
    useAuthStore.setState({
      user: null,
      token: null,
      isLoading: true,
      isAuthenticated: false,
    })
  })

  it('initializes with default state', () => {
    const state = useAuthStore.getState()
    
    expect(state.user).toBeNull()
    expect(state.token).toBeNull()
    expect(state.isLoading).toBe(true)
    expect(state.isAuthenticated).toBe(false)
  })

  it('sets user correctly', () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z',
    }

    useAuthStore.getState().setUser(mockUser)
    const state = useAuthStore.getState()

    expect(state.user).toEqual(mockUser)
    expect(state.isAuthenticated).toBe(true)
  })

  it('sets token correctly', () => {
    const mockToken = 'test-token'

    useAuthStore.getState().setToken(mockToken)
    const state = useAuthStore.getState()

    expect(state.token).toBe(mockToken)
  })

  it('logs in user correctly', () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z',
    }
    const mockToken = 'test-token'

    useAuthStore.getState().login(mockUser, mockToken)
    const state = useAuthStore.getState()

    expect(state.user).toEqual(mockUser)
    expect(state.token).toBe(mockToken)
    expect(state.isAuthenticated).toBe(true)
    expect(state.isLoading).toBe(false)
  })

  it('logs out user correctly', () => {
    // First login
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z',
    }
    useAuthStore.getState().login(mockUser, 'test-token')

    // Then logout
    useAuthStore.getState().logout()
    const state = useAuthStore.getState()

    expect(state.user).toBeNull()
    expect(state.token).toBeNull()
    expect(state.isAuthenticated).toBe(false)
    expect(state.isLoading).toBe(false)
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth-storage')
  })

  it('sets loading state correctly', () => {
    useAuthStore.getState().setLoading(true)
    expect(useAuthStore.getState().isLoading).toBe(true)

    useAuthStore.getState().setLoading(false)
    expect(useAuthStore.getState().isLoading).toBe(false)
  })

  it('initializes correctly when no token exists', async () => {
    localStorageMock.getItem.mockReturnValue(null)

    await useAuthStore.getState().initialize()
    const state = useAuthStore.getState()

    expect(state.isLoading).toBe(false)
    expect(state.user).toBeNull()
    expect(state.token).toBeNull()
    expect(state.isAuthenticated).toBe(false)
  })
})