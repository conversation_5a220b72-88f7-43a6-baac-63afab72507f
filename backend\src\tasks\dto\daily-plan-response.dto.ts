import { ApiProperty } from '@nestjs/swagger';

export class DailyPlanTaskResponseDto {
  @ApiProperty({ description: 'Daily plan task ID' })
  id: string;

  @ApiProperty({ description: 'Task ID' })
  taskId: string;

  @ApiProperty({ description: 'Estimated duration in minutes' })
  estimatedMinutes: number;

  @ApiProperty({ description: 'Actual time spent in minutes', nullable: true })
  actualMinutes: number | null;

  @ApiProperty({ description: 'Order index for drag-and-drop ordering' })
  orderIndex: number;

  @ApiProperty({ description: 'Completion timestamp', nullable: true })
  completedAt: Date | null;

  @ApiProperty({ description: 'Task details' })
  task: {
    id: string;
    title: string;
    description: string | null;
    status: string;
    priority: string | null;
    priorityScore: number;
    dueDate: Date | null;
    tags: string[];
    projectName: string | null;
    sourceUrl: string;
  };
}

export class DailyPlanResponseDto {
  @ApiProperty({ description: 'Daily plan ID' })
  id: string;

  @ApiProperty({ description: 'Workspace ID' })
  workspaceId: string;

  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({ description: 'Plan date' })
  planDate: Date;

  @ApiProperty({ description: 'Total estimated time in minutes' })
  totalEstimatedMinutes: number;

  @ApiProperty({ description: 'Total completed time in minutes' })
  totalCompletedMinutes: number;

  @ApiProperty({ description: 'Completion progress as percentage (0-100)' })
  completionProgress: number;

  @ApiProperty({ description: 'Whether the plan exceeds 8 hours (480 minutes)' })
  exceedsRecommendedTime: boolean;

  @ApiProperty({ 
    description: 'Tasks in the daily plan',
    type: [DailyPlanTaskResponseDto]
  })
  tasks: DailyPlanTaskResponseDto[];

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}