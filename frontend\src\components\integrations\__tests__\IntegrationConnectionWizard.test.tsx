import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { IntegrationConnectionWizard } from '../IntegrationConnectionWizard'
import { IntegrationService } from '@/services/integration'
import { useWorkspaceStore } from '@/store/workspace'
import { IntegrationProvider } from '@/types/api'

// Mock dependencies
vi.mock('@/services/integration')
vi.mock('@/store/workspace')
vi.mock('react-hot-toast', () => ({
  default: {
    error: vi.fn(),
    success: vi.fn()
  }
}))

const mockIntegrationService = IntegrationService as any
const mockUseWorkspaceStore = useWorkspaceStore as any

const mockProviders = [
  {
    provider: IntegrationProvider.ASANA,
    name: 'Asana',
    description: 'Connect your Asana projects and tasks',
    logoUrl: '/logos/asana.png',
    capabilities: {
      supportsTwoWaySync: true,
      supportsWebhooks: true,
      supportsFieldMapping: true
    }
  },
  {
    provider: IntegrationProvider.TRELLO,
    name: '<PERSON>rell<PERSON>',
    description: 'Connect your Trello boards and cards',
    logoUrl: '/logos/trello.png',
    capabilities: {
      supportsTwoWaySync: true,
      supportsWebhooks: false,
      supportsFieldMapping: false
    }
  }
]

describe('IntegrationConnectionWizard', () => {
  const mockOnClose = vi.fn()
  const mockOnSuccess = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    
    mockUseWorkspaceStore.mockReturnValue({
      currentWorkspace: {
        id: 'workspace-1',
        name: 'Test Workspace'
      }
    })

    mockIntegrationService.getAvailableProviders.mockResolvedValue(mockProviders)
    
    // Mock sessionStorage
    Object.defineProperty(window, 'sessionStorage', {
      value: {
        setItem: vi.fn(),
        getItem: vi.fn(),
        removeItem: vi.fn()
      },
      writable: true
    })

    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        origin: 'http://localhost:3000',
        href: 'http://localhost:3000'
      },
      writable: true
    })
  })

  it('should render provider selection step', async () => {
    render(
      <IntegrationConnectionWizard
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('Choose an Integration')).toBeInTheDocument()
    })

    expect(screen.getByText('Asana')).toBeInTheDocument()
    expect(screen.getByText('Trello')).toBeInTheDocument()
    expect(screen.getByText('Connect your Asana projects and tasks')).toBeInTheDocument()
  })

  it('should show loading state while fetching providers', () => {
    mockIntegrationService.getAvailableProviders.mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 1000))
    )

    render(
      <IntegrationConnectionWizard
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    )

    expect(screen.getByRole('status')).toBeInTheDocument() // Loading spinner
  })

  it('should proceed to OAuth step when provider is selected', async () => {
    render(
      <IntegrationConnectionWizard
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('Asana')).toBeInTheDocument()
    })

    // Click on Asana provider
    fireEvent.click(screen.getByText('Asana'))

    await waitFor(() => {
      expect(screen.getByText('Connect to Asana')).toBeInTheDocument()
    })

    expect(screen.getByLabelText('Integration Name')).toBeInTheDocument()
    expect(screen.getByLabelText('Sync Interval (minutes)')).toBeInTheDocument()
  })

  it('should start with OAuth step when provider is pre-selected', async () => {
    render(
      <IntegrationConnectionWizard
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        provider={IntegrationProvider.ASANA}
      />
    )

    await waitFor(() => {
      expect(screen.getByRole('heading', { name: /Connect to Asana/i })).toBeInTheDocument()
    })

    expect(screen.queryByText('Choose an Integration')).not.toBeInTheDocument()
  })

  it('should allow configuring integration settings', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationConnectionWizard
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        provider={IntegrationProvider.ASANA}
      />
    )

    await waitFor(() => {
      expect(screen.getByRole('heading', { name: /Connect to Asana/i })).toBeInTheDocument()
    })

    // Update integration name
    const nameInput = screen.getByLabelText('Integration Name')
    await user.clear(nameInput)
    await user.type(nameInput, 'My Custom Asana')

    expect(nameInput).toHaveValue('My Custom Asana')

    // Change sync interval
    const syncIntervalSelect = screen.getByLabelText('Sync Interval (minutes)')
    await user.click(syncIntervalSelect)
    await user.click(screen.getByText('1 hour'))

    // Toggle two-way sync (should be enabled by default for Asana)
    const twoWaySyncSwitch = screen.getByLabelText('Enable two-way sync')
    expect(twoWaySyncSwitch).toBeChecked()
    await user.click(twoWaySyncSwitch)
    expect(twoWaySyncSwitch).not.toBeChecked()

    // Toggle webhooks (should be available for Asana)
    const webhookSwitch = screen.getByLabelText('Enable real-time updates (webhooks)')
    await user.click(webhookSwitch)
    expect(webhookSwitch).toBeChecked()
  })

  it('should initiate OAuth flow when connect button is clicked', async () => {
    const user = userEvent.setup()
    
    mockIntegrationService.getOAuthUrl.mockResolvedValue({
      authUrl: 'https://app.asana.com/oauth/authorize?client_id=123',
      state: 'random-state'
    })

    render(
      <IntegrationConnectionWizard
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        provider={IntegrationProvider.ASANA}
      />
    )

    await waitFor(() => {
      expect(screen.getByRole('heading', { name: /Connect to Asana/i })).toBeInTheDocument()
    })

    const connectButton = screen.getByRole('button', { name: /Connect to Asana/i })
    await user.click(connectButton)

    await waitFor(() => {
      expect(mockIntegrationService.getOAuthUrl).toHaveBeenCalledWith(
        'workspace-1',
        IntegrationProvider.ASANA,
        'http://localhost:3000/integrations/oauth/callback'
      )
    })

    expect(window.sessionStorage.setItem).toHaveBeenCalledWith('oauth_state', 'random-state')
    expect(window.sessionStorage.setItem).toHaveBeenCalledWith('oauth_provider', IntegrationProvider.ASANA)
  })

  it('should disable connect button when integration name is empty', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationConnectionWizard
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        provider={IntegrationProvider.ASANA}
      />
    )

    await waitFor(() => {
      expect(screen.getByRole('heading', { name: /Connect to Asana/i })).toBeInTheDocument()
    })

    // Clear the integration name
    const nameInput = screen.getByLabelText('Integration Name')
    await user.clear(nameInput)

    const connectButton = screen.getByRole('button', { name: /Connect to Asana/i })
    expect(connectButton).toBeDisabled()
  })

  it('should show appropriate capabilities for each provider', async () => {
    render(
      <IntegrationConnectionWizard
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('Asana')).toBeInTheDocument()
    })

    // Asana should show all capabilities
    const asanaCard = screen.getByText('Asana').closest('.cursor-pointer')
    expect(asanaCard).toHaveTextContent('Two-way sync')
    expect(asanaCard).toHaveTextContent('Real-time')
    expect(asanaCard).toHaveTextContent('Field mapping')

    // Trello should show limited capabilities
    const trelloCard = screen.getByText('Trello').closest('.cursor-pointer')
    expect(trelloCard).toHaveTextContent('Two-way sync')
    expect(trelloCard).not.toHaveTextContent('Real-time')
    expect(trelloCard).not.toHaveTextContent('Field mapping')
  })

  it('should allow going back from OAuth step to provider selection', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationConnectionWizard
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('Asana')).toBeInTheDocument()
    })

    // Select Asana
    fireEvent.click(screen.getByText('Asana'))

    await waitFor(() => {
      expect(screen.getByRole('heading', { name: /Connect to Asana/i })).toBeInTheDocument()
    })

    // Click back button
    const backButton = screen.getByRole('button', { name: 'Back' })
    await user.click(backButton)

    await waitFor(() => {
      expect(screen.getByText('Choose an Integration')).toBeInTheDocument()
    })
  })

  it('should handle OAuth URL generation error', async () => {
    const user = userEvent.setup()
    
    mockIntegrationService.getOAuthUrl.mockRejectedValue(new Error('OAuth error'))

    render(
      <IntegrationConnectionWizard
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        provider={IntegrationProvider.ASANA}
      />
    )

    await waitFor(() => {
      expect(screen.getByRole('heading', { name: /Connect to Asana/i })).toBeInTheDocument()
    })

    const connectButton = screen.getByRole('button', { name: /Connect to Asana/i })
    await user.click(connectButton)

    await waitFor(() => {
      expect(mockIntegrationService.getOAuthUrl).toHaveBeenCalled()
    })

    // Button should not be in loading state after error
    expect(connectButton).not.toHaveTextContent('Connecting...')
  })
})