import { IsString, IsOptional, IsDateString, IsEnum, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum QuickTaskDestination {
  PERSONAL_INBOX = 'personal_inbox',
  GOOGLE_SHEETS = 'google_sheets',
}

export class CreateQuickTaskDto {
  @ApiProperty({ 
    description: 'Task title', 
    example: 'Review quarterly reports'
  })
  @IsString()
  title: string;

  @ApiProperty({ 
    description: 'Task description', 
    example: 'Review Q4 financial reports and prepare summary',
    required: false 
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    description: 'Due date (ISO string)', 
    example: '2024-12-31T23:59:59Z',
    required: false 
  })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiProperty({ 
    description: 'Destination for the quick task',
    enum: QuickTaskDestination,
    example: QuickTaskDestination.PERSONAL_INBOX,
    default: QuickTaskDestination.PERSONAL_INBOX
  })
  @IsOptional()
  @IsEnum(QuickTaskDestination)
  destination?: QuickTaskDestination = QuickTaskDestination.PERSONAL_INBOX;

  @ApiProperty({ 
    description: 'Whether to show option to add another task after creation',
    example: true,
    default: false,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  addAnother?: boolean = false;
}