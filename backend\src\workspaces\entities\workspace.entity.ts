export interface Workspace {
  id: string;
  name: string;
  slug: string;
  ownerId: string;
  settings: any; // Prisma JsonValue
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkspaceMember {
  id: string;
  userId: string;
  workspaceId: string;
  role: string; // Prisma stores as string
  permissions: string[];
  joinedAt: Date;
}

export enum WorkspaceRole {
  OWNER = 'OWNER',
  ADMIN = 'ADMIN',
  MEMBER = 'MEMBER',
}

export interface WorkspaceWithMembers extends Workspace {
  members: WorkspaceMember[];
  memberCount: number;
}

export interface WorkspaceMemberWithUser extends WorkspaceMember {
  user: {
    id: string;
    name: string;
    email: string;
    avatarUrl?: string;
  };
}