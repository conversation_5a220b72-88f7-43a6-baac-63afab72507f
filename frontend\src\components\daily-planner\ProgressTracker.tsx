import React from 'react'
import { Clock, CheckCircle2, Alert<PERSON>riangle, Target } from 'lucide-react'

import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'

interface ProgressTrackerProps {
  totalEstimated: number
  totalCompleted: number
  completionProgress: number
  exceedsRecommended: boolean
}

export const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  totalEstimated,
  totalCompleted,
  completionProgress,
  exceedsRecommended
}) => {
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  const getProgressColor = () => {
    if (completionProgress >= 80) return 'bg-green-500'
    if (completionProgress >= 50) return 'bg-blue-500'
    if (completionProgress >= 25) return 'bg-yellow-500'
    return 'bg-gray-300'
  }

  const getTimeStatus = () => {
    if (exceedsRecommended) {
      return {
        icon: AlertTriangle,
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        message: 'Exceeds recommended 8h'
      }
    }
    
    if (totalEstimated >= 360) { // 6+ hours
      return {
        icon: Target,
        color: 'text-amber-600',
        bgColor: 'bg-amber-50',
        message: 'Full day planned'
      }
    }
    
    return {
      icon: Clock,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      message: 'Good workload'
    }
  }

  const timeStatus = getTimeStatus()
  const StatusIcon = timeStatus.icon

  return (
    <Card>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Total Time */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-600">Total Planned</span>
            </div>
            <div className="text-2xl font-bold">{formatDuration(totalEstimated)}</div>
            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${timeStatus.bgColor}`}>
              <StatusIcon className={`h-3 w-3 ${timeStatus.color}`} />
              <span className={timeStatus.color}>{timeStatus.message}</span>
            </div>
          </div>

          {/* Completed Time */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium text-gray-600">Completed</span>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {formatDuration(totalCompleted)}
            </div>
            <div className="text-xs text-gray-500">
              {totalEstimated > 0 ? Math.round(completionProgress) : 0}% of plan
            </div>
          </div>

          {/* Progress Bar */}
          <div className="md:col-span-2 space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600">Daily Progress</span>
              <Badge variant="outline" className="text-xs">
                {Math.round(completionProgress)}%
              </Badge>
            </div>
            
            <Progress 
              value={completionProgress} 
              className="h-3"
            />
            
            <div className="flex justify-between text-xs text-gray-500">
              <span>0%</span>
              <span>50%</span>
              <span>100%</span>
            </div>
          </div>
        </div>

        {/* Additional Metrics */}
        {totalEstimated > 0 && (
          <div className="mt-6 pt-4 border-t">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold">
                  {Math.round((totalEstimated - totalCompleted) / 60 * 10) / 10}h
                </div>
                <div className="text-xs text-gray-500">Remaining</div>
              </div>
              
              <div>
                <div className="text-lg font-semibold">
                  {totalCompleted > 0 ? Math.round(totalCompleted / totalEstimated * 100) : 0}%
                </div>
                <div className="text-xs text-gray-500">Time Efficiency</div>
              </div>
              
              <div>
                <div className="text-lg font-semibold">
                  {Math.round(totalEstimated / 60 * 10) / 10}h
                </div>
                <div className="text-xs text-gray-500">Planned Hours</div>
              </div>
              
              <div>
                <div className="text-lg font-semibold">
                  {exceedsRecommended ? '⚠️' : '✅'}
                </div>
                <div className="text-xs text-gray-500">Workload Status</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}