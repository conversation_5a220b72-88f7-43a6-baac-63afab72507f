import { Test, TestingModule } from '@nestjs/testing';
import { getQueueToken } from '@nestjs/bull';
import { SyncEngineService } from '../sync-engine.service';
import { PrismaService } from '../../../database/prisma.service';
import { IntegrationsService } from '../../../integrations/integrations.service';
import { SYNC_QUEUE, SyncJobType, SyncJobPriority, ConflictResolutionStrategy } from '../../constants/sync.constants';
import { SyncConflict, SyncError, IntegrationStatus } from '../../../integrations/types';

describe('SyncEngineService', () => {
  let service: SyncEngineService;
  let mockQueue: any;
  let mockPrisma: any;
  let mockIntegrationsService: any;

  const mockIntegrationId = 'integration-123';
  const mockWorkspaceId = 'workspace-123';

  beforeEach(async () => {
    mockQueue = {
      add: jest.fn(),
      getJob: jest.fn(),
      getJobs: jest.fn(),
      clean: jest.fn(),
    };

    mockPrisma = {
      integration: {
        findUnique: jest.fn(),
        update: jest.fn(),
      },
      syncLog: {
        findMany: jest.fn(),
        deleteMany: jest.fn(),
      },
    };

    mockIntegrationsService = {
      getAdapter: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SyncEngineService,
        {
          provide: getQueueToken(SYNC_QUEUE),
          useValue: mockQueue,
        },
        {
          provide: PrismaService,
          useValue: mockPrisma,
        },
        {
          provide: IntegrationsService,
          useValue: mockIntegrationsService,
        },
      ],
    }).compile();

    service = module.get<SyncEngineService>(SyncEngineService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('scheduleFullSync', () => {
    it('should schedule a full sync job with default options', async () => {
      const mockJobId = 'job-123';
      mockQueue.add.mockResolvedValue({ id: mockJobId });

      const result = await service.scheduleFullSync(mockIntegrationId, mockWorkspaceId);

      expect(mockQueue.add).toHaveBeenCalledWith(
        SyncJobType.FULL_SYNC,
        {
          integrationId: mockIntegrationId,
          workspaceId: mockWorkspaceId,
          jobType: SyncJobType.FULL_SYNC,
          priority: SyncJobPriority.NORMAL,
          forceSync: false,
          syncAllTasks: false,
        },
        {
          priority: SyncJobPriority.NORMAL,
          delay: 0,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        }
      );

      expect(result).toBe(mockJobId);
    });

    it('should schedule a full sync job with custom options', async () => {
      const mockJobId = 'job-456';
      mockQueue.add.mockResolvedValue({ id: mockJobId });

      const options = {
        priority: SyncJobPriority.HIGH,
        forceSync: true,
        syncAllTasks: true,
        delay: 5000,
      };

      const result = await service.scheduleFullSync(mockIntegrationId, mockWorkspaceId, options);

      expect(mockQueue.add).toHaveBeenCalledWith(
        SyncJobType.FULL_SYNC,
        {
          integrationId: mockIntegrationId,
          workspaceId: mockWorkspaceId,
          jobType: SyncJobType.FULL_SYNC,
          priority: SyncJobPriority.HIGH,
          forceSync: true,
          syncAllTasks: true,
        },
        {
          priority: SyncJobPriority.HIGH,
          delay: 5000,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        }
      );

      expect(result).toBe(mockJobId);
    });
  });

  describe('scheduleIncrementalSync', () => {
    it('should schedule an incremental sync job', async () => {
      const mockJobId = 'job-789';
      const lastSyncAt = new Date('2023-01-01');
      mockQueue.add.mockResolvedValue({ id: mockJobId });

      const result = await service.scheduleIncrementalSync(mockIntegrationId, mockWorkspaceId, {
        lastSyncAt,
      });

      expect(mockQueue.add).toHaveBeenCalledWith(
        SyncJobType.INCREMENTAL_SYNC,
        {
          integrationId: mockIntegrationId,
          workspaceId: mockWorkspaceId,
          jobType: SyncJobType.INCREMENTAL_SYNC,
          priority: SyncJobPriority.NORMAL,
          lastSyncAt,
        },
        {
          priority: SyncJobPriority.NORMAL,
          delay: 0,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        }
      );

      expect(result).toBe(mockJobId);
    });
  });

  describe('scheduleWebhookSync', () => {
    it('should schedule a webhook sync job', async () => {
      const mockJobId = 'job-webhook';
      const webhookPayload = { taskId: 'task-123', action: 'updated' };
      const eventType = 'task.updated';
      const externalTaskId = 'external-task-123';

      mockQueue.add.mockResolvedValue({ id: mockJobId });

      const result = await service.scheduleWebhookSync(
        mockIntegrationId,
        mockWorkspaceId,
        webhookPayload,
        eventType,
        externalTaskId
      );

      expect(mockQueue.add).toHaveBeenCalledWith(
        SyncJobType.WEBHOOK_SYNC,
        {
          integrationId: mockIntegrationId,
          workspaceId: mockWorkspaceId,
          jobType: SyncJobType.WEBHOOK_SYNC,
          priority: SyncJobPriority.HIGH,
          webhookPayload,
          eventType,
          externalTaskId,
        },
        {
          priority: SyncJobPriority.HIGH,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        }
      );

      expect(result).toBe(mockJobId);
    });
  });

  describe('scheduleConflictResolution', () => {
    it('should schedule a conflict resolution job', async () => {
      const mockJobId = 'job-conflict';
      const conflicts: SyncConflict[] = [
        {
          taskId: 'task-123',
          field: 'title',
          localValue: 'Local Title',
          remoteValue: 'Remote Title',
          lastSyncAt: new Date('2023-01-01'),
          conflictedAt: new Date('2023-01-02'),
        },
      ];
      const strategy = ConflictResolutionStrategy.LOCAL_WINS;
      const resolvedBy = 'user-123';

      mockQueue.add.mockResolvedValue({ id: mockJobId });

      const result = await service.scheduleConflictResolution(
        mockIntegrationId,
        mockWorkspaceId,
        conflicts,
        strategy,
        resolvedBy
      );

      expect(mockQueue.add).toHaveBeenCalledWith(
        SyncJobType.CONFLICT_RESOLUTION,
        {
          integrationId: mockIntegrationId,
          workspaceId: mockWorkspaceId,
          jobType: SyncJobType.CONFLICT_RESOLUTION,
          priority: SyncJobPriority.HIGH,
          conflicts,
          strategy,
          resolvedBy,
        },
        {
          priority: SyncJobPriority.HIGH,
          attempts: 2,
          backoff: {
            type: 'fixed',
            delay: 5000,
          },
        }
      );

      expect(result).toBe(mockJobId);
    });
  });

  describe('retryFailedJob', () => {
    it('should schedule a retry job', async () => {
      const mockJobId = 'job-retry';
      const originalJobId = 'original-job-123';
      const originalError: SyncError = {
        type: 'NETWORK_ERROR',
        message: 'Connection failed',
        retryable: true,
        timestamp: new Date(),
      };

      mockQueue.add.mockResolvedValue({ id: mockJobId });

      const result = await service.retryFailedJob(
        originalJobId,
        mockIntegrationId,
        mockWorkspaceId,
        originalError
      );

      expect(mockQueue.add).toHaveBeenCalledWith(
        SyncJobType.RETRY_FAILED,
        {
          integrationId: mockIntegrationId,
          workspaceId: mockWorkspaceId,
          jobType: SyncJobType.RETRY_FAILED,
          priority: SyncJobPriority.NORMAL,
          originalJobId,
          originalError,
          maxRetries: 3,
        },
        {
          priority: SyncJobPriority.NORMAL,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
        }
      );

      expect(result).toBe(mockJobId);
    });
  });

  describe('getJobStatus', () => {
    it('should return job status when job exists', async () => {
      const jobId = 'job-123';
      const mockJob = {
        id: jobId,
        data: {
          integrationId: mockIntegrationId,
          jobType: SyncJobType.FULL_SYNC,
        },
        timestamp: Date.now(),
        processedOn: Date.now() + 1000,
        finishedOn: Date.now() + 2000,
        progress: jest.fn().mockReturnValue(75),
        getState: jest.fn().mockResolvedValue('completed'),
        returnvalue: { success: true },
      };

      mockQueue.getJob.mockResolvedValue(mockJob);

      const result = await service.getJobStatus(jobId);

      expect(result).toEqual({
        id: jobId,
        integrationId: mockIntegrationId,
        jobType: SyncJobType.FULL_SYNC,
        status: 'completed',
        progress: 75,
        createdAt: new Date(mockJob.timestamp),
        processedAt: new Date(mockJob.processedOn),
        completedAt: new Date(mockJob.finishedOn),
        failedAt: undefined,
        error: undefined,
        result: { success: true },
      });
    });

    it('should return null when job does not exist', async () => {
      mockQueue.getJob.mockResolvedValue(null);

      const result = await service.getJobStatus('non-existent-job');

      expect(result).toBeNull();
    });
  });

  describe('pauseIntegrationSync', () => {
    it('should pause sync for integration', async () => {
      const mockJobs = [
        {
          id: 'job-1',
          data: { integrationId: mockIntegrationId },
          getState: jest.fn().mockResolvedValue('waiting'),
        },
        {
          id: 'job-2',
          data: { integrationId: mockIntegrationId },
          getState: jest.fn().mockResolvedValue('delayed'),
        },
      ];

      mockQueue.getJobs.mockResolvedValue(mockJobs);
      mockQueue.getJob.mockImplementation((id) => 
        mockJobs.find(job => job.id === id)
      );

      // Mock job removal
      mockJobs.forEach(job => {
        job.remove = jest.fn().mockResolvedValue(undefined);
      });

      await service.pauseIntegrationSync(mockIntegrationId);

      expect(mockPrisma.integration.update).toHaveBeenCalledWith({
        where: { id: mockIntegrationId },
        data: { status: IntegrationStatus.DISABLED },
      });

      mockJobs.forEach(job => {
        expect(job.remove).toHaveBeenCalled();
      });
    });
  });

  describe('resumeIntegrationSync', () => {
    it('should resume sync for integration', async () => {
      const mockIntegration = {
        id: mockIntegrationId,
        workspaceId: mockWorkspaceId,
        lastSyncAt: new Date('2023-01-01'),
      };

      mockPrisma.integration.findUnique.mockResolvedValue(mockIntegration);
      mockQueue.add.mockResolvedValue({ id: 'resume-job' });

      await service.resumeIntegrationSync(mockIntegrationId);

      expect(mockPrisma.integration.update).toHaveBeenCalledWith({
        where: { id: mockIntegrationId },
        data: { status: IntegrationStatus.ACTIVE },
      });

      expect(mockQueue.add).toHaveBeenCalledWith(
        SyncJobType.INCREMENTAL_SYNC,
        expect.objectContaining({
          integrationId: mockIntegrationId,
          workspaceId: mockWorkspaceId,
          priority: SyncJobPriority.HIGH,
          lastSyncAt: mockIntegration.lastSyncAt,
        }),
        expect.any(Object)
      );
    });
  });

  describe('getSyncStats', () => {
    it('should return sync statistics', async () => {
      const mockSyncLogs = [
        {
          status: 'completed',
          tasksProcessed: 10,
          startedAt: new Date('2023-01-01T10:00:00Z'),
          completedAt: new Date('2023-01-01T10:05:00Z'),
          errors: [],
        },
        {
          status: 'failed',
          tasksProcessed: 5,
          startedAt: new Date('2023-01-01T11:00:00Z'),
          completedAt: new Date('2023-01-01T11:02:00Z'),
          errors: [{ type: 'NETWORK_ERROR' }],
        },
        {
          status: 'completed',
          tasksProcessed: 8,
          startedAt: new Date('2023-01-01T12:00:00Z'),
          completedAt: new Date('2023-01-01T12:03:00Z'),
          errors: [{ type: 'CONFLICT' }],
        },
      ];

      mockPrisma.syncLog.findMany.mockResolvedValue(mockSyncLogs);

      const result = await service.getSyncStats(mockIntegrationId, 7);

      expect(result).toEqual({
        totalJobs: 3,
        successfulJobs: 2,
        failedJobs: 1,
        averageDuration: expect.any(Number),
        lastSyncAt: mockSyncLogs[0].completedAt,
        tasksProcessed: 23,
        conflictsDetected: 1,
      });

      expect(result.averageDuration).toBeGreaterThan(0);
    });
  });

  describe('cleanupOldData', () => {
    it('should clean up old sync logs and jobs', async () => {
      const deletedCount = 5;
      mockPrisma.syncLog.deleteMany.mockResolvedValue({ count: deletedCount });

      await service.cleanupOldData(30);

      expect(mockPrisma.syncLog.deleteMany).toHaveBeenCalledWith({
        where: {
          createdAt: {
            lt: expect.any(Date),
          },
          status: {
            in: ['completed', 'failed'],
          },
        },
      });

      expect(mockQueue.clean).toHaveBeenCalledTimes(2);
      expect(mockQueue.clean).toHaveBeenCalledWith(
        30 * 24 * 60 * 60 * 1000,
        'completed'
      );
      expect(mockQueue.clean).toHaveBeenCalledWith(
        30 * 24 * 60 * 60 * 1000,
        'failed'
      );
    });
  });
});