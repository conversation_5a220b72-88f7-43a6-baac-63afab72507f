import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { DatabaseService } from '../../database/database.service';
import { IntegrationService } from '../../integrations/services/integration.service';
import { TasksService } from './tasks.service';
import { CreateQuickTaskDto, QuickTaskDestination } from '../dto/create-quick-task.dto';
import { QuickTaskResponseDto } from '../dto/quick-task-response.dto';
import { CreateTaskDto } from '../dto/create-task.dto';
import { IntegrationProvider } from '../../integrations/types';
import { Task } from '@prisma/client';

@Injectable()
export class QuickTaskService {
  private readonly logger = new Logger(QuickTaskService.name);

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly integrationService: IntegrationService,
    private readonly tasksService: TasksService,
  ) {}

  /**
   * Create a quick task in either personal inbox or Google Sheets
   */
  async createQuickTask(
    workspaceId: string,
    userId: string,
    createQuickTaskDto: CreateQuickTaskDto,
  ): Promise<QuickTaskResponseDto> {
    this.logger.log(`Creating quick task for workspace ${workspaceId}, destination: ${createQuickTaskDto.destination}`);

    try {
      if (createQuickTaskDto.destination === QuickTaskDestination.GOOGLE_SHEETS) {
        return await this.createQuickTaskInGoogleSheets(workspaceId, userId, createQuickTaskDto);
      } else {
        return await this.createQuickTaskInPersonalInbox(workspaceId, userId, createQuickTaskDto);
      }
    } catch (error) {
      this.logger.error('Failed to create quick task:', error);
      throw new BadRequestException(`Failed to create quick task: ${error.message}`);
    }
  }

  /**
   * Create quick task in personal inbox (TaskUnify internal)
   */
  private async createQuickTaskInPersonalInbox(
    workspaceId: string,
    userId: string,
    createQuickTaskDto: CreateQuickTaskDto,
  ): Promise<QuickTaskResponseDto> {
    const createTaskDto: CreateTaskDto = {
      title: createQuickTaskDto.title,
      description: createQuickTaskDto.description,
      dueDate: createQuickTaskDto.dueDate,
      status: 'todo',
      priority: 'medium',
      assigneeId: userId,
      tags: ['quick-task'],
      sourceUrl: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/workspaces/${workspaceId}/tasks`,
    };

    const task = await this.tasksService.createTask(workspaceId, createTaskDto);

    return {
      success: true,
      message: 'Quick task created successfully in personal inbox',
      task,
      destination: QuickTaskDestination.PERSONAL_INBOX,
      addAnother: createQuickTaskDto.addAnother || false,
    };
  }

  /**
   * Create quick task in Google Sheets
   */
  private async createQuickTaskInGoogleSheets(
    workspaceId: string,
    userId: string,
    createQuickTaskDto: CreateQuickTaskDto,
  ): Promise<QuickTaskResponseDto> {
    // Find Google Sheets integration for this workspace
    const googleSheetsIntegration = await this.databaseService.integration.findFirst({
      where: {
        workspaceId,
        provider: IntegrationProvider.GOOGLE_SHEETS,
        status: 'ACTIVE',
      },
    });

    if (!googleSheetsIntegration) {
      throw new BadRequestException('No active Google Sheets integration found for this workspace');
    }

    // Create task using the integration adapter
    const adapter = this.integrationService.getAdapter(IntegrationProvider.GOOGLE_SHEETS);
    
    const externalTask = await adapter.createTask({
      title: createQuickTaskDto.title,
      description: createQuickTaskDto.description || '',
      status: 'todo',
      priority: 'medium',
      dueDate: createQuickTaskDto.dueDate ? new Date(createQuickTaskDto.dueDate) : undefined,
      assigneeId: userId,
      tags: ['quick-task'],
      projectName: 'Quick Tasks',
    });

    // Create corresponding task in TaskUnify database
    const createTaskDto: CreateTaskDto = {
      title: createQuickTaskDto.title,
      description: createQuickTaskDto.description,
      dueDate: createQuickTaskDto.dueDate,
      status: 'todo',
      priority: 'medium',
      assigneeId: userId,
      tags: ['quick-task'],
      projectName: 'Quick Tasks',
      sourceUrl: externalTask.sourceUrl,
    };

    // Override the createTask method to use the specific integration
    const task = await this.createTaskWithIntegration(
      workspaceId,
      googleSheetsIntegration.id,
      externalTask.id,
      createTaskDto,
    );

    return {
      success: true,
      message: 'Quick task created successfully in Google Sheets',
      task,
      destination: QuickTaskDestination.GOOGLE_SHEETS,
      externalUrl: externalTask.sourceUrl,
      addAnother: createQuickTaskDto.addAnother || false,
    };
  }

  /**
   * Create task with specific integration (similar to TasksService.createTask but with integration)
   */
  private async createTaskWithIntegration(
    workspaceId: string,
    integrationId: string,
    externalId: string,
    createTaskDto: CreateTaskDto,
  ): Promise<Task> {
    const task = await this.databaseService.task.create({
      data: {
        workspaceId,
        integrationId,
        externalId,
        title: createTaskDto.title,
        description: createTaskDto.description,
        status: createTaskDto.status || 'todo',
        priority: createTaskDto.priority || 'medium',
        assigneeId: createTaskDto.assigneeId,
        assigneeName: createTaskDto.assigneeName,
        dueDate: createTaskDto.dueDate ? new Date(createTaskDto.dueDate) : null,
        estimatedMinutes: createTaskDto.estimatedMinutes,
        tags: createTaskDto.tags || [],
        projectName: createTaskDto.projectName,
        sourceUrl: createTaskDto.sourceUrl,
        syncStatus: 'synced',
      },
    });

    // Calculate and update priority score
    const settings = await this.tasksService.getWorkspacePrioritizationSettings(workspaceId);
    await this.tasksService.updateTaskPriorityScore(task.id, settings);

    return this.tasksService.getTask(workspaceId, task.id);
  }

  /**
   * Get quick task creation preferences for a workspace
   */
  async getQuickTaskPreferences(workspaceId: string): Promise<{
    hasGoogleSheetsIntegration: boolean;
    defaultDestination: QuickTaskDestination;
    keyboardShortcuts: {
      openQuickTask: string;
      submitAndClose: string;
      submitAndAddAnother: string;
    };
  }> {
    const googleSheetsIntegration = await this.databaseService.integration.findFirst({
      where: {
        workspaceId,
        provider: IntegrationProvider.GOOGLE_SHEETS,
        status: 'ACTIVE',
      },
    });

    return {
      hasGoogleSheetsIntegration: !!googleSheetsIntegration,
      defaultDestination: googleSheetsIntegration 
        ? QuickTaskDestination.GOOGLE_SHEETS 
        : QuickTaskDestination.PERSONAL_INBOX,
      keyboardShortcuts: {
        openQuickTask: 'Ctrl+Shift+T',
        submitAndClose: 'Ctrl+Enter',
        submitAndAddAnother: 'Ctrl+Shift+Enter',
      },
    };
  }
}