import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class ProgressReportFiltersDto {
  @ApiProperty({
    description: 'Start date for the report period (ISO 8601 format)',
    example: '2024-01-01',
  })
  @IsDateString()
  @Transform(({ value }) => new Date(value))
  startDate: Date;

  @ApiProperty({
    description: 'End date for the report period (ISO 8601 format)',
    example: '2024-12-31',
  })
  @IsDateString()
  @Transform(({ value }) => new Date(value))
  endDate: Date;

  @ApiPropertyOptional({
    description: 'Filter by integration source (e.g., asana, trello, google_sheets)',
    example: 'asana',
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({
    description: 'Filter by project name',
    example: 'Website Redesign',
  })
  @IsOptional()
  @IsString()
  project?: string;

  @ApiPropertyOptional({
    description: 'Filter by assignee ID',
    example: 'user123',
  })
  @IsOptional()
  @IsString()
  assignee?: string;
}