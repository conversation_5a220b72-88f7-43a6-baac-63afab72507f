import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { TasksModule } from '../tasks.module';
import { DatabaseModule } from '../../database/database.module';
import { DatabaseService } from '../../database/database.service';

describe('Daily Planning API (e2e)', () => {
  let app: INestApplication;
  let databaseService: DatabaseService;

  const mockWorkspaceId = 'test-workspace-id';
  const mockUserId = 'test-user-id';
  const mockPlanDate = '2024-01-15';

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TasksModule, DatabaseModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);
    
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Clean up test data
    await databaseService.dailyPlanTask.deleteMany({});
    await databaseService.dailyPlan.deleteMany({});
    await databaseService.task.deleteMany({});
    await databaseService.integration.deleteMany({});
    await databaseService.workspace.deleteMany({});
    await databaseService.user.deleteMany({});
  });

  describe('POST /workspaces/:workspaceId/tasks/daily-plan', () => {
    it('should create a daily plan successfully', async () => {
      // Setup test data
      const user = await databaseService.user.create({
        data: {
          id: mockUserId,
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await databaseService.workspace.create({
        data: {
          id: mockWorkspaceId,
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const integration = await databaseService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'personal',
          name: 'Personal Tasks',
          encryptedCredentials: 'encrypted-creds',
        },
      });

      const task1 = await databaseService.task.create({
        data: {
          workspaceId: workspace.id,
          integrationId: integration.id,
          externalId: 'task-1',
          title: 'Test Task 1',
          sourceUrl: 'https://example.com/task1',
        },
      });

      const task2 = await databaseService.task.create({
        data: {
          workspaceId: workspace.id,
          integrationId: integration.id,
          externalId: 'task-2',
          title: 'Test Task 2',
          sourceUrl: 'https://example.com/task2',
        },
      });

      const createDailyPlanDto = {
        planDate: mockPlanDate,
        tasks: [
          { taskId: task1.id, estimatedMinutes: 120, orderIndex: 0 },
          { taskId: task2.id, estimatedMinutes: 60, orderIndex: 1 },
        ],
      };

      const response = await request(app.getHttpServer())
        .post(`/workspaces/${mockWorkspaceId}/tasks/daily-plan`)
        .send(createDailyPlanDto)
        .expect(201);

      expect(response.body).toMatchObject({
        workspaceId: mockWorkspaceId,
        planDate: mockPlanDate + 'T00:00:00.000Z',
        totalEstimatedMinutes: 180,
        totalCompletedMinutes: 0,
        completionProgress: 0,
        exceedsRecommendedTime: false,
        tasks: expect.arrayContaining([
          expect.objectContaining({
            taskId: task1.id,
            estimatedMinutes: 120,
            orderIndex: 0,
          }),
          expect.objectContaining({
            taskId: task2.id,
            estimatedMinutes: 60,
            orderIndex: 1,
          }),
        ]),
      });
    });

    it('should return 400 when tasks do not belong to workspace', async () => {
      const createDailyPlanDto = {
        planDate: mockPlanDate,
        tasks: [
          { taskId: 'non-existent-task', estimatedMinutes: 120, orderIndex: 0 },
        ],
      };

      await request(app.getHttpServer())
        .post(`/workspaces/${mockWorkspaceId}/tasks/daily-plan`)
        .send(createDailyPlanDto)
        .expect(400);
    });

    it('should warn when daily plan exceeds 8 hours', async () => {
      // Setup test data
      const user = await databaseService.user.create({
        data: {
          id: mockUserId,
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await databaseService.workspace.create({
        data: {
          id: mockWorkspaceId,
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const integration = await databaseService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'personal',
          name: 'Personal Tasks',
          encryptedCredentials: 'encrypted-creds',
        },
      });

      const task = await databaseService.task.create({
        data: {
          workspaceId: workspace.id,
          integrationId: integration.id,
          externalId: 'long-task',
          title: 'Long Task',
          sourceUrl: 'https://example.com/long-task',
        },
      });

      const createDailyPlanDto = {
        planDate: mockPlanDate,
        tasks: [
          { taskId: task.id, estimatedMinutes: 500, orderIndex: 0 }, // Over 8 hours
        ],
      };

      const response = await request(app.getHttpServer())
        .post(`/workspaces/${mockWorkspaceId}/tasks/daily-plan`)
        .send(createDailyPlanDto)
        .expect(201);

      expect(response.body.exceedsRecommendedTime).toBe(true);
      expect(response.body.totalEstimatedMinutes).toBe(500);
    });
  });

  describe('GET /workspaces/:workspaceId/tasks/daily-plan/:planDate', () => {
    it('should return daily plan when it exists', async () => {
      // Setup test data with daily plan
      const user = await databaseService.user.create({
        data: {
          id: mockUserId,
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await databaseService.workspace.create({
        data: {
          id: mockWorkspaceId,
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const integration = await databaseService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'personal',
          name: 'Personal Tasks',
          encryptedCredentials: 'encrypted-creds',
        },
      });

      const task = await databaseService.task.create({
        data: {
          workspaceId: workspace.id,
          integrationId: integration.id,
          externalId: 'task-1',
          title: 'Test Task',
          sourceUrl: 'https://example.com/task1',
        },
      });

      const dailyPlan = await databaseService.dailyPlan.create({
        data: {
          workspaceId: workspace.id,
          userId: mockUserId,
          planDate: new Date(mockPlanDate),
          totalEstimatedMinutes: 120,
        },
      });

      await databaseService.dailyPlanTask.create({
        data: {
          dailyPlanId: dailyPlan.id,
          taskId: task.id,
          estimatedMinutes: 120,
          orderIndex: 0,
        },
      });

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${mockWorkspaceId}/tasks/daily-plan/${mockPlanDate}`)
        .expect(200);

      expect(response.body).toMatchObject({
        workspaceId: mockWorkspaceId,
        planDate: mockPlanDate + 'T00:00:00.000Z',
        totalEstimatedMinutes: 120,
        tasks: expect.arrayContaining([
          expect.objectContaining({
            taskId: task.id,
            estimatedMinutes: 120,
          }),
        ]),
      });
    });

    it('should return null when daily plan does not exist', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workspaces/${mockWorkspaceId}/tasks/daily-plan/${mockPlanDate}`)
        .expect(200);

      expect(response.body).toBeNull();
    });
  });

  describe('POST /workspaces/:workspaceId/tasks/daily-plan/:planDate/tasks/:taskId/complete', () => {
    it('should mark task as complete and update progress', async () => {
      // Setup test data
      const user = await databaseService.user.create({
        data: {
          id: mockUserId,
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await databaseService.workspace.create({
        data: {
          id: mockWorkspaceId,
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const integration = await databaseService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'personal',
          name: 'Personal Tasks',
          encryptedCredentials: 'encrypted-creds',
        },
      });

      const task = await databaseService.task.create({
        data: {
          workspaceId: workspace.id,
          integrationId: integration.id,
          externalId: 'task-1',
          title: 'Test Task',
          sourceUrl: 'https://example.com/task1',
        },
      });

      const dailyPlan = await databaseService.dailyPlan.create({
        data: {
          workspaceId: workspace.id,
          userId: mockUserId,
          planDate: new Date(mockPlanDate),
          totalEstimatedMinutes: 120,
        },
      });

      await databaseService.dailyPlanTask.create({
        data: {
          dailyPlanId: dailyPlan.id,
          taskId: task.id,
          estimatedMinutes: 120,
          orderIndex: 0,
        },
      });

      const completeDailyPlanTaskDto = { actualMinutes: 90 };

      const response = await request(app.getHttpServer())
        .post(`/workspaces/${mockWorkspaceId}/tasks/daily-plan/${mockPlanDate}/tasks/${task.id}/complete`)
        .send(completeDailyPlanTaskDto)
        .expect(200);

      expect(response.body.totalCompletedMinutes).toBe(90);
      expect(response.body.completionProgress).toBe(100); // 1 out of 1 task completed

      // Verify task status was updated
      const updatedTask = await databaseService.task.findUnique({
        where: { id: task.id },
      });
      expect(updatedTask.status).toBe('done');
      expect(updatedTask.syncStatus).toBe('pending');
    });

    it('should return 404 when daily plan does not exist', async () => {
      await request(app.getHttpServer())
        .post(`/workspaces/${mockWorkspaceId}/tasks/daily-plan/${mockPlanDate}/tasks/non-existent-task/complete`)
        .send({})
        .expect(404);
    });
  });

  describe('PATCH /workspaces/:workspaceId/tasks/daily-plan/:planDate', () => {
    it('should update daily plan task order', async () => {
      // Setup test data
      const user = await databaseService.user.create({
        data: {
          id: mockUserId,
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await databaseService.workspace.create({
        data: {
          id: mockWorkspaceId,
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const integration = await databaseService.integration.create({
        data: {
          workspaceId: workspace.id,
          provider: 'personal',
          name: 'Personal Tasks',
          encryptedCredentials: 'encrypted-creds',
        },
      });

      const task1 = await databaseService.task.create({
        data: {
          workspaceId: workspace.id,
          integrationId: integration.id,
          externalId: 'task-1',
          title: 'Test Task 1',
          sourceUrl: 'https://example.com/task1',
        },
      });

      const task2 = await databaseService.task.create({
        data: {
          workspaceId: workspace.id,
          integrationId: integration.id,
          externalId: 'task-2',
          title: 'Test Task 2',
          sourceUrl: 'https://example.com/task2',
        },
      });

      const dailyPlan = await databaseService.dailyPlan.create({
        data: {
          workspaceId: workspace.id,
          userId: mockUserId,
          planDate: new Date(mockPlanDate),
          totalEstimatedMinutes: 180,
        },
      });

      await databaseService.dailyPlanTask.createMany({
        data: [
          {
            dailyPlanId: dailyPlan.id,
            taskId: task1.id,
            estimatedMinutes: 120,
            orderIndex: 0,
          },
          {
            dailyPlanId: dailyPlan.id,
            taskId: task2.id,
            estimatedMinutes: 60,
            orderIndex: 1,
          },
        ],
      });

      // Reorder tasks
      const updateDailyPlanDto = {
        tasks: [
          { taskId: task2.id, estimatedMinutes: 60, orderIndex: 0 }, // Move task2 first
          { taskId: task1.id, estimatedMinutes: 120, orderIndex: 1 }, // Move task1 second
        ],
      };

      const response = await request(app.getHttpServer())
        .patch(`/workspaces/${mockWorkspaceId}/tasks/daily-plan/${mockPlanDate}`)
        .send(updateDailyPlanDto)
        .expect(200);

      expect(response.body.tasks[0].taskId).toBe(task2.id);
      expect(response.body.tasks[1].taskId).toBe(task1.id);
    });

    it('should return 404 when daily plan does not exist', async () => {
      const updateDailyPlanDto = {
        tasks: [
          { taskId: 'some-task', estimatedMinutes: 60, orderIndex: 0 },
        ],
      };

      await request(app.getHttpServer())
        .patch(`/workspaces/${mockWorkspaceId}/tasks/daily-plan/${mockPlanDate}`)
        .send(updateDailyPlanDto)
        .expect(404);
    });
  });

  describe('DELETE /workspaces/:workspaceId/tasks/daily-plan/:planDate', () => {
    it('should delete daily plan successfully', async () => {
      // Setup test data
      const user = await databaseService.user.create({
        data: {
          id: mockUserId,
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await databaseService.workspace.create({
        data: {
          id: mockWorkspaceId,
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      const dailyPlan = await databaseService.dailyPlan.create({
        data: {
          workspaceId: workspace.id,
          userId: mockUserId,
          planDate: new Date(mockPlanDate),
          totalEstimatedMinutes: 120,
        },
      });

      await request(app.getHttpServer())
        .delete(`/workspaces/${mockWorkspaceId}/tasks/daily-plan/${mockPlanDate}`)
        .expect(204);

      // Verify deletion
      const deletedPlan = await databaseService.dailyPlan.findUnique({
        where: { id: dailyPlan.id },
      });
      expect(deletedPlan).toBeNull();
    });

    it('should return 404 when daily plan does not exist', async () => {
      await request(app.getHttpServer())
        .delete(`/workspaces/${mockWorkspaceId}/tasks/daily-plan/${mockPlanDate}`)
        .expect(404);
    });
  });

  describe('GET /workspaces/:workspaceId/tasks/daily-plans', () => {
    it('should return daily plans for date range', async () => {
      // Setup test data
      const user = await databaseService.user.create({
        data: {
          id: mockUserId,
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const workspace = await databaseService.workspace.create({
        data: {
          id: mockWorkspaceId,
          name: 'Test Workspace',
          slug: 'test-workspace',
          ownerId: user.id,
        },
      });

      await databaseService.dailyPlan.createMany({
        data: [
          {
            workspaceId: workspace.id,
            userId: mockUserId,
            planDate: new Date('2024-01-15'),
            totalEstimatedMinutes: 120,
          },
          {
            workspaceId: workspace.id,
            userId: mockUserId,
            planDate: new Date('2024-01-16'),
            totalEstimatedMinutes: 180,
          },
        ],
      });

      const response = await request(app.getHttpServer())
        .get(`/workspaces/${mockWorkspaceId}/tasks/daily-plans`)
        .query({ startDate: '2024-01-15', endDate: '2024-01-17' })
        .expect(200);

      expect(response.body).toHaveLength(2);
      expect(response.body[0].planDate).toBe('2024-01-15T00:00:00.000Z');
      expect(response.body[1].planDate).toBe('2024-01-16T00:00:00.000Z');
    });
  });
});