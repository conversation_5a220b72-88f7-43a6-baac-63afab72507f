# Database Configuration - Neon
# Get your connection string from Neon Console: https://console.neon.tech
DATABASE_URL="postgresql://[username]:[password]@[endpoint]/[database]?sslmode=require"
# Direct URL for migrations (same as DATABASE_URL for Neon)
DIRECT_URL="postgresql://[username]:[password]@[endpoint]/[database]?sslmode=require"
# Example: DATABASE_URL="postgresql://alex:<EMAIL>/neondb?sslmode=require"

# Redis Configuration
REDIS_URL="redis://localhost:6379"

# Application Configuration
NODE_ENV=development
PORT=3001
JWT_SECRET=your_jwt_secret_here_minimum_32_characters
JWT_EXPIRES_IN=7d

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# OAuth Configuration - Google
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=http://localhost:3001/auth/google/callback

# Integration API Keys - Asana
ASANA_CLIENT_ID=your_asana_client_id
ASANA_CLIENT_SECRET=your_asana_client_secret
ASANA_CALLBACK_URL=http://localhost:3001/integrations/asana/callback

# Integration API Keys - Trello
TRELLO_API_KEY=your_trello_api_key
TRELLO_API_SECRET=your_trello_api_secret
TRELLO_CALLBACK_URL=http://localhost:3001/integrations/trello/callback

# Integration API Keys - Jira
JIRA_CLIENT_ID=your_jira_client_id
JIRA_CLIENT_SECRET=your_jira_client_secret
JIRA_CALLBACK_URL=http://localhost:3001/integrations/jira/callback

# Integration API Keys - ClickUp
CLICKUP_CLIENT_ID=your_clickup_client_id
CLICKUP_CLIENT_SECRET=your_clickup_client_secret
CLICKUP_CALLBACK_URL=http://localhost:3001/integrations/clickup/callback

# Integration API Keys - Todoist
TODOIST_CLIENT_ID=your_todoist_client_id
TODOIST_CLIENT_SECRET=your_todoist_client_secret
TODOIST_CALLBACK_URL=http://localhost:3001/integrations/todoist/callback

# Integration API Keys - Monday
MONDAY_CLIENT_ID=your_monday_client_id
MONDAY_CLIENT_SECRET=your_monday_client_secret
MONDAY_CALLBACK_URL=http://localhost:3001/integrations/monday/callback

# Integration API Keys - Notion
NOTION_CLIENT_ID=your_notion_client_id
NOTION_CLIENT_SECRET=your_notion_client_secret
NOTION_CALLBACK_URL=http://localhost:3001/integrations/notion/callback

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret_here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=debug

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/

# Encryption (for sensitive data)
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Background Jobs
QUEUE_REDIS_URL=redis://localhost:6379
SYNC_INTERVAL_MINUTES=15
MAX_SYNC_RETRIES=3

# API Versioning
API_VERSION=v1

# Health Check
HEALTH_CHECK_ENABLED=true
