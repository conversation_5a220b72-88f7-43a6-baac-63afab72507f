import { useState, useEffect, useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Loader2, AlertCircle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { TaskFilters } from './TaskFilters'
import { TaskListItem } from './TaskListItem'
import { TaskDetailPanel } from './TaskDetailPanel'
import { BulkOperations } from './BulkOperations'
import { TaskService } from '@/services/task'
import { useWorkspaceStore } from '@/store/workspace'
import { 
  Task, 
  TaskQuery, 
  TaskSortField, 
  SortOrder, 
  UpdateTaskRequest,
  BulkUpdateTasksRequest 
} from '@/types/task'
import { cn } from '@/lib/utils'

interface TaskListProps {
  className?: string
}

const DEFAULT_QUERY: TaskQuery = {
  limit: 50,
  offset: 0,
  sortBy: TaskSortField.PRIORITY_SCORE,
  sortOrder: SortOrder.DESC
}

export function TaskList({ className }: TaskListProps) {
  const { currentWorkspace } = useWorkspaceStore()
  const queryClient = useQueryClient()
  
  const [query, setQuery] = useState<TaskQuery>(DEFAULT_QUERY)
  const [selectedTaskIds, setSelectedTaskIds] = useState<string[]>([])
  const [selectedTask, setSelectedTask] = useState<Task | null>(null)
  const [isDetailPanelOpen, setIsDetailPanelOpen] = useState(false)

  // Reset state when workspace changes
  useEffect(() => {
    setQuery(DEFAULT_QUERY)
    setSelectedTaskIds([])
    setSelectedTask(null)
    setIsDetailPanelOpen(false)
  }, [currentWorkspace?.id])

  // Fetch tasks
  const {
    data: tasksResponse,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['tasks', currentWorkspace?.id, query],
    queryFn: () => currentWorkspace ? TaskService.getTasks(currentWorkspace.id, query) : null,
    enabled: !!currentWorkspace,
    staleTime: 30000, // 30 seconds
  })

  // Fetch filter options
  const { data: filterOptions } = useQuery({
    queryKey: ['task-filter-options', currentWorkspace?.id],
    queryFn: () => currentWorkspace ? TaskService.getFilterOptions(currentWorkspace.id) : null,
    enabled: !!currentWorkspace,
    staleTime: 300000, // 5 minutes
  })

  // Update task mutation
  const updateTaskMutation = useMutation({
    mutationFn: ({ taskId, updates }: { taskId: string; updates: UpdateTaskRequest }) =>
      currentWorkspace ? TaskService.updateTask(currentWorkspace.id, taskId, updates) : Promise.reject(),
    onSuccess: (updatedTask) => {
      // Update the task in the cache
      queryClient.setQueryData(['tasks', currentWorkspace?.id, query], (old: any) => {
        if (!old) return old
        return {
          ...old,
          tasks: old.tasks.map((task: Task) => 
            task.id === updatedTask.id ? updatedTask : task
          )
        }
      })
      
      // Update selected task if it's the one being edited
      if (selectedTask?.id === updatedTask.id) {
        setSelectedTask(updatedTask)
      }
    },
    onError: (error) => {
      console.error('Failed to update task:', error)
    }
  })

  // Bulk update mutation
  const bulkUpdateMutation = useMutation({
    mutationFn: (request: BulkUpdateTasksRequest) =>
      currentWorkspace ? TaskService.bulkUpdateTasks(currentWorkspace.id, request) : Promise.reject(),
    onSuccess: () => {
      // Refetch tasks to get updated data
      refetch()
      setSelectedTaskIds([])
    },
    onError: (error) => {
      console.error('Failed to bulk update tasks:', error)
    }
  })

  // Delete task mutation
  const deleteTaskMutation = useMutation({
    mutationFn: (taskId: string) =>
      currentWorkspace ? TaskService.deleteTask(currentWorkspace.id, taskId) : Promise.reject(),
    onSuccess: () => {
      refetch()
      setIsDetailPanelOpen(false)
      setSelectedTask(null)
    },
    onError: (error) => {
      console.error('Failed to delete task:', error)
    }
  })

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation({
    mutationFn: (taskIds: string[]) =>
      Promise.all(taskIds.map(taskId => 
        currentWorkspace ? TaskService.deleteTask(currentWorkspace.id, taskId) : Promise.reject()
      )),
    onSuccess: () => {
      refetch()
      setSelectedTaskIds([])
    },
    onError: (error) => {
      console.error('Failed to bulk delete tasks:', error)
    }
  })

  const handleFiltersChange = useCallback((newQuery: TaskQuery) => {
    setQuery({ ...newQuery, offset: 0 }) // Reset to first page when filters change
    setSelectedTaskIds([]) // Clear selection when filters change
  }, [])

  const handleTaskSelection = useCallback((taskId: string, selected: boolean) => {
    setSelectedTaskIds(prev => 
      selected 
        ? [...prev, taskId]
        : prev.filter(id => id !== taskId)
    )
  }, [])

  const handleSelectAll = useCallback(() => {
    if (!tasksResponse?.tasks) return
    
    const allTaskIds = tasksResponse.tasks.map(task => task.id)
    const allSelected = allTaskIds.every(id => selectedTaskIds.includes(id))
    
    if (allSelected) {
      setSelectedTaskIds([])
    } else {
      setSelectedTaskIds(allTaskIds)
    }
  }, [tasksResponse?.tasks, selectedTaskIds])

  const handleTaskClick = useCallback((task: Task) => {
    setSelectedTask(task)
    setIsDetailPanelOpen(true)
  }, [])

  const handleLoadMore = useCallback(() => {
    if (tasksResponse?.hasMore) {
      setQuery(prev => ({
        ...prev,
        offset: (prev.offset || 0) + (prev.limit || 50)
      }))
    }
  }, [tasksResponse?.hasMore])

  const handleUpdateTask = useCallback(async (taskId: string, updates: UpdateTaskRequest) => {
    await updateTaskMutation.mutateAsync({ taskId, updates })
  }, [updateTaskMutation])

  const handleDeleteTask = useCallback(async (taskId: string) => {
    await deleteTaskMutation.mutateAsync(taskId)
  }, [deleteTaskMutation])

  const handleBulkUpdate = useCallback(async (request: BulkUpdateTasksRequest) => {
    await bulkUpdateMutation.mutateAsync(request)
  }, [bulkUpdateMutation])

  const handleBulkDelete = useCallback(async (taskIds: string[]) => {
    await bulkDeleteMutation.mutateAsync(taskIds)
  }, [bulkDeleteMutation])

  if (!currentWorkspace) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">Please select a workspace to view tasks.</p>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load tasks. Please try again.
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            className="ml-2"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  const tasks = tasksResponse?.tasks || []
  const isAllSelected = tasks.length > 0 && tasks.every(task => selectedTaskIds.includes(task.id))
  const isSomeSelected = selectedTaskIds.length > 0 && !isAllSelected

  return (
    <div className={cn('space-y-6', className)}>
      {/* Filters */}
      <TaskFilters
        filters={query}
        onFiltersChange={handleFiltersChange}
        filterOptions={filterOptions}
      />

      {/* Bulk operations */}
      <BulkOperations
        selectedTaskIds={selectedTaskIds}
        onBulkUpdate={handleBulkUpdate}
        onBulkDelete={handleBulkDelete}
        onClearSelection={() => setSelectedTaskIds([])}
        isLoading={bulkUpdateMutation.isPending || bulkDeleteMutation.isPending}
      />

      {/* Task list header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold">
            Tasks ({tasksResponse?.total || 0})
          </h2>
          
          {tasks.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
              className="flex items-center gap-2"
            >
              <input
                type="checkbox"
                checked={isAllSelected}
                ref={(el) => {
                  if (el) el.indeterminate = isSomeSelected
                }}
                onChange={() => {}} // Handled by onClick
                className="rounded"
              />
              Select All
            </Button>
          )}
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => refetch()}
          disabled={isLoading}
        >
          <RefreshCw className={cn('h-4 w-4 mr-1', isLoading && 'animate-spin')} />
          Refresh
        </Button>
      </div>

      {/* Task list */}
      <div className="space-y-3">
        {isLoading && tasks.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            Loading tasks...
          </div>
        ) : tasks.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">No tasks found matching your criteria.</p>
            <Button
              variant="outline"
              onClick={() => setQuery(DEFAULT_QUERY)}
            >
              Clear Filters
            </Button>
          </div>
        ) : (
          <>
            {tasks.map((task) => (
              <TaskListItem
                key={task.id}
                task={task}
                isSelected={selectedTaskIds.includes(task.id)}
                onSelectionChange={handleTaskSelection}
                onTaskClick={handleTaskClick}
              />
            ))}

            {/* Load more button */}
            {tasksResponse?.hasMore && (
              <div className="flex justify-center pt-4">
                <Button
                  variant="outline"
                  onClick={handleLoadMore}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Loading...
                    </>
                  ) : (
                    'Load More'
                  )}
                </Button>
              </div>
            )}
          </>
        )}
      </div>

      {/* Task detail panel */}
      <TaskDetailPanel
        task={selectedTask}
        isOpen={isDetailPanelOpen}
        onClose={() => {
          setIsDetailPanelOpen(false)
          setSelectedTask(null)
        }}
        onUpdate={handleUpdateTask}
        onDelete={handleDeleteTask}
        isLoading={updateTaskMutation.isPending || deleteTaskMutation.isPending}
      />
    </div>
  )
}