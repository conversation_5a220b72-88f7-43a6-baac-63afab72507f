/**
 * Manual test for Google Sheets Adapter
 * 
 * This test can be run independently to verify basic functionality
 * without the full Jest test environment.
 * 
 * Run with: npx ts-node src/integrations/adapters/__tests__/google-sheets.manual.test.ts
 */

import { GoogleSheetsAdapter } from '../google-sheets.adapter';
import { IntegrationProvider } from '../../types';

async function runManualTests() {
  console.log('🧪 Google Sheets Adapter Manual Tests');
  console.log('=====================================');

  let passedTests = 0;
  let totalTests = 0;

  function test(name: string, testFn: () => boolean | Promise<boolean>) {
    return async () => {
      totalTests++;
      try {
        const result = await testFn();
        if (result) {
          console.log(`✅ ${name}`);
          passedTests++;
        } else {
          console.log(`❌ ${name}`);
        }
      } catch (error) {
        console.log(`❌ ${name} - Error: ${error.message}`);
      }
    };
  }

  // Test 1: Adapter instantiation
  await test('Adapter can be instantiated', () => {
    const adapter = new GoogleSheetsAdapter();
    return adapter !== null && adapter !== undefined;
  })();

  // Test 2: Provider identification
  await test('Adapter has correct provider', () => {
    const adapter = new GoogleSheetsAdapter();
    return adapter.getProvider() === IntegrationProvider.GOOGLE_SHEETS;
  })();

  // Test 3: Two-way sync support
  await test('Adapter supports two-way sync', () => {
    const adapter = new GoogleSheetsAdapter();
    return adapter.supportsTwoWaySync() === true;
  })();

  // Test 4: Webhook support
  await test('Adapter does not support webhooks', () => {
    const adapter = new GoogleSheetsAdapter();
    return adapter.supportsWebhooks() === false;
  })();

  // Test 5: Invalid credentials validation
  await test('Invalid credentials return false', async () => {
    const adapter = new GoogleSheetsAdapter();
    const result = await adapter.validateCredentials({
      accessToken: 'invalid-token',
      refreshToken: 'invalid-refresh',
    });
    return result === false;
  })();

  // Test 6: Missing spreadsheet ID error
  await test('Missing spreadsheet ID throws error', async () => {
    const adapter = new GoogleSheetsAdapter();
    try {
      await adapter.fetchTasks({
        syncInterval: 15,
        enableTwoWaySync: true,
        fieldMappings: [],
        filters: [],
        customSettings: {}, // Missing spreadsheetId
      });
      return false; // Should have thrown an error
    } catch (error) {
      return error.message.includes('Spreadsheet ID is required');
    }
  })();

  // Test 7: Task ID generation
  await test('Task ID generation works correctly', () => {
    const adapter = new GoogleSheetsAdapter();
    // Access private method through any cast for testing
    const taskId = (adapter as any).generateTaskId('sheet123', 'Tasks', 5);
    return taskId === 'gs_sheet123_Tasks_5';
  })();

  // Test 8: Task ID parsing
  await test('Task ID parsing works correctly', () => {
    const adapter = new GoogleSheetsAdapter();
    try {
      const parsed = (adapter as any).parseTaskId('gs_sheet123_Tasks_5');
      return parsed.spreadsheetId === 'sheet123' && 
             parsed.sheetName === 'Tasks' && 
             parsed.rowIndex === '5';
    } catch (error) {
      return false;
    }
  })();

  // Test 9: Invalid task ID parsing
  await test('Invalid task ID throws error', () => {
    const adapter = new GoogleSheetsAdapter();
    try {
      (adapter as any).parseTaskId('invalid-task-id');
      return false; // Should have thrown an error
    } catch (error) {
      return error.message.includes('Invalid Google Sheets task ID format');
    }
  })();

  // Test 10: Date parsing
  await test('Date parsing handles multiple formats', () => {
    const adapter = new GoogleSheetsAdapter();
    const testDates = [
      '2024-01-15',   // YYYY-MM-DD
      '01/15/2024',   // MM/DD/YYYY
      '01-15-2024',   // MM-DD-YYYY
    ];

    for (const dateStr of testDates) {
      const parsed = (adapter as any).parseDateValue(dateStr);
      if (!parsed || isNaN(parsed.getTime())) {
        return false;
      }
    }
    return true;
  })();

  // Test 11: Invalid date handling
  await test('Invalid dates return undefined', () => {
    const adapter = new GoogleSheetsAdapter();
    const result = (adapter as any).parseDateValue('invalid-date');
    return result === undefined;
  })();

  // Test 12: Tags parsing
  await test('Tags parsing works correctly', () => {
    const adapter = new GoogleSheetsAdapter();
    const tags1 = (adapter as any).parseTagsValue('urgent, feature, bug');
    const tags2 = (adapter as any).parseTagsValue('enhancement');
    const tags3 = (adapter as any).parseTagsValue('');

    return JSON.stringify(tags1) === JSON.stringify(['urgent', 'feature', 'bug']) &&
           JSON.stringify(tags2) === JSON.stringify(['enhancement']) &&
           JSON.stringify(tags3) === JSON.stringify([]);
  })();

  // Test 13: Number parsing
  await test('Number parsing works correctly', () => {
    const adapter = new GoogleSheetsAdapter();
    const num1 = (adapter as any).parseNumberValue('120');
    const num2 = (adapter as any).parseNumberValue('invalid');
    const num3 = (adapter as any).parseNumberValue('');

    return num1 === 120 && num2 === undefined && num3 === undefined;
  })();

  // Test 14: Status normalization
  await test('Status normalization works correctly', () => {
    const adapter = new GoogleSheetsAdapter();
    const status1 = (adapter as any).normalizeTaskStatus('in progress');
    const status2 = (adapter as any).normalizeTaskStatus('done');
    const status3 = (adapter as any).normalizeTaskStatus('unknown');

    return status1 === 'in_progress' && status2 === 'done' && status3 === 'todo';
  })();

  // Test 15: Priority normalization
  await test('Priority normalization works correctly', () => {
    const adapter = new GoogleSheetsAdapter();
    const priority1 = (adapter as any).normalizeTaskPriority('high');
    const priority2 = (adapter as any).normalizeTaskPriority(4);
    const priority3 = (adapter as any).normalizeTaskPriority('unknown');

    return priority1 === 'high' && priority2 === 'urgent' && priority3 === 'medium';
  })();

  // Summary
  console.log('\n📊 Test Results');
  console.log('===============');
  console.log(`Passed: ${passedTests}/${totalTests}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  if (passedTests === totalTests) {
    console.log('🎉 All tests passed!');
    return true;
  } else {
    console.log('❌ Some tests failed');
    return false;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runManualTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

export { runManualTests };