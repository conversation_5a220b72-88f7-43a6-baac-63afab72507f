---

inclusion: fileMatch
fileMatchPattern: \['**/\*.spec.ts', '**/*.test.ts', '**/test/**/*.ts']
-----------------------------------------------------------------------

# Jest Testing Rules & Best Practices

## Test File Organization

### File Naming Conventions

* Unit tests: `*.spec.ts` (e.g., `tasks.service.spec.ts`)
* Integration tests: `*.integration.spec.ts` (e.g., `tasks.integration.spec.ts`)
* E2E tests: `*.e2e.spec.ts` or place in `/test` directory
* API tests: `*.api.spec.ts` (e.g., `daily-planning.api.spec.ts`)

### Directory Structure

* Place unit tests in `__tests__` folders alongside source files
* Use consistent naming: `src/module/__tests__/service.spec.ts`
* Group related test files in the same directory as the code they test

## Test Structure & Organization

### Describe Block Hierarchy

```typescript
describe('ServiceName', () => {
  describe('methodName', () => {
    it('should do something specific', () => {
      // Test implementation
    });
  });
});
```

### Test Setup Patterns

* Use `beforeEach` for test-specific setup that runs before each test
* Use `beforeAll` for expensive setup that can be shared across tests
* Use `afterEach`/`afterAll` for cleanup operations
* Always clean up resources to prevent test pollution

## Mock Patterns & Best Practices

### Service Mocking

```typescript
const mockService = {
  method1: jest.fn(),
  method2: jest.fn(),
} as jest.Mocked<ServiceType>;
```

### Database Mocking

* Mock Prisma/DatabaseService methods using `jest.fn()`
* Create reusable mock data objects at the top of test files
* Use `mockResolvedValue` for async operations
* Use `mockRejectedValue` for error scenarios

### Module Testing Setup

```typescript
const module: TestingModule = await Test.createTestingModule({
  providers: [
    ServiceUnderTest,
    {
      provide: DependencyService,
      useValue: mockDependencyService,
    },
  ],
}).compile();
```

## Assertion Guidelines

### Use Specific Matchers

* Prefer `toBe()` for primitive values and object references
* Use `toEqual()` for deep object comparison
* Use `toMatchObject()` for partial object matching
* Use `toHaveProperty()` for checking object properties
* Use `toContain()` for array/string inclusion checks

### Error Testing

```typescript
await expect(service.method()).rejects.toThrow(NotFoundException);
await expect(service.method()).rejects.toThrow('Specific error message');
```

### Async Testing

* Always use `await` with async operations
* Use `resolves`/`rejects` matchers for promise testing
* Ensure all async operations are properly awaited

## Test Data Management

### Mock Data Creation

* Create reusable mock objects at the top of test files
* Use consistent IDs and naming patterns
* Include all required fields for realistic testing
* Use factory functions for generating test data variations

### Test Isolation

* Each test should be independent and not rely on other tests
* Clean up any created data after tests
* Use unique identifiers to prevent conflicts
* Reset mocks between tests using `jest.clearAllMocks()`

## Integration Testing Patterns

### API Testing Setup

```typescript
let app: INestApplication;
let databaseService: DatabaseService;

beforeAll(async () => {
  const moduleFixture = await Test.createTestingModule({
    imports: [ModuleUnderTest],
  }).compile();
  
  app = moduleFixture.createNestApplication();
  await app.init();
});

afterAll(async () => {
  await app.close();
});
```

### Database Testing

* Use test database or in-memory database for integration tests
* Clean up test data in `beforeEach`/`afterEach` hooks
* Create minimal test data required for each test
* Use transactions for test isolation when possible

## Performance & Resource Management

### Timeouts

* Set explicit timeouts for long-running tests: `jest.setTimeout(30000)`
* Unit tests: ≤ 5 seconds
* Integration tests: ≤ 30 seconds
* Never rely on Jest's default 5000ms for integration tests

### Resource Cleanup

```typescript
afterAll(async () => {
  await databaseService.$disconnect();
  await app.close();
});

afterEach(() => {
  jest.clearAllMocks();
});
```

### Memory Management

* Don't keep large objects in global variables between tests
* Generate small test fixtures instead of importing huge datasets
* Use `--runInBand` for integration tests to avoid parallel memory usage
* Use `--detectOpenHandles` to find unclosed async resources

## Coverage & Quality Standards

### Coverage Requirements

* Maintain minimum 80% test coverage
* Focus on critical business logic and edge cases
* Test both success and failure scenarios
* Include boundary condition testing

### Test Quality Checklist

* Each test should test one specific behavior
* Test names should clearly describe the expected behavior
* Tests should be deterministic and not flaky
* Avoid testing implementation details, focus on behavior
* Include both positive and negative test cases

## Error Handling Testing

### Exception Testing Patterns

```typescript
it('should throw NotFoundException when resource not found', async () => {
  mockService.findById.mockResolvedValue(null);
  
  await expect(service.getById('non-existent')).rejects.toThrow(
    NotFoundException
  );
});
```

### Validation Testing

* Test all validation rules for DTOs
* Test boundary conditions (min/max values)
* Test required field validation
* Test format validation (emails, dates, etc.)

## Command Usage & Optimization

### Unit Tests

```bash
npm test -- --maxWorkers=50%
```

### Integration Tests

```bash
NODE_OPTIONS="--max-old-space-size=4096" npm test -- --runInBand
```

### Development & Debugging

```bash
# Run specific test suite
npm test -- --testNamePattern="TasksService"

# Debug with open handles detection
npx jest --detectOpenHandles

# Verbose output for CI
npm test -- --runInBand --verbose --bail
```

## Common Anti-Patterns to Avoid

### Don't Do This

* Don't test private methods directly
* Don't create overly complex test setups
* Don't use real external services in unit tests
* Don't write tests that depend on specific timing
* Don't ignore async/await in test code
* Don't use `any` type in test code
* Don't create tests that test multiple behaviors

### Best Practices Instead

* Test public interfaces and observable behavior
* Keep test setup simple and focused
* Mock all external dependencies
* Use deterministic test data
* Properly handle all async operations
* Use proper TypeScript types
* Write focused, single-purpose tests

## Test Maintenance & Debugging

### Performance Optimization

* If a test consistently takes > 5s, profile and optimize
* Mock heavy dependencies
* Reduce test data size
* Remove unnecessary async waits
* Use `Promise.all` for parallel setup operations

### Debugging Failed Tests

* Use `--verbose` flag for detailed test output
* Use `console.log` sparingly for debugging
* Run tests in isolation to identify dependencies
* Use `.only` for focused development testing

### Continuous Improvement

* Update tests when refactoring code
* Remove obsolete tests when features are removed
* Keep test data up to date with schema changes
* Regularly review and improve test quality
* Use `--bail` in CI to fail fast on first error
