/**
 * Simple API test to verify Quick Task endpoints
 * Run with: npx ts-node src/tasks/__tests__/quick-task-api-simple.test.ts
 */

import { TasksController } from '../tasks.controller';
import { CreateQuickTaskDto, QuickTaskDestination } from '../dto/create-quick-task.dto';

// Mock services
const mockTasksService = {
  getTasks: () => Promise.resolve({ tasks: [], total: 0, count: 0, offset: 0, limit: 50, hasMore: false }),
  createTask: () => Promise.resolve({}),
  getTask: () => Promise.resolve({}),
  updateTask: () => Promise.resolve({}),
  deleteTask: () => Promise.resolve(),
  bulkUpdateTasks: () => Promise.resolve({}),
  searchTasks: () => Promise.resolve({ tasks: [], total: 0, count: 0, offset: 0, limit: 50, hasMore: false }),
  getWorkspacePrioritizationSettings: () => Promise.resolve({}),
  updateWorkspacePrioritizationSettings: () => Promise.resolve(),
  updateWorkspacePriorityScores: () => Promise.resolve(),
  updateTaskPriorityScore: () => Promise.resolve({}),
};

const mockPrioritizationService = {
  validateWeights: () => true,
  normalizeWeights: (weights: any) => weights,
};

const mockDailyPlanningService = {
  createOrUpdateDailyPlan: () => Promise.resolve({}),
  getDailyPlan: () => Promise.resolve({}),
  updateDailyPlan: () => Promise.resolve({}),
  completeDailyPlanTask: () => Promise.resolve({}),
  deleteDailyPlan: () => Promise.resolve(),
  getDailyPlansForRange: () => Promise.resolve([]),
};

const mockQuickTaskService = {
  createQuickTask: () => Promise.resolve({
    success: true,
    message: 'Quick task created successfully in personal inbox',
    task: {
      id: 'task-123',
      title: 'Test Quick Task',
      status: 'todo',
      priority: 'medium',
    },
    destination: QuickTaskDestination.PERSONAL_INBOX,
    addAnother: false,
  }),
  getQuickTaskPreferences: () => Promise.resolve({
    hasGoogleSheetsIntegration: false,
    defaultDestination: QuickTaskDestination.PERSONAL_INBOX,
    keyboardShortcuts: {
      openQuickTask: 'Ctrl+Shift+T',
      submitAndClose: 'Ctrl+Enter',
      submitAndAddAnother: 'Ctrl+Shift+Enter',
    },
  }),
};

async function testQuickTaskAPI() {
  console.log('🧪 Quick Task API Simple Test');
  console.log('==============================');

  try {
    // Test 1: Controller instantiation
    console.log('✅ Test 1: Controller can be instantiated');
    const controller = new TasksController(
      mockTasksService as any,
      mockPrioritizationService as any,
      mockDailyPlanningService as any,
      mockQuickTaskService as any,
      {} as any, // progressTrackingService
      {} as any, // reportExportService
    );
    console.log('✅ TasksController instantiated successfully');

    // Test 2: Quick task creation endpoint
    console.log('✅ Test 2: Quick task creation endpoint');
    const createQuickTaskDto: CreateQuickTaskDto = {
      title: 'Test Quick Task',
      description: 'Test description',
      dueDate: '2024-12-31T23:59:59Z',
    };

    const result = await controller.createQuickTask('workspace-123', createQuickTaskDto);
    console.log('✅ createQuickTask endpoint works');
    console.log(`   Result: ${result.success ? 'Success' : 'Failed'}`);
    console.log(`   Message: ${result.message}`);

    // Test 3: Quick task preferences endpoint
    console.log('✅ Test 3: Quick task preferences endpoint');
    const preferences = await controller.getQuickTaskPreferences('workspace-123');
    console.log('✅ getQuickTaskPreferences endpoint works');
    console.log(`   Has Google Sheets: ${preferences.hasGoogleSheetsIntegration}`);
    console.log(`   Default destination: ${preferences.defaultDestination}`);
    console.log(`   Keyboard shortcuts: ${JSON.stringify(preferences.keyboardShortcuts)}`);

    // Test 4: Method signatures
    console.log('✅ Test 4: Method signatures validation');
    console.log(`createQuickTask method exists: ${typeof controller.createQuickTask === 'function'}`);
    console.log(`getQuickTaskPreferences method exists: ${typeof controller.getQuickTaskPreferences === 'function'}`);

    console.log('\n🎉 All API tests passed!');
    console.log('The Quick Task API endpoints are working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testQuickTaskAPI().catch(console.error);