export interface ProgressReportFilters {
  startDate: Date;
  endDate: Date;
  source?: string;
  project?: string;
  assignee?: string;
}

export interface CompletionRateReport {
  overall: {
    totalTasks: number;
    completedTasks: number;
    completionRate: number;
  };
  bySource: {
    source: string;
    sourceName: string;
    totalTasks: number;
    completedTasks: number;
    completionRate: number;
  }[];
  byProject: {
    project: string;
    totalTasks: number;
    completedTasks: number;
    completionRate: number;
  }[];
  byTimePeriod: ReportTimeRange[];
  filters: ProgressReportFilters;
  generatedAt: Date;
}

export interface ReportTimeRange {
  period: string;
  startDate: Date;
  endDate: Date;
  totalTasks: number;
  completedTasks: number;
  completionRate: number;
}

export interface AgeRange {
  label: string;
  minDays: number;
  maxDays: number | null;
}

export interface TaskAgingReport {
  totalActiveTasks: number;
  tasksByAgeRange: {
    ageRange: AgeRange;
    taskCount: number;
    tasks: {
      id: string;
      title: string;
      ageInDays: number;
      dueDate: Date | null;
      status: string;
      project: string | null;
      assignee: string | null;
      source: string;
      sourceName: string;
    }[];
  }[];
  filters: ProgressReportFilters;
  generatedAt: Date;
}

export interface VelocityReport {
  weeklyData: {
    weekStart: string;
    weekEnd: string;
    tasksCompleted: number;
    estimatedMinutes: number;
  }[];
  averageTasksPerWeek: number;
  trendAnalysis: {
    direction: 'increasing' | 'decreasing' | 'stable';
    percentage: number;
    recentAverage: number;
    earlierAverage: number;
  };
  velocityBySource: {
    source: string;
    sourceName: string;
    totalTasksCompleted: number;
    totalEstimatedMinutes: number;
    averageTasksPerWeek: number;
  }[];
  filters: ProgressReportFilters;
  generatedAt: Date;
}

export interface ReportFilterOptions {
  sources: { value: string; label: string }[];
  projects: { value: string; label: string }[];
  assignees: { value: string; label: string }[];
}

export interface ExportFormat {
  format: 'csv' | 'pdf';
  reportType: 'completion' | 'aging' | 'velocity';
}