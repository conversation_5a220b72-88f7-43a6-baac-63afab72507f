import { AsanaAdapter } from '../asana.adapter';
import {
  IntegrationConfig,
  OAuthCredentials,
  TaskStatus,
  TaskPriority,
  CreateTaskRequest,
  TaskUpdate,
} from '../../types';

/**
 * Example usage of the Asana adapter
 * This file demonstrates how to use the AsanaAdapter in practice
 */

async function demonstrateAsanaAdapter() {
  console.log('🚀 Asana Adapter Example');
  console.log('========================');

  // Initialize the adapter
  const adapter = new AsanaAdapter();

  // Example credentials (replace with real ones for testing)
  const credentials: OAuthCredentials = {
    accessToken: 'your-asana-access-token-here',
    refreshToken: 'your-refresh-token-here',
    expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
    userId: 'asana-user-id',
    userEmail: '<EMAIL>',
  };

  // Example configuration
  const config: IntegrationConfig = {
    syncInterval: 15, // 15 minutes
    enableTwoWaySync: true,
    fieldMappings: [
      { localField: 'title', externalField: 'name' },
      { localField: 'description', externalField: 'notes' },
      { localField: 'status', externalField: 'completed' },
      { localField: 'dueDate', externalField: 'due_date' },
      { localField: 'assigneeId', externalField: 'assignee.gid' },
    ],
    filters: [
      {
        field: 'status',
        operator: 'in',
        value: [TaskStatus.TODO, TaskStatus.IN_PROGRESS],
      },
    ],
    customSettings: {
      credentials,
      workspaceIds: ['your-workspace-id'], // Optional: specific workspaces
      projectIds: ['your-project-id'], // Optional: specific projects
    },
  };

  try {
    console.log('\n1. Testing Authentication');
    console.log('-------------------------');
    
    // Test authentication
    const authResult = await adapter.authenticate(credentials);
    console.log('Authentication result:', {
      success: authResult.success,
      user: authResult.user,
      error: authResult.error,
    });

    if (!authResult.success) {
      console.log('❌ Authentication failed. Please check your credentials.');
      return;
    }

    console.log('✅ Authentication successful!');

    console.log('\n2. Testing Credential Validation');
    console.log('--------------------------------');
    
    // Test credential validation
    const isValid = await adapter.validateCredentials(credentials);
    console.log('Credentials valid:', isValid);

    console.log('\n3. Testing Task Fetching');
    console.log('------------------------');
    
    // Fetch tasks
    const tasks = await adapter.fetchTasks(config);
    console.log(`Fetched ${tasks.length} tasks`);
    
    if (tasks.length > 0) {
      console.log('Sample task:', {
        id: tasks[0].id,
        title: tasks[0].title,
        status: tasks[0].status,
        priority: tasks[0].priority,
        assigneeName: tasks[0].assigneeName,
        projectName: tasks[0].projectName,
        dueDate: tasks[0].dueDate,
        sourceUrl: tasks[0].sourceUrl,
      });
    }

    console.log('\n4. Testing Task Creation');
    console.log('------------------------');
    
    // Create a new task
    const newTaskRequest: CreateTaskRequest = {
      title: 'Test Task from TaskUnify',
      description: 'This task was created via the TaskUnify Asana adapter',
      priority: TaskPriority.MEDIUM,
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
      projectId: 'your-project-id', // Replace with actual project ID
    };

    const createdTask = await adapter.createTask(newTaskRequest);
    console.log('Created task:', {
      id: createdTask.id,
      title: createdTask.title,
      status: createdTask.status,
      sourceUrl: createdTask.sourceUrl,
    });

    console.log('\n5. Testing Task Update');
    console.log('---------------------');
    
    // Update the created task
    const taskUpdate: TaskUpdate = {
      description: 'Updated description via TaskUnify',
      status: TaskStatus.IN_PROGRESS,
    };

    const updatedTask = await adapter.updateTask(createdTask.id, taskUpdate);
    console.log('Updated task:', {
      id: updatedTask.id,
      title: updatedTask.title,
      description: updatedTask.description,
      status: updatedTask.status,
    });

    console.log('\n6. Testing Webhook Setup');
    console.log('------------------------');
    
    // Set up webhook (optional)
    try {
      const webhookConfig = await adapter.setupWebhook('https://your-app.com/webhooks/asana');
      console.log('Webhook setup successful:', {
        id: webhookConfig.id,
        url: webhookConfig.url,
        events: webhookConfig.events,
        active: webhookConfig.active,
      });
    } catch (error) {
      console.log('Webhook setup failed (this is optional):', error.message);
    }

    console.log('\n✅ All tests completed successfully!');

  } catch (error) {
    console.error('❌ Error during testing:', error);
    
    if (error.response) {
      console.error('API Response:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      });
    }
  }
}

/**
 * Adapter capabilities and features
 */
function showAdapterCapabilities() {
  console.log('\n📋 Asana Adapter Capabilities');
  console.log('=============================');
  
  const adapter = new AsanaAdapter();
  
  console.log('Provider:', adapter.getProvider());
  console.log('Two-way sync supported:', adapter.supportsTwoWaySync());
  console.log('Webhooks supported:', adapter.supportsWebhooks());
  
  console.log('\nSupported Operations:');
  console.log('• Authentication via OAuth');
  console.log('• Fetch tasks from workspaces and projects');
  console.log('• Create new tasks');
  console.log('• Update existing tasks (title, description, status, due date, assignee)');
  console.log('• Set up webhooks for real-time updates');
  console.log('• Validate credentials');
  
  console.log('\nTask Field Mapping:');
  console.log('• Title ↔ Asana task name');
  console.log('• Description ↔ Asana task notes');
  console.log('• Status ↔ Asana completed flag');
  console.log('• Priority ↔ Asana custom fields');
  console.log('• Due Date ↔ Asana due_date');
  console.log('• Assignee ↔ Asana assignee');
  console.log('• Tags ↔ Asana tags');
  console.log('• Project ↔ Asana project');
  console.log('• Estimated Time ↔ Asana custom fields');
  
  console.log('\nError Handling:');
  console.log('• Authentication errors (401, 403)');
  console.log('• Rate limiting (429) with retry-after');
  console.log('• Network errors with retry logic');
  console.log('• Validation errors (400)');
  console.log('• Server errors (500+) with retry');
}

/**
 * Configuration examples
 */
function showConfigurationExamples() {
  console.log('\n⚙️ Configuration Examples');
  console.log('=========================');
  
  console.log('\n1. Basic Configuration:');
  console.log(JSON.stringify({
    syncInterval: 15,
    enableTwoWaySync: true,
    fieldMappings: [],
    filters: [],
    customSettings: {
      credentials: {
        accessToken: 'your-token',
        refreshToken: 'your-refresh-token',
      },
    },
  }, null, 2));
  
  console.log('\n2. Workspace-Specific Configuration:');
  console.log(JSON.stringify({
    syncInterval: 30,
    enableTwoWaySync: true,
    fieldMappings: [],
    filters: [],
    customSettings: {
      credentials: { /* ... */ },
      workspaceIds: ['workspace-1', 'workspace-2'],
    },
  }, null, 2));
  
  console.log('\n3. Project-Specific Configuration:');
  console.log(JSON.stringify({
    syncInterval: 10,
    enableTwoWaySync: true,
    fieldMappings: [],
    filters: [
      {
        field: 'status',
        operator: 'in',
        value: ['todo', 'in_progress'],
      },
    ],
    customSettings: {
      credentials: { /* ... */ },
      projectIds: ['project-1', 'project-2'],
    },
  }, null, 2));
}

// Run the example if this file is executed directly
if (require.main === module) {
  showAdapterCapabilities();
  showConfigurationExamples();
  
  console.log('\n🔧 To run the live demo:');
  console.log('1. Replace the placeholder credentials with real Asana OAuth tokens');
  console.log('2. Replace workspace and project IDs with real ones');
  console.log('3. Uncomment the line below and run this file');
  
  // demonstrateAsanaAdapter();
}

export { demonstrateAsanaAdapter, showAdapterCapabilities, showConfigurationExamples };