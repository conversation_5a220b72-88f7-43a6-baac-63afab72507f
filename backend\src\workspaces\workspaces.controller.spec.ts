import { Test, TestingModule } from '@nestjs/testing';
import { WorkspacesController } from './workspaces.controller';
import { WorkspacesService } from './workspaces.service';
import { CreateWorkspaceDto } from './dto/create-workspace.dto';
import { UpdateWorkspaceDto } from './dto/update-workspace.dto';
import { InviteMemberDto } from './dto/invite-member.dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import { WorkspaceRole } from './entities/workspace.entity';

describe('WorkspacesController', () => {
  let controller: WorkspacesController;
  let mockWorkspacesService: {
    create: jest.Mock;
    findAll: jest.Mock;
    findOne: jest.Mock;
    update: jest.Mock;
    remove: jest.Mock;
    inviteMember: jest.Mock;
    getMembers: jest.Mock;
    updateMember: jest.Mock;
    removeMember: jest.Mock;
    leaveWorkspace: jest.Mock;
    transferOwnership: jest.Mock;
  };

  const mockUser = { id: 'user-1', email: '<EMAIL>' };
  const mockRequest = { user: mockUser };

  const mockWorkspace = {
    id: 'workspace-1',
    name: 'Test Workspace',
    slug: 'test-workspace',
    ownerId: 'user-1',
    settings: {},
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockWorkspaceWithMembers = {
    ...mockWorkspace,
    members: [],
    memberCount: 1,
  };

  const mockWorkspaceMember = {
    id: 'member-1',
    userId: 'user-2',
    workspaceId: 'workspace-1',
    role: WorkspaceRole.MEMBER,
    permissions: [],
    joinedAt: new Date(),
  };

  beforeEach(async () => {
    mockWorkspacesService = {
      create: jest.fn(),
      findAll: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
      inviteMember: jest.fn(),
      getMembers: jest.fn(),
      updateMember: jest.fn(),
      removeMember: jest.fn(),
      leaveWorkspace: jest.fn(),
      transferOwnership: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkspacesController],
      providers: [
        {
          provide: WorkspacesService,
          useValue: mockWorkspacesService,
        },
      ],
    }).compile();

    controller = module.get<WorkspacesController>(WorkspacesController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a workspace', async () => {
      const createWorkspaceDto: CreateWorkspaceDto = {
        name: 'Test Workspace',
        slug: 'test-workspace',
      };

      mockWorkspacesService.create.mockResolvedValue(mockWorkspace);

      const result = await controller.create(createWorkspaceDto, mockRequest);

      expect(mockWorkspacesService.create).toHaveBeenCalledWith(
        createWorkspaceDto,
        mockUser.id,
      );
      expect(result).toEqual(mockWorkspace);
    });
  });

  describe('findAll', () => {
    it('should return all workspaces for user', async () => {
      const workspaces = [mockWorkspaceWithMembers];
      mockWorkspacesService.findAll.mockResolvedValue(workspaces);

      const result = await controller.findAll(mockRequest);

      expect(mockWorkspacesService.findAll).toHaveBeenCalledWith(mockUser.id);
      expect(result).toEqual(workspaces);
    });
  });

  describe('findOne', () => {
    it('should return a specific workspace', async () => {
      mockWorkspacesService.findOne.mockResolvedValue(mockWorkspaceWithMembers);

      const result = await controller.findOne('workspace-1', mockRequest);

      expect(mockWorkspacesService.findOne).toHaveBeenCalledWith('workspace-1', mockUser.id);
      expect(result).toEqual(mockWorkspaceWithMembers);
    });
  });

  describe('update', () => {
    it('should update a workspace', async () => {
      const updateWorkspaceDto: UpdateWorkspaceDto = {
        name: 'Updated Workspace',
      };

      const updatedWorkspace = { ...mockWorkspace, name: 'Updated Workspace' };
      mockWorkspacesService.update.mockResolvedValue(updatedWorkspace);

      const result = await controller.update('workspace-1', updateWorkspaceDto, mockRequest);

      expect(mockWorkspacesService.update).toHaveBeenCalledWith(
        'workspace-1',
        updateWorkspaceDto,
        mockUser.id,
      );
      expect(result).toEqual(updatedWorkspace);
    });
  });

  describe('remove', () => {
    it('should delete a workspace', async () => {
      mockWorkspacesService.remove.mockResolvedValue(undefined);

      const result = await controller.remove('workspace-1', mockRequest);

      expect(mockWorkspacesService.remove).toHaveBeenCalledWith('workspace-1', mockUser.id);
      expect(result).toBeUndefined();
    });
  });

  describe('inviteMember', () => {
    it('should invite a member to workspace', async () => {
      const inviteMemberDto: InviteMemberDto = {
        email: '<EMAIL>',
        role: WorkspaceRole.MEMBER,
      };

      mockWorkspacesService.inviteMember.mockResolvedValue(mockWorkspaceMember);

      const result = await controller.inviteMember('workspace-1', inviteMemberDto, mockRequest);

      expect(mockWorkspacesService.inviteMember).toHaveBeenCalledWith(
        'workspace-1',
        inviteMemberDto,
        mockUser.id,
      );
      expect(result).toEqual(mockWorkspaceMember);
    });
  });

  describe('getMembers', () => {
    it('should return workspace members', async () => {
      const members = [mockWorkspaceMember];
      mockWorkspacesService.getMembers.mockResolvedValue(members);

      const result = await controller.getMembers('workspace-1', mockRequest);

      expect(mockWorkspacesService.getMembers).toHaveBeenCalledWith('workspace-1', mockUser.id);
      expect(result).toEqual(members);
    });
  });

  describe('updateMember', () => {
    it('should update a workspace member', async () => {
      const updateMemberDto: UpdateMemberDto = {
        role: WorkspaceRole.ADMIN,
      };

      const updatedMember = { ...mockWorkspaceMember, role: WorkspaceRole.ADMIN };
      mockWorkspacesService.updateMember.mockResolvedValue(updatedMember);

      const result = await controller.updateMember(
        'workspace-1',
        'member-1',
        updateMemberDto,
        mockRequest,
      );

      expect(mockWorkspacesService.updateMember).toHaveBeenCalledWith(
        'workspace-1',
        'member-1',
        updateMemberDto,
        mockUser.id,
      );
      expect(result).toEqual(updatedMember);
    });
  });

  describe('removeMember', () => {
    it('should remove a workspace member', async () => {
      mockWorkspacesService.removeMember.mockResolvedValue(undefined);

      const result = await controller.removeMember('workspace-1', 'member-1', mockRequest);

      expect(mockWorkspacesService.removeMember).toHaveBeenCalledWith(
        'workspace-1',
        'member-1',
        mockUser.id,
      );
      expect(result).toBeUndefined();
    });
  });

  describe('leaveWorkspace', () => {
    it('should allow user to leave workspace', async () => {
      mockWorkspacesService.leaveWorkspace.mockResolvedValue(undefined);

      const result = await controller.leaveWorkspace('workspace-1', mockRequest);

      expect(mockWorkspacesService.leaveWorkspace).toHaveBeenCalledWith('workspace-1', mockUser.id);
      expect(result).toBeUndefined();
    });
  });

  describe('transferOwnership', () => {
    it('should transfer workspace ownership', async () => {
      const updatedWorkspace = { ...mockWorkspace, ownerId: 'new-owner-1' };
      mockWorkspacesService.transferOwnership.mockResolvedValue(updatedWorkspace);

      const result = await controller.transferOwnership('workspace-1', 'new-owner-1', mockRequest);

      expect(mockWorkspacesService.transferOwnership).toHaveBeenCalledWith(
        'workspace-1',
        'new-owner-1',
        mockUser.id,
      );
      expect(result).toEqual(updatedWorkspace);
    });
  });
});