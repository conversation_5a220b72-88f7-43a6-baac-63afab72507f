import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function setupDevelopment() {
  console.log('🚀 Setting up TaskUnify development environment...\n');

  try {
    // Step 1: Check if .env exists
    if (!existsSync('.env')) {
      console.log('📝 Creating .env file from .env.example...');
      execSync('copy .env.example .env', { stdio: 'inherit' });
      console.log('✅ .env file created\n');
      console.log('⚠️  Please update the .env file with your actual configuration values\n');
    } else {
      console.log('✅ .env file already exists\n');
    }

    // Step 2: Install dependencies
    console.log('📦 Installing dependencies...');
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ Dependencies installed\n');

    // Step 3: Generate Prisma client
    console.log('🔧 Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    console.log('✅ Prisma client generated\n');

    // Step 4: Check database connection
    console.log('🔍 Checking database connection...');
    try {
      await prisma.$connect();
      console.log('✅ Database connection successful\n');

      // Step 5: Run migrations
      console.log('🗄️  Running database migrations...');
      execSync('npx prisma migrate deploy', { stdio: 'inherit' });
      console.log('✅ Database migrations completed\n');

      // Step 6: Check if database has data
      const userCount = await prisma.user.count();
      if (userCount === 0) {
        console.log('🌱 Seeding database with test data...');
        execSync('npm run prisma:seed', { stdio: 'inherit' });
        console.log('✅ Database seeded with test data\n');
      } else {
        console.log(`✅ Database already has ${userCount} users\n`);
      }

      // Step 7: Verify setup
      console.log('🔍 Verifying setup...');
      execSync('npm run db:verify', { stdio: 'inherit' });

    } catch (dbError) {
      console.log('❌ Database connection failed');
      console.log('💡 Please check your Neon connection string in .env file');
      console.log('   1. Visit https://console.neon.tech');
      console.log('   2. Get your connection string');
      console.log('   3. Update DATABASE_URL and DIRECT_URL in .env');
      console.log('   4. Run this setup script again\n');
    }

    console.log('🎉 Development environment setup completed!\n');
    console.log('📚 Next steps:');
    console.log('   1. Update .env with your Neon connection string');
    console.log('   2. Start the development server: npm run dev');
    console.log('   3. Open Prisma Studio: npm run prisma:studio');
    console.log('   4. View API docs: http://localhost:3001/api\n');
    console.log('💡 To get your Neon connection string:');
    console.log('   - Visit https://console.neon.tech');
    console.log('   - Create a new project or use existing one');
    console.log('   - Copy the connection string from the dashboard\n');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

setupDevelopment();