import { Test, TestingModule } from '@nestjs/testing';
import { QuickTaskService } from '../services/quick-task.service';
import { DatabaseService } from '../../database/database.service';
import { IntegrationService } from '../../integrations/services/integration.service';
import { TasksService } from '../services/tasks.service';
import { CreateQuickTaskDto, QuickTaskDestination } from '../dto/create-quick-task.dto';
import { IntegrationProvider } from '../../integrations/types';

describe('QuickTask Google Sheets Integration', () => {
  let service: QuickTaskService;
  let databaseService: jest.Mocked<DatabaseService>;
  let integrationService: jest.Mocked<IntegrationService>;
  let tasksService: jest.Mocked<TasksService>;

  const mockWorkspaceId = 'workspace-123';
  const mockUserId = 'user-123';

  const mockGoogleSheetsIntegration = {
    id: 'integration-123',
    workspaceId: mockWorkspaceId,
    provider: IntegrationProvider.GOOGLE_SHEETS,
    name: 'Google Sheets Integration',
    config: {
      syncInterval: 15,
      enableTwoWaySync: true,
      fieldMappings: [],
      filters: [],
      customSettings: {
        spreadsheetId: 'test-spreadsheet-id',
        sheetName: 'Tasks',
      },
    },
    encryptedCredentials: 'encrypted-creds',
    status: 'ACTIVE' as const,
    lastSyncAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockExternalTask = {
    id: 'gs_test-spreadsheet-id_Tasks_5',
    title: 'Test Quick Task',
    description: 'Test description',
    status: 'todo' as const,
    priority: 'medium' as const,
    dueDate: new Date('2024-12-31'),
    assigneeId: mockUserId,
    assigneeName: null,
    tags: ['quick-task'],
    projectName: 'Quick Tasks',
    sourceUrl: 'https://docs.google.com/spreadsheets/d/test-spreadsheet-id/edit#gid=0',
    metadata: {
      spreadsheetId: 'test-spreadsheet-id',
      sheetName: 'Tasks',
      rowIndex: 5,
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockCreatedTask = {
    id: 'task-123',
    workspaceId: mockWorkspaceId,
    integrationId: mockGoogleSheetsIntegration.id,
    externalId: mockExternalTask.id,
    title: 'Test Quick Task',
    description: 'Test description',
    status: 'todo',
    priority: 'medium',
    assigneeId: mockUserId,
    assigneeName: null,
    dueDate: new Date('2024-12-31'),
    estimatedMinutes: null,
    tags: ['quick-task'],
    projectName: 'Quick Tasks',
    sourceUrl: mockExternalTask.sourceUrl,
    metadata: {},
    syncStatus: 'synced',
    priorityScore: 50.0,
    createdAt: new Date(),
    updatedAt: new Date(),
    lastSyncAt: new Date(),
  };

  beforeEach(async () => {
    const mockDatabaseService = {
      integration: {
        findFirst: jest.fn(),
      },
      task: {
        create: jest.fn(),
      },
    };

    const mockIntegrationService = {
      getAdapter: jest.fn(),
    };

    const mockTasksService = {
      getWorkspacePrioritizationSettings: jest.fn(),
      updateTaskPriorityScore: jest.fn(),
      getTask: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuickTaskService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
        {
          provide: IntegrationService,
          useValue: mockIntegrationService,
        },
        {
          provide: TasksService,
          useValue: mockTasksService,
        },
      ],
    }).compile();

    service = module.get<QuickTaskService>(QuickTaskService);
    databaseService = module.get(DatabaseService);
    integrationService = module.get(IntegrationService);
    tasksService = module.get(TasksService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Google Sheets Integration Flow', () => {
    it('should complete full Google Sheets quick task creation flow', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
        description: 'Test description',
        dueDate: '2024-12-31T23:59:59Z',
        destination: QuickTaskDestination.GOOGLE_SHEETS,
      };

      const mockAdapter = {
        createTask: jest.fn().mockResolvedValue(mockExternalTask),
      };

      // Setup mocks
      databaseService.integration.findFirst.mockResolvedValue(mockGoogleSheetsIntegration);
      integrationService.getAdapter.mockReturnValue(mockAdapter);
      databaseService.task.create.mockResolvedValue(mockCreatedTask);
      tasksService.getWorkspacePrioritizationSettings.mockResolvedValue({
        weights: {
          dueDateProximity: 0.3,
          effortEstimate: 0.2,
          businessImpact: 0.3,
          contextSwitching: 0.2,
        },
      });
      tasksService.updateTaskPriorityScore.mockResolvedValue(mockCreatedTask);
      tasksService.getTask.mockResolvedValue(mockCreatedTask);

      const result = await service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto);

      // Verify integration lookup
      expect(databaseService.integration.findFirst).toHaveBeenCalledWith({
        where: {
          workspaceId: mockWorkspaceId,
          provider: IntegrationProvider.GOOGLE_SHEETS,
          status: 'ACTIVE',
        },
      });

      // Verify adapter usage
      expect(integrationService.getAdapter).toHaveBeenCalledWith(IntegrationProvider.GOOGLE_SHEETS);
      expect(mockAdapter.createTask).toHaveBeenCalledWith({
        title: createQuickTaskDto.title,
        description: createQuickTaskDto.description,
        status: 'todo',
        priority: 'medium',
        dueDate: new Date(createQuickTaskDto.dueDate),
        assigneeId: mockUserId,
        tags: ['quick-task'],
        projectName: 'Quick Tasks',
      });

      // Verify task creation in database
      expect(databaseService.task.create).toHaveBeenCalledWith({
        data: {
          workspaceId: mockWorkspaceId,
          integrationId: mockGoogleSheetsIntegration.id,
          externalId: mockExternalTask.id,
          title: createQuickTaskDto.title,
          description: createQuickTaskDto.description,
          status: 'todo',
          priority: 'medium',
          assigneeId: mockUserId,
          assigneeName: undefined,
          dueDate: new Date(createQuickTaskDto.dueDate),
          estimatedMinutes: undefined,
          tags: ['quick-task'],
          projectName: 'Quick Tasks',
          sourceUrl: mockExternalTask.sourceUrl,
          syncStatus: 'synced',
        },
      });

      // Verify priority score calculation
      expect(tasksService.getWorkspacePrioritizationSettings).toHaveBeenCalledWith(mockWorkspaceId);
      expect(tasksService.updateTaskPriorityScore).toHaveBeenCalledWith(
        mockCreatedTask.id,
        expect.any(Object)
      );

      // Verify response
      expect(result.success).toBe(true);
      expect(result.message).toBe('Quick task created successfully in Google Sheets');
      expect(result.task).toEqual(mockCreatedTask);
      expect(result.destination).toBe(QuickTaskDestination.GOOGLE_SHEETS);
      expect(result.externalUrl).toBe(mockExternalTask.sourceUrl);
    });

    it('should handle Google Sheets API errors gracefully', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
        destination: QuickTaskDestination.GOOGLE_SHEETS,
      };

      const mockAdapter = {
        createTask: jest.fn().mockRejectedValue(new Error('Google Sheets API rate limit exceeded')),
      };

      databaseService.integration.findFirst.mockResolvedValue(mockGoogleSheetsIntegration);
      integrationService.getAdapter.mockReturnValue(mockAdapter);

      await expect(
        service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto)
      ).rejects.toThrow('Failed to create quick task: Google Sheets API rate limit exceeded');

      expect(mockAdapter.createTask).toHaveBeenCalled();
      expect(databaseService.task.create).not.toHaveBeenCalled();
    });

    it('should handle missing Google Sheets configuration', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
        destination: QuickTaskDestination.GOOGLE_SHEETS,
      };

      const mockAdapter = {
        createTask: jest.fn().mockRejectedValue(new Error('Spreadsheet ID is required in configuration')),
      };

      databaseService.integration.findFirst.mockResolvedValue(mockGoogleSheetsIntegration);
      integrationService.getAdapter.mockReturnValue(mockAdapter);

      await expect(
        service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto)
      ).rejects.toThrow('Failed to create quick task: Spreadsheet ID is required in configuration');
    });

    it('should handle authentication errors', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
        destination: QuickTaskDestination.GOOGLE_SHEETS,
      };

      const mockAdapter = {
        createTask: jest.fn().mockRejectedValue(new Error('Authentication failed: Invalid credentials')),
      };

      databaseService.integration.findFirst.mockResolvedValue(mockGoogleSheetsIntegration);
      integrationService.getAdapter.mockReturnValue(mockAdapter);

      await expect(
        service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto)
      ).rejects.toThrow('Failed to create quick task: Authentication failed: Invalid credentials');
    });

    it('should sync task within 30 seconds requirement', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
        destination: QuickTaskDestination.GOOGLE_SHEETS,
      };

      const mockAdapter = {
        createTask: jest.fn().mockImplementation(() => {
          // Simulate API call that takes some time but less than 30 seconds
          return new Promise(resolve => {
            setTimeout(() => resolve(mockExternalTask), 100); // 100ms delay
          });
        }),
      };

      databaseService.integration.findFirst.mockResolvedValue(mockGoogleSheetsIntegration);
      integrationService.getAdapter.mockReturnValue(mockAdapter);
      databaseService.task.create.mockResolvedValue(mockCreatedTask);
      tasksService.getWorkspacePrioritizationSettings.mockResolvedValue({});
      tasksService.updateTaskPriorityScore.mockResolvedValue(mockCreatedTask);
      tasksService.getTask.mockResolvedValue(mockCreatedTask);

      const startTime = Date.now();
      const result = await service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(30000); // Less than 30 seconds
      expect(result.success).toBe(true);
    });
  });

  describe('Task Metadata and Tracking', () => {
    it('should properly track task source and metadata', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task with Metadata',
        description: 'Task with rich metadata',
        destination: QuickTaskDestination.GOOGLE_SHEETS,
      };

      const mockAdapter = {
        createTask: jest.fn().mockResolvedValue(mockExternalTask),
      };

      databaseService.integration.findFirst.mockResolvedValue(mockGoogleSheetsIntegration);
      integrationService.getAdapter.mockReturnValue(mockAdapter);
      databaseService.task.create.mockResolvedValue(mockCreatedTask);
      tasksService.getWorkspacePrioritizationSettings.mockResolvedValue({});
      tasksService.updateTaskPriorityScore.mockResolvedValue(mockCreatedTask);
      tasksService.getTask.mockResolvedValue(mockCreatedTask);

      const result = await service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto);

      expect(result.task.sourceUrl).toContain('docs.google.com/spreadsheets');
      expect(result.task.externalId).toMatch(/^gs_/);
      expect(result.task.tags).toContain('quick-task');
      expect(result.task.projectName).toBe('Quick Tasks');
      expect(result.task.syncStatus).toBe('synced');
    });

    it('should generate proper external task ID format', async () => {
      const createQuickTaskDto: CreateQuickTaskDto = {
        title: 'Test Quick Task',
        destination: QuickTaskDestination.GOOGLE_SHEETS,
      };

      const mockAdapter = {
        createTask: jest.fn().mockResolvedValue(mockExternalTask),
      };

      databaseService.integration.findFirst.mockResolvedValue(mockGoogleSheetsIntegration);
      integrationService.getAdapter.mockReturnValue(mockAdapter);
      databaseService.task.create.mockResolvedValue(mockCreatedTask);
      tasksService.getWorkspacePrioritizationSettings.mockResolvedValue({});
      tasksService.updateTaskPriorityScore.mockResolvedValue(mockCreatedTask);
      tasksService.getTask.mockResolvedValue(mockCreatedTask);

      await service.createQuickTask(mockWorkspaceId, mockUserId, createQuickTaskDto);

      expect(databaseService.task.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          externalId: 'gs_test-spreadsheet-id_Tasks_5',
        }),
      });
    });
  });
});