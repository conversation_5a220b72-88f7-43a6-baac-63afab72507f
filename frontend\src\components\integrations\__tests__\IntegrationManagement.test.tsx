import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { IntegrationManagement } from '../IntegrationManagement'
import { IntegrationService } from '@/services/integration'
import { useWorkspaceStore } from '@/store/workspace'
import { IntegrationProvider, IntegrationStatus, IntegrationStatusInfo } from '@/types/api'

// Mock dependencies
vi.mock('@/services/integration')
vi.mock('@/store/workspace')
vi.mock('react-hot-toast', () => ({
  default: {
    error: vi.fn(),
    success: vi.fn()
  }
}))

const mockIntegrationService = IntegrationService as any
const mockUseWorkspaceStore = useWorkspaceStore as any

const mockIntegrations = [
  {
    id: 'integration-1',
    workspaceId: 'workspace-1',
    provider: IntegrationProvider.ASANA,
    name: 'My Asana Integration',
    config: {
      syncInterval: 30,
      enableTwoWaySync: true,
      fieldMappings: [],
      filters: []
    },
    status: IntegrationStatus.ACTIVE,
    lastSyncAt: '2024-01-01T12:00:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'integration-2',
    workspaceId: 'workspace-1',
    provider: IntegrationProvider.TRELLO,
    name: 'My Trello Integration',
    config: {
      syncInterval: 60,
      enableTwoWaySync: false,
      fieldMappings: [],
      filters: []
    },
    status: IntegrationStatus.ERROR,
    lastSyncAt: '2024-01-01T11:00:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
]

const mockIntegrationStatuses: Record<string, IntegrationStatusInfo> = {
  'integration-1': {
    id: 'integration-1',
    status: IntegrationStatus.ACTIVE,
    lastSyncAt: '2024-01-01T12:00:00Z',
    lastSyncResult: {
      success: true,
      tasksProcessed: 15,
      tasksCreated: 5,
      tasksUpdated: 8,
      tasksDeleted: 2,
      errors: [],
      conflicts: [],
      duration: 2000
    },
    isHealthy: true,
    errorCount: 0
  },
  'integration-2': {
    id: 'integration-2',
    status: IntegrationStatus.ERROR,
    lastSyncAt: '2024-01-01T11:00:00Z',
    lastSyncResult: {
      success: false,
      tasksProcessed: 0,
      tasksCreated: 0,
      tasksUpdated: 0,
      tasksDeleted: 0,
      errors: [
        {
          type: 'AUTH_ERROR',
          message: 'Invalid credentials',
          retryable: false,
          timestamp: '2024-01-01T11:00:00Z'
        }
      ],
      conflicts: [],
      duration: 500
    },
    isHealthy: false,
    errorCount: 1
  }
}

describe('IntegrationManagement', () => {
  const mockOnEditIntegration = vi.fn()
  const mockOnDeleteIntegration = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    
    mockUseWorkspaceStore.mockReturnValue({
      currentWorkspace: {
        id: 'workspace-1',
        name: 'Test Workspace'
      }
    })

    mockIntegrationService.getWorkspaceIntegrations.mockResolvedValue(mockIntegrations)
    mockIntegrationService.getIntegrationStatus.mockImplementation((workspaceId, integrationId) => {
      return Promise.resolve(mockIntegrationStatuses[integrationId])
    })
  })

  it('should render integrations list', async () => {
    render(
      <IntegrationManagement
        onEditIntegration={mockOnEditIntegration}
        onDeleteIntegration={mockOnDeleteIntegration}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('My Asana Integration')).toBeInTheDocument()
    })

    expect(screen.getByText('My Asana Integration')).toBeInTheDocument()
    expect(screen.getByText('My Trello Integration')).toBeInTheDocument()
    expect(screen.getByText('Asana')).toBeInTheDocument()
    expect(screen.getByText('Trello')).toBeInTheDocument()
  })

  it('should show loading state initially', () => {
    mockIntegrationService.getWorkspaceIntegrations.mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 1000))
    )

    render(
      <IntegrationManagement
        onEditIntegration={mockOnEditIntegration}
        onDeleteIntegration={mockOnDeleteIntegration}
      />
    )

    expect(screen.getAllByRole('status')).toHaveLength(3) // 3 skeleton cards
  })

  it('should show empty state when no integrations exist', async () => {
    mockIntegrationService.getWorkspaceIntegrations.mockResolvedValue([])

    render(
      <IntegrationManagement
        onEditIntegration={mockOnEditIntegration}
        onDeleteIntegration={mockOnDeleteIntegration}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('No integrations yet')).toBeInTheDocument()
    })

    expect(screen.getByText('Connect your favorite tools to start aggregating tasks')).toBeInTheDocument()
  })

  it('should display integration status badges correctly', async () => {
    render(
      <IntegrationManagement
        onEditIntegration={mockOnEditIntegration}
        onDeleteIntegration={mockOnDeleteIntegration}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('Active')).toBeInTheDocument()
    })

    expect(screen.getByText('Active')).toBeInTheDocument()
    expect(screen.getByText('Error')).toBeInTheDocument()
  })

  it('should display integration configuration details', async () => {
    render(
      <IntegrationManagement
        onEditIntegration={mockOnEditIntegration}
        onDeleteIntegration={mockOnDeleteIntegration}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('30 min')).toBeInTheDocument()
    })

    // Check sync intervals
    expect(screen.getByText('30 min')).toBeInTheDocument()
    expect(screen.getByText('60 min')).toBeInTheDocument()

    // Check two-way sync status
    expect(screen.getAllByText('Enabled')).toHaveLength(1)
    expect(screen.getAllByText('Disabled')).toHaveLength(1)

    // Check tasks processed
    expect(screen.getByText('15')).toBeInTheDocument() // Active integration
    expect(screen.getByText('0')).toBeInTheDocument() // Error integration
  })

  it('should show sync errors for failed integrations', async () => {
    render(
      <IntegrationManagement
        onEditIntegration={mockOnEditIntegration}
        onDeleteIntegration={mockOnDeleteIntegration}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('1 sync error(s)')).toBeInTheDocument()
    })

    expect(screen.getByText('Invalid credentials')).toBeInTheDocument()
  })

  it('should trigger manual sync when sync button is clicked', async () => {
    const user = userEvent.setup()
    
    mockIntegrationService.syncIntegration.mockResolvedValue({
      success: true,
      tasksProcessed: 10,
      tasksCreated: 3,
      tasksUpdated: 5,
      tasksDeleted: 2,
      errors: [],
      conflicts: [],
      duration: 1500
    })

    render(
      <IntegrationManagement
        onEditIntegration={mockOnEditIntegration}
        onDeleteIntegration={mockOnDeleteIntegration}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('My Asana Integration')).toBeInTheDocument()
    })

    // Click on the dropdown menu for the first integration
    const dropdownButtons = screen.getAllByRole('button', { name: '' })
    await user.click(dropdownButtons[0])

    // Click sync now
    const syncButton = screen.getByText('Sync Now')
    await user.click(syncButton)

    await waitFor(() => {
      expect(mockIntegrationService.syncIntegration).toHaveBeenCalledWith(
        'workspace-1',
        'integration-1'
      )
    })
  })

  it('should test connection when test connection button is clicked', async () => {
    const user = userEvent.setup()
    
    mockIntegrationService.testConnection.mockResolvedValue({
      success: true,
      message: 'Connection successful'
    })

    render(
      <IntegrationManagement
        onEditIntegration={mockOnEditIntegration}
        onDeleteIntegration={mockOnDeleteIntegration}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('My Asana Integration')).toBeInTheDocument()
    })

    // Click on the dropdown menu
    const dropdownButtons = screen.getAllByRole('button', { name: '' })
    await user.click(dropdownButtons[0])

    // Click test connection
    const testButton = screen.getByText('Test Connection')
    await user.click(testButton)

    await waitFor(() => {
      expect(mockIntegrationService.testConnection).toHaveBeenCalledWith(
        'workspace-1',
        'integration-1'
      )
    })
  })

  it('should call onEditIntegration when settings button is clicked', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationManagement
        onEditIntegration={mockOnEditIntegration}
        onDeleteIntegration={mockOnDeleteIntegration}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('My Asana Integration')).toBeInTheDocument()
    })

    // Click on the dropdown menu
    const dropdownButtons = screen.getAllByRole('button', { name: '' })
    await user.click(dropdownButtons[0])

    // Click settings
    const settingsButton = screen.getByText('Settings')
    await user.click(settingsButton)

    expect(mockOnEditIntegration).toHaveBeenCalledWith(mockIntegrations[0])
  })

  it('should call onDeleteIntegration when delete button is clicked', async () => {
    const user = userEvent.setup()

    render(
      <IntegrationManagement
        onEditIntegration={mockOnEditIntegration}
        onDeleteIntegration={mockOnDeleteIntegration}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('My Asana Integration')).toBeInTheDocument()
    })

    // Click on the dropdown menu
    const dropdownButtons = screen.getAllByRole('button', { name: '' })
    await user.click(dropdownButtons[0])

    // Click delete
    const deleteButton = screen.getByText('Delete')
    await user.click(deleteButton)

    expect(mockOnDeleteIntegration).toHaveBeenCalledWith(mockIntegrations[0])
  })

  it('should show syncing state during manual sync', async () => {
    const user = userEvent.setup()
    
    // Mock a slow sync operation
    mockIntegrationService.syncIntegration.mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve({
        success: true,
        tasksProcessed: 10,
        tasksCreated: 3,
        tasksUpdated: 5,
        tasksDeleted: 2,
        errors: [],
        conflicts: [],
        duration: 1500
      }), 1000))
    )

    render(
      <IntegrationManagement
        onEditIntegration={mockOnEditIntegration}
        onDeleteIntegration={mockOnDeleteIntegration}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('My Asana Integration')).toBeInTheDocument()
    })

    // Click on the dropdown menu
    const dropdownButtons = screen.getAllByRole('button', { name: '' })
    await user.click(dropdownButtons[0])

    // Click sync now
    const syncButton = screen.getByText('Sync Now')
    await user.click(syncButton)

    // Should show syncing state
    await waitFor(() => {
      expect(screen.getByText('Syncing...')).toBeInTheDocument()
    })
  })

  it('should handle sync failure gracefully', async () => {
    const user = userEvent.setup()
    
    mockIntegrationService.syncIntegration.mockRejectedValue(new Error('Sync failed'))

    render(
      <IntegrationManagement
        onEditIntegration={mockOnEditIntegration}
        onDeleteIntegration={mockOnDeleteIntegration}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('My Asana Integration')).toBeInTheDocument()
    })

    // Click on the dropdown menu
    const dropdownButtons = screen.getAllByRole('button', { name: '' })
    await user.click(dropdownButtons[0])

    // Click sync now
    const syncButton = screen.getByText('Sync Now')
    await user.click(syncButton)

    await waitFor(() => {
      expect(mockIntegrationService.syncIntegration).toHaveBeenCalled()
    })

    // Should not be in syncing state after error
    expect(screen.queryByText('Syncing...')).not.toBeInTheDocument()
  })
})