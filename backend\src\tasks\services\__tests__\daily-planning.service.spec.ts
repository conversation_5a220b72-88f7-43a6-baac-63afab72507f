import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { DailyPlanningService } from '../daily-planning.service';
import { DatabaseService } from '../../../database/database.service';

describe('DailyPlanningService', () => {
  let service: DailyPlanningService;
  let databaseService: jest.Mocked<DatabaseService>;

  const mockWorkspaceId = 'workspace-1';
  const mockUserId = 'user-1';
  const mockPlanDate = '2024-01-15';
  const mockTaskId1 = 'task-1';
  const mockTaskId2 = 'task-2';

  const mockTask1 = {
    id: mockTaskId1,
    workspaceId: mockWorkspaceId,
    title: 'Test Task 1',
    description: 'Test description 1',
    status: 'todo',
    priority: 'high',
    priorityScore: 85.5,
    dueDate: new Date('2024-01-15'),
    tags: ['urgent'],
    projectName: 'Test Project',
    sourceUrl: 'https://example.com/task1',
  };

  const mockTask2 = {
    id: mockTaskId2,
    workspaceId: mockWorkspaceId,
    title: 'Test Task 2',
    description: 'Test description 2',
    status: 'todo',
    priority: 'medium',
    priorityScore: 65.0,
    dueDate: new Date('2024-01-16'),
    tags: ['feature'],
    projectName: 'Test Project',
    sourceUrl: 'https://example.com/task2',
  };

  const mockDailyPlan = {
    id: 'daily-plan-1',
    workspaceId: mockWorkspaceId,
    userId: mockUserId,
    planDate: new Date(mockPlanDate),
    totalEstimatedMinutes: 180,
    totalCompletedMinutes: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockDailyPlanTask1 = {
    id: 'daily-plan-task-1',
    dailyPlanId: 'daily-plan-1',
    taskId: mockTaskId1,
    estimatedMinutes: 120,
    actualMinutes: null,
    orderIndex: 0,
    completedAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    task: mockTask1,
  };

  const mockDailyPlanTask2 = {
    id: 'daily-plan-task-2',
    dailyPlanId: 'daily-plan-1',
    taskId: mockTaskId2,
    estimatedMinutes: 60,
    actualMinutes: null,
    orderIndex: 1,
    completedAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    task: mockTask2,
  };

  beforeEach(async () => {
    const mockDatabaseService = {
      task: {
        findMany: jest.fn(),
        update: jest.fn(),
      },
      dailyPlan: {
        upsert: jest.fn(),
        findUnique: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
        findMany: jest.fn(),
      },
      dailyPlanTask: {
        deleteMany: jest.fn(),
        create: jest.fn(),
        findUnique: jest.fn(),
        update: jest.fn(),
        findMany: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DailyPlanningService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<DailyPlanningService>(DailyPlanningService);
    databaseService = module.get(DatabaseService);
  });

  describe('createOrUpdateDailyPlan', () => {
    const createDailyPlanDto = {
      planDate: mockPlanDate,
      tasks: [
        { taskId: mockTaskId1, estimatedMinutes: 120, orderIndex: 0 },
        { taskId: mockTaskId2, estimatedMinutes: 60, orderIndex: 1 },
      ],
    };

    it('should create a new daily plan successfully', async () => {
      // Mock task verification
      databaseService.task.findMany.mockResolvedValue([mockTask1, mockTask2]);
      
      // Mock daily plan creation
      databaseService.dailyPlan.upsert.mockResolvedValue(mockDailyPlan);
      
      // Mock daily plan task deletion and creation
      databaseService.dailyPlanTask.deleteMany.mockResolvedValue({ count: 0 });
      databaseService.dailyPlanTask.create
        .mockResolvedValueOnce(mockDailyPlanTask1)
        .mockResolvedValueOnce(mockDailyPlanTask2);

      // Mock getDailyPlan call
      const mockDailyPlanWithTasks = {
        ...mockDailyPlan,
        dailyPlanTasks: [mockDailyPlanTask1, mockDailyPlanTask2],
      };
      databaseService.dailyPlan.findUnique.mockResolvedValue(mockDailyPlanWithTasks);

      const result = await service.createOrUpdateDailyPlan(
        mockWorkspaceId,
        mockUserId,
        createDailyPlanDto,
      );

      expect(databaseService.task.findMany).toHaveBeenCalledWith({
        where: {
          id: { in: [mockTaskId1, mockTaskId2] },
          workspaceId: mockWorkspaceId,
        },
      });

      expect(databaseService.dailyPlan.upsert).toHaveBeenCalledWith({
        where: {
          workspaceId_userId_planDate: {
            workspaceId: mockWorkspaceId,
            userId: mockUserId,
            planDate: new Date(mockPlanDate),
          },
        },
        create: {
          workspaceId: mockWorkspaceId,
          userId: mockUserId,
          planDate: new Date(mockPlanDate),
          totalEstimatedMinutes: 180,
          totalCompletedMinutes: 0,
        },
        update: {
          totalEstimatedMinutes: 180,
        },
      });

      expect(result).toEqual({
        id: mockDailyPlan.id,
        workspaceId: mockWorkspaceId,
        userId: mockUserId,
        planDate: new Date(mockPlanDate),
        totalEstimatedMinutes: 180,
        totalCompletedMinutes: 0,
        completionProgress: 0,
        exceedsRecommendedTime: false,
        tasks: expect.arrayContaining([
          expect.objectContaining({
            taskId: mockTaskId1,
            estimatedMinutes: 120,
            orderIndex: 0,
          }),
          expect.objectContaining({
            taskId: mockTaskId2,
            estimatedMinutes: 60,
            orderIndex: 1,
          }),
        ]),
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });
    });

    it('should throw BadRequestException when tasks do not belong to workspace', async () => {
      // Mock task verification - only return one task
      databaseService.task.findMany.mockResolvedValue([mockTask1]);

      await expect(
        service.createOrUpdateDailyPlan(mockWorkspaceId, mockUserId, createDailyPlanDto),
      ).rejects.toThrow(BadRequestException);
    });

    it('should warn when daily plan exceeds 8 hours', async () => {
      const longPlanDto = {
        planDate: mockPlanDate,
        tasks: [
          { taskId: mockTaskId1, estimatedMinutes: 300, orderIndex: 0 }, // 5 hours
          { taskId: mockTaskId2, estimatedMinutes: 240, orderIndex: 1 }, // 4 hours
        ],
      };

      databaseService.task.findMany.mockResolvedValue([mockTask1, mockTask2]);
      
      const longDailyPlan = { ...mockDailyPlan, totalEstimatedMinutes: 540 };
      databaseService.dailyPlan.upsert.mockResolvedValue(longDailyPlan);
      
      databaseService.dailyPlanTask.deleteMany.mockResolvedValue({ count: 0 });
      databaseService.dailyPlanTask.create
        .mockResolvedValueOnce({ ...mockDailyPlanTask1, estimatedMinutes: 300 })
        .mockResolvedValueOnce({ ...mockDailyPlanTask2, estimatedMinutes: 240 });

      const mockLongPlanWithTasks = {
        ...longDailyPlan,
        dailyPlanTasks: [
          { ...mockDailyPlanTask1, estimatedMinutes: 300 },
          { ...mockDailyPlanTask2, estimatedMinutes: 240 },
        ],
      };
      databaseService.dailyPlan.findUnique.mockResolvedValue(mockLongPlanWithTasks);

      const result = await service.createOrUpdateDailyPlan(
        mockWorkspaceId,
        mockUserId,
        longPlanDto,
      );

      expect(result.exceedsRecommendedTime).toBe(true);
      expect(result.totalEstimatedMinutes).toBe(540);
    });
  });

  describe('getDailyPlan', () => {
    it('should return daily plan when it exists', async () => {
      const mockDailyPlanWithTasks = {
        ...mockDailyPlan,
        dailyPlanTasks: [mockDailyPlanTask1, mockDailyPlanTask2],
      };

      databaseService.dailyPlan.findUnique.mockResolvedValue(mockDailyPlanWithTasks);

      const result = await service.getDailyPlan(mockWorkspaceId, mockUserId, mockPlanDate);

      expect(databaseService.dailyPlan.findUnique).toHaveBeenCalledWith({
        where: {
          workspaceId_userId_planDate: {
            workspaceId: mockWorkspaceId,
            userId: mockUserId,
            planDate: new Date(mockPlanDate),
          },
        },
        include: {
          dailyPlanTasks: {
            include: {
              task: {
                select: {
                  id: true,
                  title: true,
                  description: true,
                  status: true,
                  priority: true,
                  priorityScore: true,
                  dueDate: true,
                  tags: true,
                  projectName: true,
                  sourceUrl: true,
                },
              },
            },
            orderBy: { orderIndex: 'asc' },
          },
        },
      });

      expect(result).toEqual({
        id: mockDailyPlan.id,
        workspaceId: mockWorkspaceId,
        userId: mockUserId,
        planDate: new Date(mockPlanDate),
        totalEstimatedMinutes: 180,
        totalCompletedMinutes: 0,
        completionProgress: 0,
        exceedsRecommendedTime: false,
        tasks: expect.arrayContaining([
          expect.objectContaining({
            taskId: mockTaskId1,
            estimatedMinutes: 120,
          }),
          expect.objectContaining({
            taskId: mockTaskId2,
            estimatedMinutes: 60,
          }),
        ]),
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });
    });

    it('should return null when daily plan does not exist', async () => {
      databaseService.dailyPlan.findUnique.mockResolvedValue(null);

      const result = await service.getDailyPlan(mockWorkspaceId, mockUserId, mockPlanDate);

      expect(result).toBeNull();
    });
  });

  describe('completeDailyPlanTask', () => {
    it('should mark task as complete and update task status', async () => {
      const completeDailyPlanTaskDto = { actualMinutes: 90 };

      databaseService.dailyPlan.findUnique.mockResolvedValue(mockDailyPlan);
      databaseService.dailyPlanTask.findUnique.mockResolvedValue(mockDailyPlanTask1);
      databaseService.dailyPlanTask.update.mockResolvedValue({
        ...mockDailyPlanTask1,
        completedAt: new Date(),
        actualMinutes: 90,
      });
      databaseService.task.update.mockResolvedValue({
        ...mockTask1,
        status: 'done',
        syncStatus: 'pending',
      });
      databaseService.dailyPlanTask.findMany.mockResolvedValue([
        { ...mockDailyPlanTask1, completedAt: new Date(), actualMinutes: 90 },
      ]);
      databaseService.dailyPlan.update.mockResolvedValue({
        ...mockDailyPlan,
        totalCompletedMinutes: 90,
      });

      // Mock getDailyPlan call
      const mockCompletedPlan = {
        ...mockDailyPlan,
        totalCompletedMinutes: 90,
        dailyPlanTasks: [
          { ...mockDailyPlanTask1, completedAt: new Date(), actualMinutes: 90 },
          mockDailyPlanTask2,
        ],
      };
      databaseService.dailyPlan.findUnique.mockResolvedValue(mockCompletedPlan);

      const result = await service.completeDailyPlanTask(
        mockWorkspaceId,
        mockUserId,
        mockPlanDate,
        mockTaskId1,
        completeDailyPlanTaskDto,
      );

      expect(databaseService.dailyPlanTask.update).toHaveBeenCalledWith({
        where: { id: mockDailyPlanTask1.id },
        data: {
          completedAt: expect.any(Date),
          actualMinutes: 90,
        },
      });

      expect(databaseService.task.update).toHaveBeenCalledWith({
        where: { id: mockTaskId1 },
        data: {
          status: 'done',
          syncStatus: 'pending',
        },
      });

      expect(result.totalCompletedMinutes).toBe(90);
    });

    it('should throw NotFoundException when daily plan does not exist', async () => {
      databaseService.dailyPlan.findUnique.mockResolvedValue(null);

      await expect(
        service.completeDailyPlanTask(
          mockWorkspaceId,
          mockUserId,
          mockPlanDate,
          mockTaskId1,
          {},
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when task is not in daily plan', async () => {
      databaseService.dailyPlan.findUnique.mockResolvedValue(mockDailyPlan);
      databaseService.dailyPlanTask.findUnique.mockResolvedValue(null);

      await expect(
        service.completeDailyPlanTask(
          mockWorkspaceId,
          mockUserId,
          mockPlanDate,
          mockTaskId1,
          {},
        ),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateDailyPlan', () => {
    it('should update daily plan with new task order', async () => {
      const updateDailyPlanDto = {
        tasks: [
          { taskId: mockTaskId2, estimatedMinutes: 60, orderIndex: 0 }, // Reordered
          { taskId: mockTaskId1, estimatedMinutes: 120, orderIndex: 1 },
        ],
      };

      databaseService.dailyPlan.findUnique.mockResolvedValue(mockDailyPlan);
      databaseService.task.findMany.mockResolvedValue([mockTask1, mockTask2]);
      databaseService.dailyPlan.update.mockResolvedValue(mockDailyPlan);
      databaseService.dailyPlanTask.deleteMany.mockResolvedValue({ count: 2 });
      databaseService.dailyPlanTask.create
        .mockResolvedValueOnce({ ...mockDailyPlanTask2, orderIndex: 0 })
        .mockResolvedValueOnce({ ...mockDailyPlanTask1, orderIndex: 1 });

      // Mock getDailyPlan call
      const mockReorderedPlan = {
        ...mockDailyPlan,
        dailyPlanTasks: [
          { ...mockDailyPlanTask2, orderIndex: 0 },
          { ...mockDailyPlanTask1, orderIndex: 1 },
        ],
      };
      databaseService.dailyPlan.findUnique.mockResolvedValue(mockReorderedPlan);

      const result = await service.updateDailyPlan(
        mockWorkspaceId,
        mockUserId,
        mockPlanDate,
        updateDailyPlanDto,
      );

      expect(databaseService.dailyPlanTask.deleteMany).toHaveBeenCalledWith({
        where: { dailyPlanId: mockDailyPlan.id },
      });

      expect(result.tasks[0].taskId).toBe(mockTaskId2);
      expect(result.tasks[1].taskId).toBe(mockTaskId1);
    });

    it('should throw NotFoundException when daily plan does not exist', async () => {
      databaseService.dailyPlan.findUnique.mockResolvedValue(null);

      await expect(
        service.updateDailyPlan(mockWorkspaceId, mockUserId, mockPlanDate, {}),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteDailyPlan', () => {
    it('should delete daily plan successfully', async () => {
      databaseService.dailyPlan.findUnique.mockResolvedValue(mockDailyPlan);
      databaseService.dailyPlan.delete.mockResolvedValue(mockDailyPlan);

      await service.deleteDailyPlan(mockWorkspaceId, mockUserId, mockPlanDate);

      expect(databaseService.dailyPlan.delete).toHaveBeenCalledWith({
        where: { id: mockDailyPlan.id },
      });
    });

    it('should throw NotFoundException when daily plan does not exist', async () => {
      databaseService.dailyPlan.findUnique.mockResolvedValue(null);

      await expect(
        service.deleteDailyPlan(mockWorkspaceId, mockUserId, mockPlanDate),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('getDailyPlansForRange', () => {
    it('should return daily plans for date range', async () => {
      const startDate = '2024-01-15';
      const endDate = '2024-01-17';

      const mockDailyPlans = [
        {
          ...mockDailyPlan,
          planDate: new Date('2024-01-15'),
          dailyPlanTasks: [mockDailyPlanTask1],
        },
        {
          ...mockDailyPlan,
          id: 'daily-plan-2',
          planDate: new Date('2024-01-16'),
          dailyPlanTasks: [mockDailyPlanTask2],
        },
      ];

      databaseService.dailyPlan.findMany.mockResolvedValue(mockDailyPlans);

      const result = await service.getDailyPlansForRange(
        mockWorkspaceId,
        mockUserId,
        startDate,
        endDate,
      );

      expect(databaseService.dailyPlan.findMany).toHaveBeenCalledWith({
        where: {
          workspaceId: mockWorkspaceId,
          userId: mockUserId,
          planDate: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        },
        include: {
          dailyPlanTasks: {
            include: {
              task: {
                select: {
                  id: true,
                  title: true,
                  description: true,
                  status: true,
                  priority: true,
                  priorityScore: true,
                  dueDate: true,
                  tags: true,
                  projectName: true,
                  sourceUrl: true,
                },
              },
            },
            orderBy: { orderIndex: 'asc' },
          },
        },
        orderBy: { planDate: 'asc' },
      });

      expect(result).toHaveLength(2);
      expect(result[0].planDate).toEqual(new Date('2024-01-15'));
      expect(result[1].planDate).toEqual(new Date('2024-01-16'));
    });
  });
});